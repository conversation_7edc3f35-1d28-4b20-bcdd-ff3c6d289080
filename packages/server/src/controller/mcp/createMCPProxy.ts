import { type App } from '../../index'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js'
import { isJSONRPCRequest } from '@modelcontextprotocol/sdk/types.js'
import { type Request } from 'express'
import { SSEClientTransport, SseError } from '@modelcontextprotocol/sdk/client/sse.js'
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js'
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js'
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js'
import { randomUUID } from 'node:crypto'
import { MCP } from '../../database/entities/MCP'
import { opsLogger } from '../../utils/biliLogger'

// 头信息的类型
type HeadersInit = [string, string][] | Record<string, string> | Headers

// 需要从请求中传递到SSE客户端的头信息
const SSE_HEADERS_PASSTHROUGH = ['authorization']
// 需要从请求中传递到HTTP客户端的头信息
const STREAMABLE_HTTP_HEADERS_PASSTHROUGH = ['authorization', 'mcp-session-id', 'last-event-id']

// 是否是开发环境
const IS_DEV = (process.env.NODE_ENV || 'local') === 'local'

// 如果是开发环境，将日志输出到控制台
if (IS_DEV) {
    opsLogger.info = console.log
    opsLogger.error = console.error
}

// 处理客户端错误
function onClientError(error: Error) {
    opsLogger.error('Error from client:', error)
}

// 处理服务器错误
function onServerError(error: Error) {
    opsLogger.error('Error from server:', error)
}

// 创建MCP代理
export default function mcpProxy({ transportToClient, transportToServer }: { transportToClient: Transport; transportToServer: Transport }) {
    // 客户端连接是否关闭
    let transportToClientClosed = false
    // 服务器连接是否关闭
    let transportToServerClosed = false

    // 处理客户端消息
    transportToClient.onmessage = (message) => {
        transportToServer.send(message).catch((error) => {
            // Send error response back to client if it was a request (has id) and connection is still open
            if (isJSONRPCRequest(message) && !transportToClientClosed) {
                const errorResponse = {
                    jsonrpc: '2.0' as const,
                    id: message.id,
                    error: {
                        code: -32001,
                        message: error.message,
                        data: error
                    }
                }
                transportToClient.send(errorResponse).catch(onClientError)
            }
        })
    }

    // 处理服务器消息
    transportToServer.onmessage = (message) => {
        transportToClient.send(message).catch(onClientError)
    }

    // 处理客户端关闭
    transportToClient.onclose = () => {
        if (transportToServerClosed) {
            return
        }

        transportToClientClosed = true
        transportToServer.close().catch(onServerError)
    }

    // 处理服务器关闭
    transportToServer.onclose = () => {
        if (transportToClientClosed) {
            return
        }
        transportToServerClosed = true
        transportToClient.close().catch(onClientError)
    }

    // 处理客户端错误
    transportToClient.onerror = onClientError
    // 处理服务器错误
    transportToServer.onerror = onServerError

    opsLogger.info('Set up MCP proxy')
}

export function createMCPProxy({ app, AppDataSource }: App) {
    const webAppTransports: Map<string, Transport> = new Map<string, Transport>() // Transports by sessionId

    function prepareHeaders(req: Request, transportType: 'sse' | 'streamable-http', customHeaderKeys: string[] = []) {
        const headersToCopy =
            transportType === 'sse'
                ? [...SSE_HEADERS_PASSTHROUGH, ...customHeaderKeys]
                : [...STREAMABLE_HTTP_HEADERS_PASSTHROUGH, ...customHeaderKeys]

        const headers: HeadersInit = {
            Accept: transportType === 'sse' ? 'text/event-stream' : 'text/event-stream, application/json'
        }

        for (const key of headersToCopy) {
            if (req.headers[key]) {
                const value = req.headers[key]
                headers[key] = Array.isArray(value) ? value[value.length - 1] : value
            }
        }

        return headers
    }

    const createTransport = async (req: Request, url: string, transportType: 'sse' | 'streamable-http'): Promise<Transport> => {
        opsLogger.info('Query parameters:', req.query)

        const customHeaderKeys: string[] = []

        if (req.query.__MCP__CUSTOM__HEADER_KEYS__ != null) {
            customHeaderKeys.push(...(req.query.__MCP__CUSTOM__HEADER_KEYS__ as string).split(','))
        }

        const _url = new URL(url)

        Object.keys(req.query).forEach((key) => {
            if (key === '__MCP__CUSTOM__HEADER_KEYS__') return
            _url.searchParams.append(key, req.query[key] as string)
        })

        url = _url.href

        if (transportType === 'sse') {
            const headers: HeadersInit = prepareHeaders(req, transportType, customHeaderKeys)

            opsLogger.info(`SSE transport: url=${url}, headers=${Object.keys(headers)}`)

            const transport = new SSEClientTransport(new URL(url), {
                eventSourceInit: {
                    fetch: (url, init) => fetch(url, { ...init, headers })
                },
                requestInit: {
                    headers
                }
            })
            await transport.start()

            opsLogger.info('Connected to SSE transport')
            return transport
        } else if (transportType === 'streamable-http') {
            const headers: HeadersInit = prepareHeaders(req, transportType, customHeaderKeys)

            const transport = new StreamableHTTPClientTransport(new URL(url), {
                requestInit: {
                    headers
                }
            })
            await transport.start()
            opsLogger.info('Connected to Streamable HTTP transport')
            return transport
        } else {
            opsLogger.error(`Invalid transport type: ${transportType}`)
            throw new Error('Invalid transport type specified')
        }
    }

    let backingServerTransport: Transport | undefined

    app.get('/api/mcp/external/:id', async (req, res) => {
        const sessionId = req.headers['mcp-session-id'] as string
        opsLogger.info(`Received GET message for sessionId ${sessionId}`)
        try {
            const transport = webAppTransports.get(sessionId) as StreamableHTTPServerTransport
            if (!transport) {
                res.status(404).end('Session not found')
                return
            } else {
                await transport.handleRequest(req, res)
            }
        } catch (error) {
            opsLogger.error('Error in /mcp route:', error)
            res.status(500).json(error)
        }
    })

    app.post('/api/mcp/external/:id', async (req, res) => {
        const sessionId = req.headers['mcp-session-id'] as string | undefined
        opsLogger.info(`Received POST message for sessionId ${sessionId}`)
        if (!sessionId) {
            const mcp = await AppDataSource.getRepository(MCP).findOne({
                where: {
                    id: req.params.id as string
                }
            })

            try {
                opsLogger.info('New streamable-http connection')
                try {
                    const { streamable } = JSON.parse(mcp?.source as string)
                    backingServerTransport = await createTransport(req, streamable, 'streamable-http')
                } catch (error) {
                    if (error instanceof SseError && error.code === 401) {
                        opsLogger.error('Received 401 Unauthorized from MCP server:', error.message)
                        res.status(401).json(error)
                        return
                    }

                    throw error
                }

                opsLogger.info('Connected MCP client to backing server transport')

                const webAppTransport = new StreamableHTTPServerTransport({
                    sessionIdGenerator: randomUUID,
                    onsessioninitialized: (sessionId) => {
                        webAppTransports.set(sessionId, webAppTransport)
                        opsLogger.info('Created streamable web app transport ' + sessionId)
                    }
                })

                await webAppTransport.start()

                mcpProxy({
                    transportToClient: webAppTransport,
                    transportToServer: backingServerTransport
                })

                await (webAppTransport as StreamableHTTPServerTransport).handleRequest(req, res, req.body)
            } catch (error) {
                opsLogger.error('Error in /mcp POST route:', error)
                res.status(500).json(error)
            }
        } else {
            try {
                const transport = webAppTransports.get(sessionId) as StreamableHTTPServerTransport
                if (!transport) {
                    res.status(404).end('Transport not found for sessionId ' + sessionId)
                } else {
                    await (transport as StreamableHTTPServerTransport).handleRequest(req, res)
                }
            } catch (error) {
                opsLogger.error('Error in /mcp route:', error)
                res.status(500).json(error)
            }
        }
    })

    app.get('/api/mcp/external/sse/:id', async (req, res) => {
        try {
            opsLogger.info('New SSE connection. NOTE: The sse transport is deprecated and has been replaced by streamable-http')

            const mcp = await AppDataSource.getRepository(MCP).findOne({
                where: {
                    id: req.params.id as string
                }
            })

            const { sse } = JSON.parse(mcp?.source as string)

            try {
                backingServerTransport = await createTransport(req, sse, 'sse')
            } catch (error) {
                if (error instanceof SseError && error.code === 401) {
                    opsLogger.error('Received 401 Unauthorized from MCP server:', error.message)
                    res.status(401).json(error)
                    return
                }

                throw error
            }

            opsLogger.info('Connected MCP client to backing server transport')

            const webAppTransport = new SSEServerTransport('/api/mcp/external/__sse/message', res)
            webAppTransports.set(webAppTransport.sessionId, webAppTransport)
            opsLogger.info('Created web app transport')

            await webAppTransport.start()

            mcpProxy({
                transportToClient: webAppTransport,
                transportToServer: backingServerTransport
            })
        } catch (error) {
            opsLogger.error('Error in /sse route:', error)
            res.status(500).json(error)
        }
    })

    app.post('/api/mcp/external/__sse/message', async (req, res) => {
        try {
            const sessionId = req.query.sessionId
            opsLogger.info(`Received message for sessionId ${sessionId}`)

            const transport = webAppTransports.get(sessionId as string) as SSEServerTransport
            if (!transport) {
                res.status(404).end('Session not found')
                return
            }
            await transport.handlePostMessage(req, res)
        } catch (error) {
            opsLogger.error('Error in /message route:', error)
            res.status(500).json(error)
        }
    })
}
