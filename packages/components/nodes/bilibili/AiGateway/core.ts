import {
    ChatOpenAI,
    ChatOpenAICallOptions,
    ChatOpenAIFields,
    OpenAIChatInput,
    OpenAICoreRequestOptions,
    wrapOpenAIClientError
} from '@langchain/openai'
import crypto from 'crypto'
import { BaseLLMParams } from '@langchain/core/language_models/llms'
import { opsLogger } from '../../../src/biliLogger'
import { OpenAI as OpenAIClient } from 'openai'

export interface ChatAiGatewayCallOptions extends ChatOpenAICallOptions {
    headers?: Record<string, string>
}

export interface ChatAiGatewayInput extends ChatOpenAIFields {
    /**
     * The API key to use for authentication.
     */
    secretKey: string
    accessKey: string
    /**
     * The base URL for the API.
     */
    source: string
    modelName: string
    basePath?: string
    /**
     * Up to 4 sequences where the API will stop generating further tokens. The
     * returned text will not contain the stop sequence.
     * Alias for `stopSequences`
     */
    stop?: Array<string>
    /**
     * Up to 4 sequences where the API will stop generating further tokens. The
     * returned text will not contain the stop sequence.
     */
    stopSequences?: Array<string>
    /**
     * Whether or not to stream responses.
     */
    streaming?: boolean
    /**
     * The temperature to use for sampling.
     */
    temperature?: number
    /**
     * The maximum number of tokens that the model can process in a single response.
     * This limits ensures computational efficiency and resource management.
     */
    maxTokens?: number
}

export class ChatAiGateway extends ChatOpenAI<ChatAiGatewayCallOptions> {
    static lc_name() {
        return 'ChatAiGateway'
    }

    _llmType() {
        return 'aigateway'
    }

    get lc_secrets(): { [key: string]: string } | undefined {
        return {
            apiKey: 'AI_GATEWAY_API_KEY'
        }
    }

    lc_serializable = true

    lc_namespace = ['langchain', 'chat_models', 'aigateway']

    source: string

    apiModel: string

    secretKey: string

    accessKey: string

    apiKey: string

    basePath: string

    constructor(fields?: Partial<ChatAiGatewayInput>) {
        const secretKey = fields?.secretKey
        const accessKey = fields?.accessKey

        if (!secretKey || !accessKey) {
            throw new Error(`AiGateway API key not found.`)
        }

        const source = fields?.source || 'openai'
        const apiModel = fields?.modelName || 'gpt-4o'

        const apiKey = `sk-`

        const obj: Partial<OpenAIChatInput> & BaseLLMParams & { openAIApiKey?: string } = {
            apiKey,
            temperature: fields?.temperature,
            streaming: fields?.streaming ?? false,
            model: 'gpt-4'
        }

        const basePath = fields?.basePath || 'http://ai-gateway.bilibili.co/v1'

        super({
            ...obj,
            configuration: {
                baseURL: basePath,
                defaultHeaders: { Authorization: apiKey },
                defaultQuery: { model: apiModel, source },
                ...fields?.configuration
            }
        })

        this.source = source
        this.apiModel = apiModel
        this.model = 'gpt-4' // 由于tiktoken与aiGateway中的modelName不一致，所以这里都使用gpt-4o代替
        this.secretKey = secretKey
        this.accessKey = accessKey
        this.apiKey = apiKey
        this.basePath = basePath
    }

    /**
     * 每次运行时动态生产apikey
     */
    protected override _getClientOptions(options: OpenAICoreRequestOptions | undefined): OpenAICoreRequestOptions {
        const date = new Date().toUTCString()
        const signature = this._generateSignature('/v1/chat/completions', this.source, this.apiModel, this.secretKey, this.accessKey, date)
        this.apiKey = `hmac-auth-v1#${this.accessKey}#${signature}#hmac-sha256#${date}`
        return super._getClientOptions({
            ...options,
            headers: { ...options?.headers, Authorization: this.apiKey }
        })
    }

    async completionWithRetry(
        request: OpenAIClient.Chat.ChatCompletionCreateParamsStreaming,
        options?: OpenAICoreRequestOptions
    ): Promise<AsyncIterable<OpenAIClient.Chat.Completions.ChatCompletionChunk>>

    // eslint-disable-next-line no-dupe-class-members
    async completionWithRetry(
        request: OpenAIClient.Chat.ChatCompletionCreateParamsNonStreaming,
        options?: OpenAICoreRequestOptions
    ): Promise<OpenAIClient.Chat.Completions.ChatCompletion>

    // eslint-disable-next-line no-dupe-class-members
    async completionWithRetry(
        request: OpenAIClient.Chat.ChatCompletionCreateParamsStreaming | OpenAIClient.Chat.ChatCompletionCreateParamsNonStreaming,
        options?: OpenAICoreRequestOptions
    ): Promise<AsyncIterable<OpenAIClient.Chat.Completions.ChatCompletionChunk> | OpenAIClient.Chat.Completions.ChatCompletion> {
        const requestOptions = this._getClientOptions(options)
        return this.caller.call(async () => {
            try {
                opsLogger.info('AiGateway', {
                    msg: 'AiGateway Request',
                    request,
                    apikey: this.apiKey
                })

                const res = await this.client.chat.completions.create(request, requestOptions)

                opsLogger.info('AiGateway', {
                    msg: 'AiGateway Success',
                    response: res,
                    request,
                    apikey: this.apiKey
                })

                return res
            } catch (e) {
                console.log('AiGateway Error', e)

                opsLogger.error('AiGateway', {
                    msg: 'AiGateway Error',
                    apikey: this.apiKey,
                    request,
                    error: e
                })

                const error = wrapOpenAIClientError(e)
                throw error
            }
        })
    }

    _generateSignature(path: string, source: string, modelName: string, secretKey: string, accessKey: string, date: string): string {
        const secret = Buffer.from(secretKey, 'utf-8')

        // 构造消息内容，canonical_query_string
        const message = `POST\n${path}\nmodel=${modelName}&source=${source}\n${accessKey}\n${date}\n`

        // 使用 HMAC-SHA256 算法，使用 secretKey 对消息内容进行签名
        const hash = crypto.createHmac('sha256', secret).update(message, 'utf-8').digest()

        // 使用 base64 编码
        return hash.toString('base64')
    }
}
