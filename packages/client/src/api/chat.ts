import { apiV1, apiV0, proxy, type ResponseData } from './client'

export interface ChatflowSpecific {
    id: string
    name: string
    chatbotConfig: string
    flowData: string
    analytic: string
    createdDate: string
    updatedDate: string
    category: string
    isPublic: boolean
    deployed: boolean
    apikeyid: string
    description: string
    creator: string
    avatar: string
    type: string
}
export type messageType = 'apiMessage' | 'userMessage' | 'usermessagewaiting'
export interface ChatMessage {
    chatId: string
    chatType: string
    chatflowid: string
    content: string
    createdDate: string
    fileAnnotations: string
    fileUploads: string
    id: string
    memoryType: string
    mtime: string
    role: string
    sessionId: string
    sourceDocuments: string
    usedTools: any[]
    username?: string
    feedback?: {
        id: string
        chatflowid: string
        chatId: string
        messageId: string
        rating: 'THUMBS_UP' | 'THUMBS_DOWN'
        content: string
    }
    agentReasoning?: any[]
}
export type MessageType = {
    messageId?: string
    message: string
    type: string
    sourceDocuments?: any
    fileAnnotations?: any
    fileUploads?: any[]
    feedback?: {
        id: string
        rating: string
        content?: string
    }
    status?: number
    ctime: string
}
export type ChatStats = {
    totalMessages: number
    totalFeedback: number
    positiveFeedback: number
}
export const getSpecificChatflow = (id: string) => apiV1.get<ChatflowSpecific>(`/v1/chatflows/${id}`)

export const getChatMessage = (
    id: string,
    params: {
        order: string
        chatId?: string
        chatType?: string
        memoryType?: string
        sessionId?: string
        startDate?: Date | number
        endDate?: Date | number
        feedback?: boolean
    }
) =>
    apiV1.get<ChatMessage[]>(`/v1/chatmessage/${id}`, {
        params: params
    })

export const getChatStats = (
    id: string,
    params: {
        chatType?: string
        startDate?: Date | number
        endDate?: Date | number
    }
) =>
    apiV0.get<ResponseData<ChatStats>>(`/v0/stats/${id}`, {
        params: params
    })

export const getToolsInfo = (ids: string[]) => apiV0.post('/v0/tools/ids', { ids })

type IWorkflow = {
    applicant: string
    avatar: string
    businessId: number
    ctime: string
    department: string
    flowTemplateId: number
    id: string
    manager: string
    managerAvatar: string
    msgKey: string
    mtime: string
    nickname: string
    status: number
    title: string
    type: string
}
export const getWorkflow = (sessionId: string) => {
    return apiV0.get<ResponseData<IWorkflow>>(`/v0/workflow?sessionId=${sessionId}`)
}
export interface KvResp {
    list: {
        ctime: string
        id: string
        is_bind: string
        keyword: string
        mtime: string
        name: string
        service: string
        status: string
        template: string
        tree_id: string
        tree_name: string
        tree_path: string
        value: string
    }[]
    total_num: string
}

/**
 * 获取公告
 */
export const getNoticeKv = async () => {
    return await proxy<KvResp>({
        method: 'get',
        url: '//api.live.bilibili.co/xlive/internal/resource/v1/titans/getServiceConfigList',
        params: {
            tree_name: 'live',
            tree_id: 858083,
            keyword: 'notice'
        }
    })
}
