<template>
    <a-dropdown :trigger="['click']" @open-change="handleDropdownVisibleChange">
        <a-button class="history-button">
            <HistoryOutlined class="history-icon" />
            <span class="button-text">历史版本</span>
            <DownOutlined class="arrow-icon" />
        </a-button>

        <template #overlay>
            <div class="history-menu-wrapper">
                <a-menu class="history-menu" :selected-keys="selectedKeys" @click="handleMenuClick" ref="menuRef">
                    <a-menu-item v-if="loading" key="loading" disabled>
                        <div class="loading-content">
                            <a-spin size="small" />
                            <span>加载中...</span>
                        </div>
                    </a-menu-item>
                    <a-menu-item v-else-if="historyList.length === 0" key="empty" disabled>
                        <div class="empty-content">暂无历史记录</div>
                    </a-menu-item>
                    <template v-else>
                        <a-menu-item v-for="record in historyList" :key="record.id">
                            <div
                                class="menu-option"
                                :class="{
                                    'current-option': isCurrentVersion(record.id),
                                    'selected-option': selectedHistoryId === record.id
                                }"
                            >
                                <HistoryOutlined class="option-icon" />
                                <div class="option-content">
                                    <div class="option-title">
                                        {{ formatTime(record.ctime) }}
                                        <span v-if="isCurrentVersion(record.id)" class="current-badge">当前版本</span>
                                        <span class="username-tag">{{ record.username }}</span>
                                    </div>
                                    <div class="option-desc" v-if="record.remark || record.func">
                                        {{ record.remark || truncateText(record.func || '', 40) }}
                                    </div>
                                </div>
                            </div>
                        </a-menu-item>

                        <!-- 加载更多提示 -->
                        <a-menu-item v-if="loadingMore" key="loading-more" disabled>
                            <div class="loading-content">
                                <a-spin size="small" />
                                <span>加载更多...</span>
                            </div>
                        </a-menu-item>

                        <!-- 没有更多数据提示 -->
                        <a-menu-item v-else-if="!hasMore && historyList.length > 0" key="no-more" disabled>
                            <div class="empty-content">没有更多记录了</div>
                        </a-menu-item>
                    </template>
                </a-menu>
            </div>
        </template>
    </a-dropdown>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { getToolHistoryList } from '@/api/tool'
import { message } from 'ant-design-vue'
import { HistoryOutlined, DownOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

interface HistoryRecord {
    id: string
    toolId: string
    schema?: string
    func?: string
    remark?: string
    username: string
    ctime: string
    mtime: string
}

interface Props {
    toolId: string
}

const props = defineProps<Props>()
const emit = defineEmits(['apply'])

const loading = ref(false)
const loadingMore = ref(false)
const historyList = ref<HistoryRecord[]>([])
const selectedHistoryId = ref<string | undefined>(undefined)
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const menuRef = ref()

// 判断是否为当前版本（第一条记录）
const isCurrentVersion = (recordId: string) => {
    if (historyList.value.length === 0) return false
    const firstRecord = historyList.value[0]
    return firstRecord && firstRecord.id === recordId && selectedHistoryId.value === undefined
}

// 计算选中的菜单项
const selectedKeys = computed(() => {
    if (selectedHistoryId.value) {
        return [selectedHistoryId.value]
    }
    // 如果没有选中特定历史记录，默认选中第一条（当前版本）
    if (historyList.value.length > 0) {
        return [historyList.value[0].id]
    }
    return []
})

const formatTime = (time: string) => {
    return dayjs(time).format('MM-DD HH:mm')
}

const truncateText = (text: string, maxLength: number) => {
    if (!text) return ''
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
}

const fetchHistoryList = async (isLoadMore = false) => {
    if (!props.toolId) return

    if (isLoadMore) {
        if (!hasMore.value || loadingMore.value) return
        loadingMore.value = true
        // 先递增页码，再发请求
        currentPage.value++
    } else {
        loading.value = true
        currentPage.value = 1
        hasMore.value = true
    }

    try {
        const response = await getToolHistoryList({
            toolId: props.toolId,
            page: currentPage.value,
            pageSize: pageSize.value
        })

        if (response.code === 0) {
            const { rows, count } = response.data

            if (isLoadMore) {
                historyList.value = [...historyList.value, ...rows]
            } else {
                historyList.value = rows
                // 重置为当前版本（第一条记录）
                selectedHistoryId.value = undefined
            }

            // 检查是否还有更多数据
            const totalLoaded = historyList.value.length
            hasMore.value = totalLoaded < count
        } else {
            message.error(response.message || '获取历史记录失败')
            // 如果请求失败，需要回退页码
            if (isLoadMore) {
                currentPage.value--
            }
        }
    } catch (error) {
        message.error('获取历史记录失败')
        console.error('Fetch history error:', error)
        // 如果请求失败，需要回退页码
        if (isLoadMore) {
            currentPage.value--
        }
    } finally {
        if (isLoadMore) {
            loadingMore.value = false
        } else {
            loading.value = false
        }
    }
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'loading' || key === 'empty' || key === 'loading-more' || key === 'no-more') return

    const selectedRecord = historyList.value.find((record) => record.id === key)
    if (selectedRecord) {
        // 如果点击的是第一条记录（当前版本），重置选中状态
        if (historyList.value.length > 0 && historyList.value[0].id === key) {
            selectedHistoryId.value = undefined
        } else {
            selectedHistoryId.value = key
        }

        emit('apply', {
            schema: selectedRecord.schema,
            func: selectedRecord.func
        })
        message.success('历史记录已应用')
    }
}

// 处理下拉菜单可见性变化
const handleDropdownVisibleChange = (visible: boolean) => {
    if (visible && !loading.value && historyList.value.length === 0) {
        fetchHistoryList()
    }

    if (visible) {
        // 当菜单打开时，添加滚动监听
        nextTick(() => {
            const menuElement = menuRef.value?.$el
            if (menuElement) {
                menuElement.addEventListener('scroll', handleMenuScroll)
            }
        })
    } else {
        // 当菜单关闭时，移除滚动监听
        const menuElement = menuRef.value?.$el
        if (menuElement) {
            menuElement.removeEventListener('scroll', handleMenuScroll)
        }
    }
}

// 处理菜单滚动事件
const handleMenuScroll = (event: Event) => {
    const target = event.target as HTMLElement
    const { scrollTop, scrollHeight, clientHeight } = target

    // 当滚动到底部附近时（还有20px距离），触发加载更多
    if (scrollHeight - scrollTop - clientHeight <= 20 && hasMore.value && !loadingMore.value) {
        fetchHistoryList(true)
    }
}

// 组件挂载时不立即加载数据，等用户点击下拉框时再加载
onMounted(() => {
    // 可以在这里做一些初始化工作，但不加载数据
})

// 组件销毁时清理事件监听
onUnmounted(() => {
    const menuElement = menuRef.value?.$el
    if (menuElement) {
        menuElement.removeEventListener('scroll', handleMenuScroll)
    }
})

// 重置到当前版本
const resetToCurrent = () => {
    selectedHistoryId.value = undefined
}

defineExpose({
    refresh: () => fetchHistoryList(false),
    resetToCurrent
})
</script>

<style scoped>
.history-button {
    background: #f6f8fa !important;
    border: 1px solid #d0d7de !important;
    border-radius: 6px !important;
    padding: 4px 15px !important;
    height: 32px !important;
    font-size: 14px !important;
    color: #24292f !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    box-shadow: 0 1px 0 rgba(27, 31, 36, 0.04) !important;
}

.history-button:hover {
    background: #f3f4f6 !important;
    border-color: #d0d7de !important;
}

.history-icon {
    font-size: 14px;
    color: #656d76;
}

.button-text {
    font-weight: 500;
    color: #24292f;
    font-size: 14px;
}

.arrow-icon {
    font-size: 12px;
    color: #656d76;
    margin-left: auto;
}

.history-menu-wrapper {
    border-radius: 6px;
    box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
    border: 1px solid #d0d7de;
    overflow: hidden;
}

.history-menu {
    min-width: 240px;
    max-height: 400px;
    overflow-y: auto;
    border: none;
    box-shadow: none;
}

.menu-option {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 4px 0;
}

.option-icon {
    font-size: 12px;
    color: #656d76;
    margin-top: 2px;
    flex-shrink: 0;
}

.option-content {
    flex: 1;
    min-width: 0;
}

.option-title {
    font-size: 13px;
    font-weight: 500;
    color: #24292f;
    line-height: 1.3;
    display: flex;
    align-items: center;
    gap: 8px;
}

.username-tag {
    font-size: 11px;
    background: #f6f8fa;
    color: #656d76;
    padding: 2px 6px;
    border-radius: 12px;
    border: 1px solid #d0d7de;
    font-weight: 400;
}

.current-badge {
    font-size: 11px;
    background: #0969da;
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 500;
}

.option-desc {
    font-size: 11px;
    color: #656d76;
    line-height: 1.3;
    margin-top: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.current-option .option-title {
    color: #0969da;
    font-weight: 600;
}

.current-option .option-icon {
    color: #0969da;
}

.selected-option .option-title {
    color: #0969da;
    font-weight: 600;
}

.selected-option .option-icon {
    color: #0969da;
}

.loading-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    color: #656d76;
    font-size: 12px;
}

.empty-content {
    padding: 8px 0;
    color: #656d76;
    font-size: 12px;
    text-align: center;
}

/* 菜单项样式覆盖 */
.history-menu :deep(.ant-menu-item) {
    padding: 8px 12px !important;
    margin: 0 !important;
    height: auto !important;
    line-height: normal !important;
}

.history-menu :deep(.ant-menu-item:hover) {
    background-color: #f6f8fa !important;
}

.history-menu :deep(.ant-menu-item-selected) {
    background-color: #dbeafe !important;
}

.history-menu :deep(.ant-menu-item-selected):after {
    display: none !important;
}

.history-menu :deep(.ant-menu-divider) {
    margin: 4px 0 !important;
    background-color: #d0d7de !important;
}
</style>
