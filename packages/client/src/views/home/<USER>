<template>
    <div class="container mx-auto px-4 py-20 overflow-auto">
        <section class="flex flex-col items-center text-center mb-16">
            <h1 class="text-6xl font-extrabold mb-8 gradient-text">Copilot AI平台 - 3分钟快速创建团队AI助手</h1>
            <p class="text-base max-w-3xl mb-12">
                在3分钟内快速创建您的团队AI助手，无论您是研发、产品经理，还是运营老师，Copilot平台都能帮助您轻松构建属于您业务的智能机器人，提升工作效率，降低AI使用门槛。
            </p>
            <a
                href="#get-started"
                class="bg-blue-600 text-white px-8 py-4 rounded-full text-sm font-semibold hover:bg-blue-700 transition"
                @click.prevent="onPersonalSpace"
                >立即开始</a
            >
        </section>

        <section class="my-24 text-center">
            <h2 class="text-3xl font-bold mb-12">什么是Copilot平台？</h2>
            <p class="text-base max-w-4xl mx-auto mb-12">
                Copilot平台是公司内部的AI
                Bot平台，面向全员开放。它允许您基于AI模型创建智能机器人，完成从智能问答到数据查询、任务提交等多种任务。您还能将这些机器人发布到工作群中，帮助团队成员更高效地完成工作。
            </p>
        </section>

        <section class="my-24">
            <h2 class="text-3xl font-bold text-center mb-16">平台特色功能</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <div class="bg-white p-8 rounded-lg flex flex-col items-center">
                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-blue-100 rounded-full">
                        <BuildingLibraryIcon class="w-8 h-8 text-blue-600" />
                    </div>
                    <strong class="text-lg mb-4">知识库管理</strong>
                    <p class="text-gray-600 leading-normal">
                        通过上传多种格式的文档（如.txt、.pdf、.docx等），轻松构建业务知识库。平台会自动切分文档片段，并在与AI对话时智能检索，确保回答准确无误。
                    </p>
                </div>
                <div class="bg-white p-8 rounded-lg flex flex-col items-center">
                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-green-100 rounded-full">
                        <BoltIcon class="w-8 h-8 text-green-600" />
                    </div>
                    <strong class="text-lg mb-4">一键生成智能Bot</strong>
                    <p class="text-gray-600 leading-normal">支持多种Bot模板，无需复杂的编程知识，您可以快速创建适合不同场景的智能助手。</p>
                </div>
                <div class="bg-white p-8 rounded-lg flex flex-col items-center">
                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-yellow-100 rounded-full">
                        <CogIcon class="w-8 h-8 text-yellow-600" />
                    </div>
                    <strong class="text-lg mb-4">多工作流编排</strong>
                    <p class="text-gray-600 leading-normal">
                        利用可视化工具，将知识库、大语言模型和智能体插件进行组合，实现复杂而稳定的业务流程。
                    </p>
                </div>
                <div class="bg-white p-8 rounded-lg flex flex-col items-center">
                    <div class="flex items-center justify-center w-12 h-12 mb-4 bg-red-100 rounded-full">
                        <CpuChipIcon class="w-8 h-8 text-red-600" />
                    </div>
                    <strong class="text-lg mb-4">智能体插件</strong>
                    <p class="text-gray-600 leading-normal">扩展AI能力，支持数据查询、流程申请、表单展示等多种场景，赋予AI更多的执行力。</p>
                </div>
            </div>
        </section>

        <section class="my-24 text-center">
            <h2 class="text-3xl font-bold mb-16">如何快速开始</h2>
            <div class="relative">
                <div class="grid grid-cols-1 md:grid-cols-[auto_80px_auto_80px_auto] gap-12 items-center">
                    <!-- Step 1 -->
                    <div class="p-8 flex flex-col items-center">
                        <div class="flex items-center justify-center w-12 h-12 mb-4 bg-blue-100 rounded-full">
                            <DocumentDuplicateIcon class="w-8 h-8 text-blue-600" />
                        </div>
                        <h3 class="text-lg font-semibold mb-3">Step 1. 创建知识库</h3>
                        <p class="leading-normal">上传您的业务文档，将其转化为可查询的知识片段，方便模型使用。</p>
                    </div>
                    <!-- Arrow between steps -->
                    <div class="hidden md:block w-20 flex justify-center">
                        <ArrowRightIcon class="w-12 h-12 text-gray-400" />
                    </div>
                    <!-- Step 2 -->
                    <div class="p-8 flex flex-col items-center">
                        <div class="flex items-center justify-center w-12 h-12 mb-4 bg-green-100 rounded-full">
                            <SparklesIcon class="w-8 h-8 text-green-600" />
                        </div>
                        <h3 class="text-lg font-semibold mb-3">Step 2. 生成Bot</h3>
                        <p class="leading-normal">选择合适的模板，一键生成适合您团队的AI助手，无需编写复杂代码。</p>
                    </div>
                    <!-- Arrow between steps -->
                    <div class="hidden md:block">
                        <ArrowRightIcon class="w-12 h-12 text-gray-400 mx-auto" />
                    </div>
                    <!-- Step 3 -->
                    <div class="p-8 flex flex-col items-center">
                        <div class="flex items-center justify-center w-12 h-12 mb-4 bg-yellow-100 rounded-full">
                            <PaperAirplaneIcon class="w-8 h-8 text-yellow-600" />
                        </div>
                        <h3 class="text-lg font-semibold mb-3">Step 3. 发布到工作群</h3>
                        <p class="leading-normal">分享链接或通过企微机器人将AI助手快速发布到您的工作群，让团队成员随时使用。</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="my-24 text-center">
            <h2 class="text-3xl font-bold mb-16">实际应用案例</h2>
            <div class="max-w-4xl m-auto">
                <a-tabs default-active-key="1" size="large" @change="handleCaseTab">
                    <a-tab-pane key="1" tab="信息查询">
                        <div class="cursor-pointer" @click="showCaseSearch = !showCaseSearch">
                            <img
                                v-show="!showCaseSearch"
                                src="https://i0.hdslb.com/bfs/activity-plat/static/20241021/f0d5ed1ef1dd7f917a9a0e5d46a6c810/Eo0yNFc3Gw.png"
                                class="w-full h-auto mx-auto rounded-lg"
                            />
                            <img
                                v-show="showCaseSearch"
                                src="https://i0.hdslb.com/bfs/activity-plat/static/20241021/f0d5ed1ef1dd7f917a9a0e5d46a6c810/0a8d6jomq2.gif"
                                class="w-full h-auto mx-auto rounded-lg"
                            />
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="Chat2Form">
                        <div class="cursor-pointer" @click="showCaseForm = !showCaseForm">
                            <img
                                v-show="!showCaseForm"
                                src="https://i0.hdslb.com/bfs/activity-plat/static/20241021/f0d5ed1ef1dd7f917a9a0e5d46a6c810/0Pw4ikmUsr.png"
                                class="w-full h-auto mx-auto rounded-lg"
                            />
                            <img
                                v-show="showCaseForm"
                                src="https://i0.hdslb.com/bfs/activity-plat/static/20241021/f0d5ed1ef1dd7f917a9a0e5d46a6c810/tpCQrL0iHM.gif"
                                class="w-full h-auto mx-auto rounded-lg"
                            />
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="3" tab="智能客服">
                        <div class="cursor-pointer" @click="showCaseServer = !showCaseServer">
                            <img
                                v-show="!showCaseServer"
                                src="https://i0.hdslb.com/bfs/activity-plat/static/20241021/f0d5ed1ef1dd7f917a9a0e5d46a6c810/eX91CuF7pG.png"
                                class="w-full h-auto mx-auto rounded-lg"
                            />
                            <img
                                v-show="showCaseServer"
                                src="https://i0.hdslb.com/bfs/activity-plat/static/20241021/f0d5ed1ef1dd7f917a9a0e5d46a6c810/Bu0sfWZia8.gif"
                                class="w-full h-auto mx-auto rounded-lg"
                            />
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </section>

        <section class="my-24 text-center bg-gray-100 p-16 rounded-lg">
            <h2 class="text-5xl font-bold mb-8">立即体验Copilot平台</h2>
            <p class="text-xl mb-12">想要3分钟内拥有自己的团队AI助手吗？</p>
            <video controls class="mx-auto rounded-lg w-full md:w-3/4">
                <source src="http://shjd-inner-boss.bilibili.co/copilot/3分钟创建团队AI助手.mp4" type="video/mp4" />
                您的浏览器不支持HTML视频。
            </video>
        </section>
    </div>
</template>

<script setup lang="ts">
import {
    BuildingLibraryIcon,
    BoltIcon,
    CogIcon,
    CpuChipIcon,
    ArrowRightIcon,
    DocumentDuplicateIcon,
    SparklesIcon,
    PaperAirplaneIcon
} from '@heroicons/vue/24/outline'
import { ref, toRefs } from 'vue'
import { useSpaceStore } from '@/stores/space'
import router from '@/router'

const spaceStore = useSpaceStore()

const { personalSpace } = toRefs(spaceStore)

const showCaseSearch = ref(false)
const showCaseForm = ref(false)
const showCaseServer = ref(false)

const handleCaseTab = () => {
    showCaseSearch.value = false
    showCaseForm.value = false
    showCaseServer.value = false
}

const onPersonalSpace = () => {
    router.push(`/space/${personalSpace.value.id}/workflow`)
}
</script>

<style scoped>
.container {
    max-width: 1600px;
}
.gradient-text {
    background-image: linear-gradient(to right, #22c55e, #facc15, #ec4899, #ef4444);
    -webkit-background-clip: text;
    color: transparent;
}
</style>
