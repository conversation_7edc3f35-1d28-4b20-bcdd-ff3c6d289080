<template>
    <div class="data-view-page">
        <p class="mb-[20px] font-semibold">数据预览</p>
        <a-table
            :dataSource="previewData"
            :columns="columns"
            rowKey="key"
            class="copilot-knowledge-table"
            :pagination="false"
            :scroll="{
                x: tableX,
                y: 'calc(100vh - 400px)'
            }"
        >
            <template #headerCell="{ column }">
                <template v-if="column.isIndex">
                    <span>
                        {{ column.title }}
                        <a-tag :bordered="false" color="green">向量存储</a-tag>
                    </span>
                </template>
            </template>
        </a-table>

        <div class="footer" v-if="totalRow > 20">
            <p>总{{ totalRow }}条记录，当前预览只展示前20条记录</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'

const props = defineProps<{ tableRule: { previewData: any; totalRow: number; tableMeta: any[] } }>()

const { previewData, totalRow, tableMeta } = toRefs(props.tableRule)

const columns = computed(() => {
    return tableMeta.value.map((item) => {
        return {
            title: item.columnName,
            key: item.columnName,
            dataIndex: item.columnName,
            isIndex: item.isIndex,
            width: 160,
            ellipsis: true
        }
    })
})

const tableX = computed(() => {
    return columns.value.length * 160 + 'px'
})
</script>

<style scoped>
h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
}

.progress-item {
    margin-bottom: 20px;
}

.label {
    display: block;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
}

.footer {
    margin-top: 20px;
    font-size: 14px;
    color: #1c1d2399;
}
</style>
