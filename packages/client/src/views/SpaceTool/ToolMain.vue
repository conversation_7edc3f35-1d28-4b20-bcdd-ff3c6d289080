<template>
    <div class="flex flex-col h-screen">
        <ToolHeader :detail="newDetail" @save="save" @edit="handleOpen"></ToolHeader>
        <splitpanes class="flex flex-1 overflow-hidden">
            <pane size="60" min-size="40">
                <splitpanes horizontal style="border-right: 1px solid #e5e6eb" class="text-center text-black flex-1 bg-ff flex flex-col">
                    <pane class="text-center text-black flex-1 bg-ff flex flex-col" min-size="30" size="75">
                        <div class="pt-3 pb-3 layout-radio bg-[#fff]">
                            <a-radio-group v-model:value="tab" size="default">
                                <a-radio-button value="1">Function</a-radio-button>
                                <a-radio-button value="2">
                                    Tool arguments
                                    <a-tooltip>
                                        <template #title>AI会在调用插件时根据入参字段的description,自动填入内容</template>
                                        <QuestionCircleOutlined />
                                    </a-tooltip>
                                </a-radio-button>
                                <!--<a-radio-button value="3">插件配置项</a-radio-button>-->
                            </a-radio-group>
                        </div>
                        <KeepAlive>
                            <div v-show="tab === '1'" class="flex flex-col flex-1">
                                <div
                                    class="h-[30px] bg-[#fff] flex justify-between items-center w-full"
                                    style="border-bottom: 1px solid #e5e6eb"
                                >
                                    <div class="flex items-center pl-4 color-[#0000008c]">
                                        <div class="bg-green-500 w-[8px] h-[8px] rounded-full mr-3" />
                                        <a-tooltip>
                                            <template #title>暂时只支持JavaScript</template>
                                            <span class="text-[12px]">JavaScript</span>
                                        </a-tooltip>
                                        <a-tooltip>
                                            <template #title>点击查看 如何使用Function</template>
                                            <InfoCircleOutlined @click="handleFunctionInfo" class="pl-3" />
                                        </a-tooltip>
                                        <a-tooltip>
                                            <template #title>点击查看 支持的npm包</template>
                                            <svg
                                                t="1702292210064"
                                                @click="handleNpmInfo"
                                                class="pl-3 cursor-pointer"
                                                viewBox="0 0 1024 1024"
                                                width="33"
                                                height="33"
                                                xmlns="http://www.w3.org/2000/svg"
                                                p-id="9172"
                                                fill="currentColor"
                                                aria-hidden="true"
                                                focusable="false"
                                            >
                                                <path
                                                    d="M117.149737 906.850263V117.160081h789.690182v789.690182z m148.521374-641.706667v492.533657h248.873374V367.843556h145.025293v389.906101h98.735321V265.143596z"
                                                    fill="#CB3837"
                                                    p-id="9173"
                                                ></path>
                                            </svg>
                                        </a-tooltip>
                                    </div>
                                    <div>
                                        <a-tooltip>
                                            <template #title>复制Demo代码到编辑器中</template>
                                            <span class="pr-4 cursor-pointer text-xs text-blue-500 font-bold" @click="handleCaseOverlay"
                                                >Demo</span
                                            >
                                        </a-tooltip>
                                    </div>
                                </div>
                                <Suspense>
                                    <template #default>
                                        <AsyncCodeEditor
                                            @updateCode="updateCode"
                                            :modelValue="newDetail.func"
                                            :schemaKey="schemaKey"
                                            :height="'70%'"
                                            :theme="'vs'"
                                            :width="'100%'"
                                            language="javascript"
                                        ></AsyncCodeEditor>
                                    </template>
                                    <template #fallback>
                                        <tool-spin />
                                    </template>
                                    <template #error>
                                        <tool-empty />
                                    </template>
                                </Suspense>
                            </div>
                        </KeepAlive>
                        <template v-if="tab === '2'">
                            <ParamForm :schema="newDetail.schema" @update="updateSchema"></ParamForm>
                        </template>
                        <template v-if="tab === '3'">
                            <OptionsForm :options="newDetail.options" @update="updateOptions"></OptionsForm>
                        </template>
                    </pane>
                    <pane v-if="tab === '1'" class="bg-white" min-size="5" size="25">
                        <div class="h-full flex-1 flex text-left flex-col">
                            <div
                                class="p-4 flex justify-between"
                                style="border-bottom: 1px solid rgb(229, 230, 235); border-top: 1px solid rgb(229, 230, 235)"
                            >
                                <span class="mb-0">日志</span>
                                <svg
                                    class="h-5 w-5 cursor-pointer"
                                    @click="handleClear"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    strokeWidth="{1.5}"
                                    stroke="currentColor"
                                    className="size-1"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                    />
                                </svg>
                            </div>
                            <div class="flex-1 overflow-y-auto h-full p-3 pl-1 pr-1 bg-[#f7f7fa]" ref="contentWrapper">
                                <transition-group name="fade" tag="div">
                                    <div
                                        v-for="(item, index) in logs"
                                        :key="index"
                                        class="pl-4 pr-4 pt-1 pb-1 flex hover:bg-gray-200 text-white px-4 py-2 rounded"
                                    >
                                        <div class="text-[#333] text-[14px]">
                                            <span class="text-[#333]" style="word-break: break-all">
                                                [
                                                <div class="text-[#939393] w-[55px] text-center inline-block">{{ item.time }}</div>
                                                ]
                                                <span
                                                    v-for="(log, yndex) in item!.logs"
                                                    :key="yndex"
                                                    :style="{
                                                        color: JSON.stringify(log).includes('Error') ? 'red' : 'rgba(0, 0, 0, 0.88)'
                                                    }"
                                                >
                                                    {{ JSON.stringify(log) + '  ' }}</span
                                                >
                                            </span>
                                        </div>
                                    </div>
                                </transition-group>
                            </div>
                        </div>
                    </pane>
                </splitpanes>
            </pane>
            <pane size="40" min-size="20">
                <splitpanes horizontal>
                    <pane min-size="30" class="text-black bg-white flex-1 z-1 flex flex-col">
                        <span class="p-4 bg-white m-0 h-[57px] w-full" style="border-bottom: 1px solid #e5e6eb">调试</span>
                        <div style="flex-direction: column" class="pr-4 pl-4 flex flex-1 overflow-y-auto w-full">
                            <div class="pt-4 pb-4 flex justify-between">
                                <span class="pt-2 text-[14px]">
                                    参数mock
                                    <a-button type="link" size="small" @click="copyDefaultInput">还原</a-button>
                                </span>
                                <a-button type="primary" class="w-[80px]" @click="run" :loading="iconLoading">
                                    <template #icon>
                                        <ApiOutlined />
                                    </template>
                                    运行
                                </a-button>
                            </div>
                            <Suspense>
                                <template #default>
                                    <AsyncJsonEditorPlugin
                                        :mainMenuBar="false"
                                        :statusBar="false"
                                        :navigationBar="false"
                                        v-model:text="input"
                                        @change="handleSetInput"
                                        expandAll
                                        mode="text"
                                        class="json-view flex-1"
                                        boxed
                                    >
                                    </AsyncJsonEditorPlugin>
                                </template>
                                <template #fallback>
                                    <tool-spin />
                                </template>
                                <template #error>
                                    <tool-empty />
                                </template>
                            </Suspense>
                        </div>
                    </pane>
                    <pane min-size="30" style="margin-bottom: 20px">
                        <div class="pr-4 pl-4 flex flex-1 flex-col overflow-y-auto w-full h-full">
                            <span class="pt-5 pb-5 text-[14px] w-full">输出</span>
                            <Suspense>
                                <template #default>
                                    <AsyncJsonEditorPlugin
                                        ref="childJsonRef"
                                        :mainMenuBar="false"
                                        :statusBar="false"
                                        :navigationBar="false"
                                        :askToFormat="false"
                                        v-model:json="result"
                                        mode="text"
                                        readOnly
                                        class="json-view flex-1"
                                        boxed
                                    >
                                    </AsyncJsonEditorPlugin>
                                </template>
                                <template #fallback>
                                    <tool-spin />
                                </template>
                                <template #error>
                                    <tool-empty />
                                </template>
                            </Suspense>
                        </div>
                    </pane>
                </splitpanes>
            </pane>
        </splitpanes>
    </div>
    <create-tool ref="createRef" :space-id="route.params.spaceId" @callback="getData" />
    <function-info ref="functionInfoRef"></function-info>
    <function-npm ref="npmInfoRef"></function-npm>
    <function-demo ref="functionDemoRef"></function-demo>
</template>

<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { onMounted, ref, defineAsyncComponent, nextTick, h } from 'vue'
import { useRoute } from 'vue-router'
import 'splitpanes/dist/splitpanes.css'
import { Splitpanes, Pane } from 'splitpanes'
import { message, Modal } from 'ant-design-vue'
import CreateTool from './components/CreateTool.vue'
import FunctionInfo from './ToolModal/FunctionInfo.vue'
import FunctionNpm from './ToolModal/FunctionNpm.vue'
import FunctionDemo from './ToolModal/FunctionDemo.vue'

import { ApiOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import ParamForm from '@/views/SpaceTool/components/SchemaForm.vue'
import OptionsForm from '@/views/SpaceTool/components/OptionsForm.vue'
import ToolHeader from '@/views/SpaceTool/components/ToolHeader.vue'
import ToolSpin from '@/views/SpaceTool/components/ToolSpin.vue'
import ToolEmpty from '@/views/SpaceTool/components/ToolEmpty.vue'

const AsyncCodeEditor = defineAsyncComponent(() => import('@/views/SpaceTool/components/CodeEditor.vue'))
const AsyncJsonEditorPlugin = defineAsyncComponent<ReturnType<typeof import('vue3-ts-jsoneditor')>>(() => import('vue3-ts-jsoneditor'))

import { functionTestTool, getToolDetail, updateTool, type TestToolLogItem } from '@/api/tool'
import { getDataFromLocalStorage, saveDataToLocalStorage } from '@/utils/localStorage'

const contentWrapper = ref<any>(null)
const childJsonRef = ref<any>(null)
const iconLoading = ref<boolean | { delay: number }>(false)
const createRef = ref<any>(null)
const route = useRoute()

const id: any = route.params.id
const spaceId: any = route.params.spaceId

const headerStyle: CSSProperties = {
    textAlign: 'center',
    color: '#000',
    height: '80px',
    paddingInline: 30,
    boxShadow: '0 2px 2px rgba(29, 28, 35, .04), 0 0 2px rgba(29, 28, 35, .18)',
    backgroundColor: '#f4f4f6',
    zIndex: 1,
    display: 'flex',
    padding: '0 22px'
}

const tab = ref('1')
const schemaKey = ref<string[]>([])
const detail = ref<any>({})
const newDetail = ref<any>({}) // 修改后的值
const functionInfoRef = ref<any>(null)
const npmInfoRef = ref<any>(null)
const functionDemoRef = ref<any>(null)

const original = ref('const noop = () => {}')
const defaultInput = {
    input: '用户输入的问题',
    schema: {
        hello: 'world'
    },
    env: {
        hello: '仅当场景是MCP时, 获取URL中的&hello=world参数'
    }
}

// todo: 这里有bug: mock数据类型异常 SyntaxError: Unexpected token
const inputValue = getDataFromLocalStorage(id, JSON.stringify(defaultInput))

const input = ref<string>(inputValue)

const result = ref({})
const logs = ref<TestToolLogItem[]>([])

const run = async () => {
    iconLoading.value = true
    try {
        const data = await functionTestTool({
            func: newDetail.value.func,
            ...(JSON.parse(input.value) as { input: string; schema: Record<string, any>; env: Record<string, any> })
        })
        logs.value = logs.value.concat(data.data.logs)
        result.value = data.data.result || {}
        // result 是字符且是TypeError 开头则是红色
    } finally {
        await nextTick(() => {
            childJsonRef.value && childJsonRef.value?.$expandAll()
            contentWrapper.value?.scrollTo(0, contentWrapper.value.scrollHeight)
            iconLoading.value = false
        })
    }
}
const handleClear = () => {
    logs.value = []
}

onMounted(() => {
    getData()
})

const handleOpen = () => {
    createRef.value?.open(newDetail.value || {})
}

const getData = async () => {
    const { data } = await getToolDetail(id)

    if (!data) {
        message.error('此条数据已被删除', 8000)
        return
    }
    detail.value = data
    newDetail.value = data
}

const handleSetInput = ({ text }: { text: string }) => {
    saveDataToLocalStorage(id, text)
    setToolStorage(text)
}

const setToolStorage = (text: string) => {
    try {
        schemaKey.value = Object.keys(JSON.parse(text).schema)
    } catch (e) {
        console.info('mock数据类型异常', e)
    }
}
setToolStorage(inputValue)

const updateCode = (func: string) => {
    newDetail.value = { ...newDetail.value, func }
}
const updateSchema = (schema: string) => {
    newDetail.value = { ...newDetail.value, schema: schema || '[]' }
}
const updateOptions = (options: any[]) => {
    newDetail.value = { ...newDetail.value, options }
}

const save = () => {
    Modal.confirm({
        title: `提示`,
        content: `确认保存配置吗？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            const { id, func, schema, name } = newDetail.value
            const data = {
                name,
                id,
                func: func || '',
                schema: schema || '[]'
            }
            await updateTool(data)
            message.success(`保存成功！`)
            await getData()
        }
    })
}

const handleCaseOverlay = () => {
    if (functionDemoRef.value && functionDemoRef.value.open) {
        functionDemoRef.value.open()
    }
}

const handleFunctionInfo = () => {
    if (functionInfoRef.value && functionInfoRef.value.open) {
        functionInfoRef.value.open()
    }
}

const handleNpmInfo = () => {
    if (npmInfoRef.value && npmInfoRef.value.open) {
        npmInfoRef.value.open()
    }
}

const copyDefaultInput = async () => {
    try {
        await navigator.clipboard.writeText(JSON.stringify(defaultInput, null, 2))
        message.success('默认参数已复制到剪贴板')
    } catch (e) {
        // 兼容不支持 clipboard API 的浏览器
        const textarea = document.createElement('textarea')
        textarea.value = JSON.stringify(defaultInput, null, 2)
        textarea.style.position = 'fixed'
        textarea.style.opacity = '0'
        document.body.appendChild(textarea)
        textarea.focus()
        textarea.select()
        try {
            document.execCommand('copy')
            message.success('默认参数已复制到剪贴板')
        } catch (err) {
            message.error('复制失败，当前浏览器不兼容')
        }
        document.body.removeChild(textarea)
    }
}
</script>

<style>
.tool {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.tool-body {
    display: flex;
    justify-content: space-between;
}

.tool-left {
    flex: 1;
    height: 100%;
    padding-left: 10px;
}

.tool-left .ant-tabs {
    flex: 1;
}

.tool-right {
    width: 400px;
    height: 100%;
    flex-shrink: 0;
}

.tool-right .jv-container {
    height: 100%;
}

.json-view {
    /*height: calc(100% - 65px);*/
    min-height: 200px;
    overflow: hidden;
    padding: 4px;
}

.jv-container.jv-light .jv-code {
    max-height: initial;
}

.jv-more {
    display: none;
}

.jv-container .jv-code.open {
    height: 100%;
    margin: 25px 0;
}

.layout-radio {
    border-bottom: 1px solid #e5e6eb;
    width: 100%;
}

.my-awesome-json-theme {
    background: #fff;
    white-space: nowrap;
    color: #525252;
    font-size: 14px;
    font-family: Consolas, Menlo, Courier, monospace;
}

.my-awesome-json-theme .jv-ellipsis {
    color: #999;
    background-color: #f4f4f6;
    display: inline-block;
    line-height: 0.9;
    font-size: 0.9em;
    padding: 0px 4px 2px 4px;
    border-radius: 3px;
    vertical-align: 2px;
    cursor: pointer;
    user-select: none;
}

.my-awesome-json-theme .jv-button {
    color: #49b3ff;
}

.my-awesome-json-theme .jv-key {
    color: #111111;
}

.my-awesome-json-theme .jv-item.jv-array {
    color: #111111;
}

.my-awesome-json-theme .jv-item.jv-boolean {
    color: #fc1e70;
}

.my-awesome-json-theme .jv-item.jv-function {
    color: #067bca;
}

.my-awesome-json-theme .jv-item.jv-number {
    color: #fc1e70;
}

.my-awesome-json-theme .jv-item.jv-number-float {
    color: #fc1e70;
}

.my-awesome-json-theme .jv-item.jv-number-integer {
    color: #fc1e70;
}

.my-awesome-json-theme .jv-item.jv-object {
    color: #111111;
}

.my-awesome-json-theme .jv-item.jv-undefined {
    color: #e08331;
}

.my-awesome-json-theme .jv-item.jv-string {
    color: #42b983;
    word-break: break-word;
    white-space: normal;
}

.my-awesome-json-theme .jv-code .jv-toggle:before {
    padding: 0px 2px;
    border-radius: 2px;
}

.my-awesome-json-theme .jv-code .jv-toggle:hover:before {
    background: #fff;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.codeEditBox {
    flex: 1;
}

.ͼ1 .cm-gutter-lint {
    width: 0;
}

.ͼ2 .cm-gutters {
    background: #fff !important;
}

svg {
    outline: none; /* 移除默认的轮廓边框样式 */
}

.splitpanes {
    background: #fff;
}
.splitpanes__pane {
    display: flex;
    position: relative;
}

.splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: #fff;
    margin-top: 56px;
    border-top: 1px solid rgb(229, 230, 235);
}

.splitpanes--horizontal > .splitpanes__splitter {
    min-height: 6px;
    background: #fff;
}
.splitpanes__splitter {
    position: relative;
}
.splitpanes__splitter:hover {
    transition: opacity 0.4s;
    background-color: rgba(203, 203, 203, 0.2);
}
.splitpanes--vertical .splitpanes__splitter:hover:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #00000026;
    transition: background-color 0.3s;
    transform: translateY(-50%);
    width: 1px;
    height: 30px;
    margin-left: -2px;
}
.splitpanes--vertical .splitpanes__splitter:hover:after {
    margin-left: 1px;
    transform: translateY(-50%);
    width: 1px;
    height: 30px;
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #00000026;
    transition: background-color 0.3s;
}
.splitpanes--horizontal .splitpanes__splitter:hover:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #00000026;
    transition: background-color 0.3s;
    transform: translateY(-50%);
    width: 30px;
    height: 1px;
    margin-left: 1px;
    margin-top: -2px;
}
.splitpanes--horizontal .splitpanes__splitter:hover:after {
    margin-top: 1px;
    transform: translateY(-50%);
    width: 30px;
    height: 1px;
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: #00000026;
    transition: background-color 0.3s;
}
</style>
