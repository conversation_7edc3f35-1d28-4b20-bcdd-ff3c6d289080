<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title></title>
    </head>
    <body>
        <script>
            const search = new URLSearchParams(location.search)

            const reportURL = '/api/v0/acceptStats'

            const report = () => {
                const data = JSON.stringify({
                    chatflowid: search.get('chatflowid'),
                    chatId: search.get('chat_id'),
                    messageId: search.get('message_id'),
                    extra: search.get('extra') || undefined
                })
                if (navigator.sendBeacon != null) {
                    navigator.sendBeacon(reportURL, new Blob([data], { type: 'application/json' }))
                } else if (window.fetch != null) {
                    fetch(reportURL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        mode: 'cors',
                        credentials: 'include',
                        keepalive: true,
                        body: data
                    })
                } else {
                    const xhr = new XMLHttpRequest()
                    xhr.open('POST', reportURL, false)
                    xhr.setRequestHeader('Content-Type', 'application/json')
                    xhr.withCredentials = true
                    xhr.send(data)
                }
            }

            report()

            window.location.href = search.get('redirect')
        </script>
    </body>
</html>
