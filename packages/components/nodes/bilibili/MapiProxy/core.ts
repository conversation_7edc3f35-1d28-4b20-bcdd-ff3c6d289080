import { Tool } from 'langchain/tools'
import fetch, { HeadersInit } from 'node-fetch'
import qs from 'qs'

export const desc = `内网代理转发网关`

export interface ProxyRequest {
    appid?: string
    url: string
    method: 'get' | 'post' | 'grpc' | 'rpc'
    params?: any
}

export class Mapi {
    headers = {}

    constructor({ headers }: { headers: HeadersInit }) {
        this.headers = headers
    }

    async proxy(params: ProxyRequest) {
        const mapi = `http://${(process.env.NODE_ENV !== 'production' && 'uat-') || ''}console.bilibili.co/nb/mapi/proxy/call`

        const body = {
            real_url: params.url,
            method: params.method || 'get',
            env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
            params: typeof params.params === 'object' ? JSON.stringify(params.params || {}) : params.params
        }

        try {
            const res = await fetch(mapi, {
                method: 'POST',
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: qs.stringify(body)
            })

            if (res.status !== 200) {
                throw new Error(`${res.status} ${res.statusText}`)
            }

            const { code, data, message } = (await res.json()) as { code: number; data: any; message: string }
            if (code === 0) {
                return data
            } else {
                throw new Error(`${code}, ${message}`)
            }
        } catch (error) {
            throw new Error(`${error}`)
        }
    }

    async txtProxy(params: ProxyRequest) {
        const mapi = `http://${(process.env.NODE_ENV !== 'production' && 'uat-') || ''}console.bilibili.co/nb/mapi/proxy/txtCall`

        const body = {
            real_url: params.url,
            method: params.method || 'get',
            env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
            params: typeof params.params === 'object' ? JSON.stringify(params.params || {}) : params.params
        }

        try {
            const res = await fetch(mapi, {
                method: 'POST',
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: qs.stringify(body)
            })

            if (res.status !== 200) {
                throw new Error(`${res.status} ${res.statusText}`)
            }

            return res.text()
        } catch (error) {
            throw new Error(`${error}`)
        }
    }
}
