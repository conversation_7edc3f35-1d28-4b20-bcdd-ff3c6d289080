import axios from 'axios'
import qs from 'qs'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, CreateAxiosDefaults, InternalAxiosRequestConfig } from 'axios'

import { notification } from 'ant-design-vue'
import { env } from '@/constant/env'

export interface ResponseData<T = any> {
    code: number
    message: string
    data: T
}
/**
 * 检测服务端处理结果，失败就提示，成功就返回response.data
 * .get('/').then(checkResponse<>)
 */
export function checkResponse<T = any>(body: AxiosResponse<ResponseData<T>>) {
    // 网络正常
    if (body && body.status >= 200 && body.status < 300) {
        if (body.data.code === 0) {
            return body.data as ResponseData<T>
        } else {
            if (body.data.code === 401 || body.data.code === 422) {
                notification.warn({
                    message: `没有权限 ${body.data.code}`,
                    description: `${body.data.message}`,
                    duration: 1.2
                })
            } else {
                notification.error({
                    message: `请求出错 ${body.data.code}`,
                    description: `${body.data.message}`
                })
            }
            throw body.data.message
        }
    }
    notification.error({
        message: '网络错误',
        description: `${body.statusText}`
    })
    throw body.statusText
}

interface CustomInterceptors<T = AxiosResponse> {
    interceptors?: {
        reqSuccessFn?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
        reqFailureFn?: (err: any) => any
        resSuccessFn?: (res: T) => T | Promise<any>
        resFailureFn?: (err: any) => any
    }
}

class ClientRequest {
    public instance: AxiosInstance

    constructor(config: CreateAxiosDefaults & CustomInterceptors) {
        this.instance = axios.create(config)

        this.instance.interceptors.response.use(null, (e) => {
            notification.error({
                message: '网络错误',
                description: `${e.message}`
            })
        })

        if (config.interceptors) {
            const { resFailureFn, resSuccessFn, reqSuccessFn, reqFailureFn } = config.interceptors

            this.instance.interceptors.request.use(reqSuccessFn, reqFailureFn)
            this.instance.interceptors.response.use(resSuccessFn, resFailureFn)
        }
    }

    request<T = any>(config: AxiosRequestConfig) {
        return this.instance.request<any, T>({ ...config })
    }

    get<T = any>(url: string, config?: AxiosRequestConfig) {
        return this.request<T>({ ...config, url, method: 'GET' })
    }
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig) {
        return this.request<T>({ ...config, data, url, method: 'POST' })
    }
    delete<T = any>(url: string, config?: AxiosRequestConfig) {
        return this.request<T>({ ...config, url, method: 'DELETE' })
    }
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig) {
        return this.request<T>({ ...config, url, data, method: 'PUT' })
    }
}

export const apiV1 = new ClientRequest({
    baseURL: `/api`,
    headers: {
        'Content-type': 'application/json'
    },
    withCredentials: true,
    interceptors: {
        resSuccessFn(res) {
            return res.data
        }
    }
})

export const apiV0 = new ClientRequest({
    baseURL: `/api`,
    headers: {
        'Content-type': 'application/json'
    },
    withCredentials: true,
    interceptors: {
        resSuccessFn(res) {
            if (res.data?.code === 301) {
                // const route = encodeURIComponent('/' + location.hash)
                // const route = encodeURIComponent(window.location.pathname + window.location.hash + window.location.search)
                const caller = 'console-mlive'
                window.location.href = `https://dashboard-mng.biliapi.net/api/v4/user/dashboard_login?caller=${caller}`
                return Promise.reject()
            }
            return Promise.resolve(checkResponse(res))
        }
    }
})

interface ProxyReq {
    url: string
    method: 'get' | 'post'
    params?: unknown
    appid?: number | string
}

export const proxy = async <T>(params: ProxyReq): Promise<T> => {
    const prefix = env === 'production' ? '' : 'uat-'

    const req = {
        real_url: params.url,
        method: params.method || 'get',
        env,
        params: JSON.stringify(params.params || {})
    }

    return new Promise(function (resolve, reject) {
        fetch(`//${prefix}mlive-api.bilibili.co/api/proxy/call`, {
            credentials: 'include',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: qs.stringify(req)
        })
            .then(async (res) => {
                const { code, data, message } = (await res.json()) as { code: number; data: T; message: string }
                if (code === 0) {
                    resolve(data)
                } else {
                    reject({ code, message })
                }
            })
            .catch((e) => {
                reject(e)
            })
    })
}
