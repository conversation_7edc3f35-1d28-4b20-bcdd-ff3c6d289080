export interface IMCPInfo {
    id: string
    name: string
    title: string
    description: string

    /**
     * - copilot: 通过copilot平台已有的工具集创建
     * - external: 使用外部自托管服务
     */
    createType: 'copilot' | 'external'

    /**
     * - SSE
     * - Streamable
     * - STDIO 【TODO: 暂不支持】
     */
    transportType: string

    /**
     * 工具来源数据
     * 根据type区分
     * - copilot: 值为copilot平台已有的工具集id
     * - external: 值为外部自托管服务URL
     */
    source: string

    /**
     * 使用场景
     */
    scene: string

    iconSrc?: string
    spaceId: string
    creator: string
    ctime: string
    mtime: string
}
