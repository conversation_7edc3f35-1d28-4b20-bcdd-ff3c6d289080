import moment from 'moment'
import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('ai_script_schedule')
export class ScriptSchedule {
    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column()
    cron: string

    @Column()
    chatflowid: string

    @Column()
    content: string

    @Column()
    username: string

    @Column({ name: 'wx_bot' })
    wxBot: string

    @Column()
    status: number

    @Column({ name: 'last_run_date' })
    lastRunDate: string

    @Column({ name: 'last_run_status' })
    lastRunStatus: number

    @CreateDateColumn({
        type: 'datetime',
        update: false,
        transformer: {
            to: (value: string) => value,
            from: (value: Date) => moment(value).format('YYYY-MM-DD HH:mm:ss')
        }
    })
    ctime: Date

    @UpdateDateColumn({
        type: 'datetime',
        update: false,
        transformer: {
            to: (value: string) => value,
            from: (value: Date) => moment(value).format('YYYY-MM-DD HH:mm:ss')
        }
    })
    mtime: Date
}
