import { apiV0, type ResponseData } from './client'
import { type IMCPInfo } from '@/interface/mcp'

export const addMCP = (data: Omit<IMCPInfo, 'creator' | 'ctime' | 'mtime'>) => {
    return apiV0.post<ResponseData>('/v0/mcp/add', data)
}

export const updateMCP = (data: Omit<IMCPInfo, 'creator' | 'ctime' | 'mtime'>) => {
    return apiV0.post('/v0/mcp/update', data)
}

export const getMCPList = (params: { page?: number; spaceId?: string; pageSize?: number; keyword?: string }) => {
    return apiV0.get(`/v0/mcp/list`, { params })
}

export const deleteMCP = (id: string) => {
    return apiV0.post('/v0/mcp/delete', { id })
}

export const getMCP = (id: string) => {
    return apiV0.get<ResponseData<IMCPInfo>>('/v0/mcp', { params: { id } })
}

export const getToolsInfo = (ids: string[]) => {
    return apiV0.post('/v0/tools/ids', { ids })
}

export const getListToolsBySSEUrl = (url: string) => {
    return apiV0.get('/v0/mcp/list-tools', { params: { url } })
}

export const mcpSign = () => {
    return apiV0.get('/v0/mcp/sign')
}
