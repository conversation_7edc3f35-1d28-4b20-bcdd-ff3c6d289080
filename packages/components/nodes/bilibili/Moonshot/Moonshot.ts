import { ChatOpenAI, ChatOpenAIFields } from '@langchain/openai'
import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'

class Moonshot_ChatModels implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    description: string
    baseClasses: string[]
    credential: INodeParams
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'Moonshot'
        this.name = 'moonshot'
        this.version = 1.0
        this.type = 'Moonshot'
        this.icon = 'moonshot.png'
        this.category = 'Chat Models'
        this.description = '月之暗面AI Chat Model'
        this.tags = ['bilibili']
        this.baseClasses = [this.type, 'BaseChatModel', ...getBaseClasses(ChatOpenAI)]
        this.inputs = [
            {
                label: 'Model',
                name: 'modelName',
                type: 'options',
                options: [
                    {
                        label: 'moonshot-v1-8k',
                        name: 'moonshot-v1-8k'
                    },
                    {
                        label: 'moonshot-v1-32k',
                        name: 'moonshot-v1-32k'
                    },
                    {
                        label: 'moonshot-v1-128k',
                        name: 'moonshot-v1-128k'
                    }
                ],
                default: 'moonshot-v1-8k'
            },
            {
                label: 'API Key',
                name: 'apiKey',
                type: 'string'
            },
            {
                label: 'Stream',
                name: 'streaming',
                description: '开启ws流模式',
                type: 'boolean'
            },
            {
                label: 'Temperature',
                name: 'temperature',
                type: 'number',
                step: 0.1,
                default: 0.3,
                optional: true,
                additionalParams: true
            },
            {
                label: 'Max Tokens',
                name: 'maxTokens',
                type: 'number',
                step: 1,
                default: 1024,
                optional: true,
                additionalParams: true
            },
            {
                label: 'Top Probability',
                name: 'topP',
                type: 'number',
                step: 0.1,
                default: 1.0,
                optional: true,
                additionalParams: true
            }
        ]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const apiKey = nodeData.inputs?.apiKey as string
        const temperature = nodeData.inputs?.temperature as string
        const modelName = (nodeData.inputs?.modelName as string) || 'moonshot-v1-8k'
        const maxTokens = nodeData.inputs?.maxTokens as string
        const topP = nodeData.inputs?.topP as string
        const streaming = nodeData.inputs?.streaming as boolean

        const obj: ChatOpenAIFields = {
            model: modelName,
            openAIApiKey: apiKey,
            streaming: streaming,
            configuration: {
                baseURL: 'https://api.moonshot.cn/v1/'
            },
            maxRetries: 1
        }

        if (temperature) obj.temperature = parseFloat(temperature)
        if (maxTokens) obj.maxTokens = parseInt(maxTokens, 10)
        if (topP) obj.topP = parseFloat(topP)

        const model = new ChatOpenAI(obj)

        return model
    }
}

module.exports = { nodeClass: Moonshot_ChatModels }
