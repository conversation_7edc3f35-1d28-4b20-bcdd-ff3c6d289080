import { Tool } from 'langchain/tools'
import fetch, { HeadersInit } from 'node-fetch'
import qs from 'qs'

// /api/v1/nlp/vector/query/do_query

export interface VectorQueryParams {
    query: string
    topN: number
    datasetId: string
    distance: number
}

interface VectorContent {
    distance: number
    content: string
    sliceId: string
    sentenceId: number
    knowledgeId: string
    filename: string
    location: string
}

interface VectorQueryResponse {
    data: VectorContent[]
    code: number
    message: string
}

export const vectorQuery = async (body: VectorQueryParams) => {
    let url = ''
    if (process.env.NODE_ENV === 'local') {
        url = 'http://dev.bilibili.co:3000/api/knowledge/vector/query'
    } else {
        const prefix = process.env.NODE_ENV === 'uat' ? 'uat-' : ''
        url = `http://${prefix}copilot.bilibili.co/api/knowledge/vector/query`
    }

    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
    })

    if (res.status !== 200) {
        throw new Error(`Vector Query Error: ${res.status} ${res.statusText}`)
    }

    const data: VectorQueryResponse = await res.json()
    if (data.code !== 0) {
        throw new Error(`${data.code} ${data.message}`)
    }

    return data.data
}
