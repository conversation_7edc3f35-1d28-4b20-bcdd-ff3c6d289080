{"name": "@bilibili/copilot", "version": "3.0.1", "description": "基于Flowiseai Server定制开发", "main": "dist/index", "types": "dist/index.d.ts", "bin": {"flowise": "./bin/run"}, "files": ["bin", "marketplaces", "dist", "npm-shrinkwrap.json", "oclif.manifest.json", "oauth2.html"], "oclif": {"bin": "flowise", "commands": "./dist/commands"}, "scripts": {"build": "tsc", "start": "run-script-os", "clean": "<PERSON><PERSON><PERSON> dist", "nuke": "rimraf dist node_modules .turbo", "start:windows": "cd bin && run start", "start:default": "cd bin && ./run start", "start-worker:windows": "cd bin && run worker", "start-worker:default": "cd bin && ./run worker", "dev": "concurrently \"yarn watch\" \"nodemon\"", "oclif-dev": "run-script-os", "oclif-dev:windows": "cd bin && dev start", "oclif-dev:default": "cd bin && ./dev start", "postpack": "shx rm -f oclif.manifest.json", "prepack": "pnpm build && oclif manifest && oclif readme", "typeorm": "typeorm-ts-node-commonjs", "watch": "tsc --watch", "version": "oclif readme && git add README.md"}, "keywords": [], "homepage": "https://flowiseai.com", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20"}, "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.699.0", "@bilibili-live/node-yuumi": "^3.0.3", "@bilibili/bunyan-log-agent": "^3.1.0", "@bilibili/config-sdk": "^4.1.16", "@bilibili/copilot-components": "workspace:^3.0.1-rc.1", "@bilibili/databus": "^1.4.1", "@google-cloud/logging-winston": "^6.0.0", "@langchain/community": "^0.3.11", "@langchain/openai": "0.5.6", "@modelcontextprotocol/sdk": "1.10.1", "@oclif/core": "4.0.7", "@opentelemetry/api": "^1.3.0", "@opentelemetry/auto-instrumentations-node": "^0.52.0", "@opentelemetry/core": "1.27.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.54.0", "@opentelemetry/exporter-metrics-otlp-http": "0.54.0", "@opentelemetry/exporter-metrics-otlp-proto": "0.54.0", "@opentelemetry/exporter-trace-otlp-grpc": "0.54.0", "@opentelemetry/exporter-trace-otlp-http": "0.54.0", "@opentelemetry/exporter-trace-otlp-proto": "0.54.0", "@opentelemetry/resources": "1.27.0", "@opentelemetry/sdk-metrics": "1.27.0", "@opentelemetry/sdk-node": "^0.54.0", "@opentelemetry/sdk-trace-base": "1.27.0", "@opentelemetry/semantic-conventions": "1.27.0", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@wecom/crypto": "^1.0.1", "async-mutex": "^0.4.0", "aws-sdk": "^2.1580.0", "axios": "1.7.9", "bull-board": "^2.1.3", "bullmq": "~5.42.0", "cheerio": "^1.0.0-rc.12", "content-disposition": "0.5.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cron-parser": "^4.9.0", "crypto-js": "^4.1.1", "dotenv": "^16.0.0", "express": "^4.17.3", "express-basic-auth": "^1.2.1", "express-rate-limit": "^6.9.0", "express-validator": "^7.0.1", "express-xml-bodyparser": "^0.3.0", "flowise-nim-container-manager": "^1.0.11", "global-agent": "^3.0.0", "http-errors": "^2.0.0", "http-status-codes": "^2.3.0", "ioredis": "^5.4.1", "jimp": "^0.22.12", "langchain": "^0.3.5", "langchainhub": "^0.0.11", "lodash": "^4.17.21", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "multer": "^1.4.5-lts.1", "multer-cloud-storage": "^4.0.0", "multer-s3": "^3.0.1", "mysql2": "^3.11.3", "nanoid": "^3.3.7", "node-fetch": "^2.6.11", "node-xlsx": "^0.24.0", "openai": "4.96.0", "pg": "^8.11.1", "posthog-node": "^3.5.0", "prom-client": "^15.1.3", "rate-limit-redis": "^4.2.0", "reflect-metadata": "^0.1.13", "sanitize-html": "^2.11.0", "sqlite3": "^5.1.6", "turndown": "^7.2.0", "turndown-plugin-gfm": "^1.0.2", "typeorm": "^0.3.6", "uuid": "^9.0.1", "vm2": "^3.9.19", "winston": "^3.9.0", "xml2js": "^0.6.2", "zod": "3.22.4"}, "devDependencies": {"@types/content-disposition": "0.5.8", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.1.1", "@types/d3-dsv": "^3.0.7", "@types/express-xml-bodyparser": "^0.3.5", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.7", "@types/multer-s3": "^3.0.3", "@types/node-fetch": "2.6.2", "@types/sanitize-html": "^2.9.5", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "concurrently": "^7.1.0", "nodemon": "^2.0.22", "oclif": "^3", "rimraf": "^5.0.5", "run-script-os": "^1.1.6", "shx": "^0.3.3", "start-server-and-test": "^2.0.3", "ts-node": "^10.7.0", "tsc-watch": "^6.0.4", "typescript": "^5.4.5"}, "pnpm": {"onlyBuiltDependencies": ["faiss-node", "sqlite3"]}, "resolutions": {"@huggingface/tasks": "~0.11.13", "@langchain/core": "0.3.37", "@langchain/google-common": "0.2.3", "@langchain/google-gauth": "0.2.3", "@langchain/google-vertexai": "0.2.3", "@opentelemetry/sdk-trace-base": "1.27.0", "@qdrant/openapi-typescript-fetch": "1.2.1", "@types/express": "^4.17.3", "@types/mime": "3.0.4", "openai": "4.96.0", "rimraf": "^5.0.5", "undici": "5.28.3"}}