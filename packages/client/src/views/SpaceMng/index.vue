<template>
    <div class="p-8 rounded my-8">
        <div style="width: 800px; height: 400px" class="mx-auto">
            <div>
                <h3 class="my-2">基本信息</h3>
                <a-form name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
                    <a-form-item label="团队名称" name="title" v-bind="validateInfos.title">
                        <a-input disabled v-model:value="state.formState.title" placeholder="请输入团队名称" />
                    </a-form-item>

                    <a-form-item label="团队描述" name="description" v-bind="validateInfos.description">
                        <a-textarea v-model:value="state.formState.description" placeholder="请输入团队描述" />
                    </a-form-item>
                </a-form>
            </div>

            <div>
                <h3 class="my-8">权限管理</h3>
                <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" autocomplete="off">
                    <a-form-item label="owners" name="owners">
                        <b-oa-user-select
                            class="b-oa-user-select"
                            ref="bOaSelectUserRef"
                            :selected.prop="state.members"
                            mode="multiple"
                            @change="handleMemberUpdate"
                        ></b-oa-user-select>
                    </a-form-item>
                </a-form>
            </div>
            <div class="text-right">
                <a-tooltip placement="topLeft" :title="`联系创建人@${state.formState.creator}`">
                    <a-button :loading="state.loading" :disabled="!saveRole" type="primary" @click="save">保存</a-button>
                </a-tooltip>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router'
import { onMounted, watch, reactive, computed } from 'vue'
import { Form, message, Modal } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import { getSpaceDetail, getSpaceMembers, updateMembers, updateSpace } from '@/api/space'
import type { SapceForm } from '@/interface/space'

const importOaUserSelect = async () => {
    // @ts-ignore
    const module = await import(/* @vite-ignore */ '//s1.hdslb.com/bfs/seed/camelot-rsc/oa-user-select/latest/oa-user-select.mjs')
    module.OaUserSelectRegister()
    const shadowRoot = document.querySelector('.b-oa-user-select')?.shadowRoot
    const biSelect = shadowRoot?.querySelector<HTMLElement>('.bi-select')
    const inputBox = shadowRoot?.querySelector<HTMLElement>('input')
    if (biSelect && inputBox) {
        biSelect.style.backgroundColor = 'transparent'
        inputBox.style.backgroundColor = 'transparent'
    }
}
const route = useRoute()
const { isSelf } = useUserStore()

const state = reactive({
    formState: {} as SapceForm,
    spaceId: route.params.spaceId as string,
    members: [] as string[],
    loading: false
})

const saveRole = computed(() => isSelf(state.formState.creator))

const { resetFields, validate, validateInfos } = Form.useForm(
    state.formState,
    reactive({
        title: [
            {
                required: true,
                type: 'string'
            }
        ],
        description: [
            {
                type: 'string'
            }
        ]
    })
)
onMounted(() => {
    importOaUserSelect()
    getSpaceData()
    getSpaceMembersData()
})
const getSpaceData = async () => {
    const { data } = await getSpaceDetail(state.spaceId)

    state.formState = {
        id: data.id,
        ...data.space
    }
}
const getSpaceMembersData = async () => {
    const { data } = await getSpaceMembers(state.spaceId)
    if (Array.isArray(data)) {
        state.members = data.map((item) => item.username)
    }
}

const handleMemberUpdate = (e: Event) => {
    state.members = (e as CustomEvent).detail[0]
}

const save = async () => {
    try {
        state.loading = true
        await updateMembers(state.spaceId, state.members.join(','))
        await updateSpace({
            id: state.spaceId,
            description: state.formState.description
        })
        message.success('更新成功')
    } catch (error) {
        console.error(error)
    }
    state.loading = false
}
</script>
<style scoped>
.ant-input:not(.ant-input-disabled) {
    background: transparent;
}
</style>
