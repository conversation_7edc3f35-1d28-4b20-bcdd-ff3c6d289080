import { ICommonObject, INode, INodeData, INodeOutputsValue, INodeParams } from '../../../src/Interface'
import { ProxyRequest, Mapi } from './core'
import { formatResponse } from '../../outputparsers/OutputParserHelpers'
import json2md = require('json2md')
import { availableDependencies, handleEscapeCharacters } from '../../../src'
import { NodeVM } from 'vm2'
import { getUserCookie } from '../../../src/utils'

class MapiProxy_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]
    outputs: INodeOutputsValue[]

    constructor() {
        this.label = 'MapiProxy'
        this.name = 'mapiProxy'
        this.version = 1.0
        this.type = 'MapiProxy'
        this.icon = 'mapi.png'
        this.category = 'Tools'
        this.description = '后台proxy网关，用于接口转发'
        this.tags = ['bilibili']
        this.baseClasses = [this.type, 'json']
        this.inputs = [
            {
                label: 'App ID',
                name: 'appid',
                type: 'string',
                description: '应用的discovery id',
                optional: true,
                additionalParams: true
            },
            {
                label: 'Method',
                name: 'method',
                type: 'string',
                description: 'get | post | grpc | rpc',
                additionalParams: true
            },
            {
                label: 'URL',
                name: 'url',
                type: 'string',
                description: '请求地址',
                additionalParams: true
            },
            {
                label: 'Headers',
                name: 'headers',
                type: 'json',
                description: '自定义headers头',
                optional: true,
                additionalParams: true
            },
            {
                label: 'Callback',
                name: 'callback',
                type: 'code',
                description: '自定义处理函数,输入是$response',
                optional: true,
                additionalParams: true
            },
            {
                label: 'Params',
                name: 'params',
                type: 'json',
                description: '请求参数，如果存在_params，则会将对象上移一层',
                optional: true,
                acceptVariable: true
            }
        ]
        this.outputs = [
            {
                label: 'Output Json',
                name: 'outputJson',
                baseClasses: ['string', 'json']
            },
            {
                label: 'Output Markdown Table',
                name: 'outputMarkdownTable',
                baseClasses: ['string', 'json']
            },
            {
                label: 'Output Text',
                name: 'outputText',
                baseClasses: ['string']
            }
        ]
    }

    async init(nodeData: INodeData, input: string, options: ICommonObject): Promise<any> {
        const appid = nodeData.inputs?.appid as string
        const method = nodeData.inputs?.method as 'get' | 'post' | 'grpc' | 'rpc'
        const url = nodeData.inputs?.url as string
        const params = (nodeData.inputs?.params as string) || '{}'
        const headers = (nodeData.inputs?.headers as string) || '{}'
        const callback = (nodeData.inputs?.callback as string) || ''
        const output = nodeData.outputs?.output as string
        const cookie = await getUserCookie(options.chatId)

        const obj: ProxyRequest = {
            method,
            url,
            params
        }

        if (appid) obj.appid = appid

        if (params) {
            const parsedBody = typeof params === 'object' ? params : JSON.parse(params)
            // 如果存在_params,则认为需要将对象上移一层
            obj.params = (parsedBody._params && parsedBody._params) || parsedBody
        }

        const parsedHeaders = typeof headers === 'object' ? headers : JSON.parse(headers)

        const mapi = new Mapi({
            headers: {
                Cookie: cookie,
                ...parsedHeaders
            }
        })

        let res: any = ''
        if (output === 'outputText') {
            res = await mapi.txtProxy(obj)
        } else {
            res = await mapi.proxy(obj)
        }
        console.log(`[mapi]: ${url}`)

        /**
         * 处理自定义回调函数
         */
        if (callback) {
            let sandbox: any = { $input: input }
            sandbox[`$response`] = res

            const defaultAllowBuiltInDep = [
                'assert',
                'buffer',
                'crypto',
                'events',
                'http',
                'https',
                'net',
                'path',
                'querystring',
                'timers',
                'tls',
                'url',
                'zlib'
            ]

            const builtinDeps = process.env.TOOL_FUNCTION_BUILTIN_DEP
                ? defaultAllowBuiltInDep.concat(process.env.TOOL_FUNCTION_BUILTIN_DEP.split(','))
                : defaultAllowBuiltInDep
            const externalDeps = process.env.TOOL_FUNCTION_EXTERNAL_DEP ? process.env.TOOL_FUNCTION_EXTERNAL_DEP.split(',') : []
            const deps = availableDependencies.concat(externalDeps)

            const nodeVMOptions = {
                console: 'inherit',
                sandbox,
                require: {
                    external: { modules: deps },
                    builtin: builtinDeps
                }
            } as any

            const vm = new NodeVM(nodeVMOptions)
            try {
                const newRes = await vm.run(`module.exports = async function() {${callback}}()`, __dirname)
                res = newRes
            } catch (e) {
                throw new Error(e)
            }
        }

        if (output === 'outputJson') {
            return formatResponse(res || '{}')
        } else if (output === 'outputMarkdownTable') {
            let mk = ''
            if (res && res[0]) {
                mk = json2md([
                    {
                        table: {
                            headers: Object.keys(res[0]),
                            rows: res
                        }
                    }
                ])
            }
            return handleEscapeCharacters(formatResponse(mk), false)
        } else if (output === 'outputText') {
            return handleEscapeCharacters(formatResponse(res), false)
        }
    }
}

module.exports = { nodeClass: MapiProxy_Tools }
