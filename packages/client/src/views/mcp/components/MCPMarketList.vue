<template>
    <div class="copilot-card-container">
        <a-card
            v-for="mcp in props.data"
            :key="mcp.id"
            hoverable
            class="w-full"
            :bodyStyle="{ paddingBottom: '20px' }"
            @click="todeTail(mcp.id)"
            size="small"
        >
            <template #actions>
                <div class="flex justify-between items-center">
                    <a-button disabled type="link">最后更新：{{ mcp.mtime }}</a-button>
                    <a-button type="link" @click="todeTail(mcp.id)">
                        <SettingOutlined />
                        查看详情
                    </a-button>
                </div>
            </template>
            <a class="block w-full h-full text-black" target="_blank">
                <a-card-meta class="p-0">
                    <template #title>
                        <div class="flex">
                            <div class="flex items-center">
                                <a-avatar class="mr-[10px]" :size="68" shape="square" :src="mcp.iconSrc || MCPIcon" />
                            </div>
                            <div>
                                <div>{{ mcp.title }}</div>
                                <a-typography-text type="secondary" class="mr-1" style="color: var(--color-symbol-bold)">{{
                                    mcp.name
                                }}</a-typography-text
                                ><a-typography-text type="secondary" style="color: var(--color-symbol-dc)"
                                    >@{{ mcp.creator }}</a-typography-text
                                >
                                <div>
                                    <a-tag v-for="type in mcp.transportType.split(',')" :key="type" color="blue">
                                        <span class="text-[10px]">{{ type }}</span>
                                    </a-tag>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #description>
                        <div class="h-[80px] flex flex-col justify-between">
                            <a-typography-paragraph
                                class="mt-[10px]"
                                style="color: var(--color-symbol-bold); margin-bottom: 8px"
                                type="secondary"
                                :ellipsis="{ rows: 2 }"
                                :content="mcp.description || '暂无描述'"
                            >
                            </a-typography-paragraph>
                            <div>
                                <a-tag v-for="tag in mcp.scene.split(',')" :key="tag" color="green">{{ tag }}</a-tag>
                            </div>
                        </div>
                    </template>
                </a-card-meta>
            </a>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import MCPIcon from '@/assets/images/mcp.png'
import { useRouter } from 'vue-router'
import { type IMCPInfo } from '@/interface/mcp'

export interface IProps {
    data: IMCPInfo[]
}

const router = useRouter()

const props = defineProps<IProps>()

const todeTail = (id: string) => {
    router.push(`/mcp/detail/${id}`)
}
</script>
