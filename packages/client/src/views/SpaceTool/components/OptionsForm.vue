<template>
    <div class="flex ml-25 mr-25 mt-15">
        <a-button @click="add" type="primary">新增</a-button>
    </div>
    <a-table
        :pagination="false"
        :scroll="{ x: 550, y: 500 }"
        :columns="columns"
        :data-source="dataSource"
        bordered
        hideOnSinglePage
        class="p-5"
    >
        <template #bodyCell="{ column, index, record }">
            <template v-if="['title', 'name', 'value', 'placeholder', 'description'].includes(column.dataIndex)">
                <a-input
                    v-if="column.dataIndex !== 'required'"
                    :value="dataSource[index][column.dataIndex]"
                    @change="(e: ChangeEvent)=> {dataSource[index][column.dataIndex] = e.target.value}"
                    class="m-[-5px] mx-0"
                />
            </template>
            <a-radio-group
                v-if="column.dataIndex === 'required'"
                :value="dataSource[index][column.dataIndex]"
                @change="(e: RadioChangeEvent)=> {
                         dataSource[index][column.dataIndex] = e.target.value}"
                button-style="solid"
                size="small"
            >
                <a-radio-button :value="true">true</a-radio-button>
                <a-radio-button :value="false">false</a-radio-button>
            </a-radio-group>
            <template v-else-if="column.dataIndex === 'operation'">
                <div class="editable-row-operations">
                    <a-popconfirm title="确定删除？" @confirm="cancel(record.id)">
                        <a>删除</a>
                    </a-popconfirm>
                </div>
            </template>
        </template>
    </a-table>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from 'vue'
import type { RadioChangeEvent } from 'ant-design-vue'
import type { ChangeEvent } from 'ant-design-vue/es/_util/EventInterface'

const columns = [
    {
        title: '标题',
        dataIndex: 'title',
        width: '25%'
    },
    {
        title: '属性名(英文)',
        dataIndex: 'name',
        width: '15%'
    },
    {
        title: '默认值',
        dataIndex: 'value',
        width: '25%'
    },
    {
        title: 'required',
        dataIndex: 'required',
        width: '15%'
    },

    {
        title: 'placeholder',
        dataIndex: 'placeholder',
        width: '25%'
    },
    {
        title: 'description',
        dataIndex: 'description',
        width: '25%'
    },
    {
        title: '操作',
        width: '10%',
        dataIndex: 'operation'
    }
]

const props = defineProps(['options'])
const dataSource = ref<any[]>(JSON.parse(props.options || '[]'))
const emit = defineEmits(['update'])

watch(dataSource.value, (arr) => {
    console.log('arr', arr)
    emit('update', JSON.stringify(arr))
})

const add = () => {
    dataSource.value.push({
        id: Math.floor(Math.random() * 100000000).toString(),
        name: '',
        title: '',
        value: '',
        required: true,
        type: 'Input',
        placeholder: '',
        description: '',
        dataType: 'string'
    })
}
const cancel = (id: string) => {
    dataSource.value = dataSource.value.filter((o) => o.id !== id)
    emit('update', JSON.stringify(dataSource.value))
}
</script>
<style scoped>
.editable-row-operations a {
    margin-right: 8px;
}
</style>
