<template>
    <div class="pl-[20px] w-[600px]">
        <div class="flex justify-between">
            <div class="text-[20px]">
                <BugOutlined />
                <span class="ml-[8px]">调试</span>
            </div>
        </div>

        <a-card size="small" class="mt-[10px]">
            <template #title>
                <div class="p-[15px]">自定义字段配置</div>
            </template>
            <a-collapse class="mt-[10px]" ghost v-model:activeKey="activeKey" collapsible="header">
                <a-collapse-panel key="header" header="自定义请求头">
                    <a-form layout="inline" v-if="customHeaderForm.length">
                        <div class="flex mb-[10px]" v-for="(item, idx) in customHeaderForm" :key="item.id">
                            <a-form-item label="key">
                                <a-input v-model:value="item.key" class="w-[168px]" allow-clear></a-input>
                            </a-form-item>
                            <a-form-item label="value">
                                <a-input v-model:value="item.value" class="w-[168px]" allow-clear></a-input>
                            </a-form-item>
                            <a-button @click="addCustomHeader" type="primary" class="mr-[10px]" shape="circle" :icon="h(PlusOutlined)" />
                            <a-button @click="deleteCustomHeader(idx)" danger type="primary" shape="circle" :icon="h(MinusOutlined)" />
                        </div>
                    </a-form>
                    <a-button type="primary" :icon="h(PlusOutlined)" v-else @click="addCustomHeader">添加</a-button>
                </a-collapse-panel>
            </a-collapse>
            <a-collapse class="mt-[10px]" ghost v-model:activeKey="activeKey" collapsible="query">
                <a-collapse-panel key="query" header="自定义云函数 env">
                    <a-form layout="inline" v-if="customQueryForm.length">
                        <div class="flex mb-[10px]" v-for="(item, idx) in customQueryForm" :key="item.id">
                            <a-form-item label="key">
                                <a-input v-model:value="item.key" class="w-[168px]" allow-clear></a-input>
                            </a-form-item>
                            <a-form-item label="value">
                                <a-input v-model:value="item.value" class="w-[168px]" allow-clear></a-input>
                            </a-form-item>
                            <a-button @click="addCustomQuery" type="primary" class="mr-[10px]" shape="circle" :icon="h(PlusOutlined)" />
                            <a-button @click="deleteCustomQuery(idx)" danger type="primary" shape="circle" :icon="h(MinusOutlined)" />
                        </div>
                    </a-form>
                    <a-button type="primary" :icon="h(PlusOutlined)" v-else @click="addCustomQuery">添加</a-button>
                </a-collapse-panel>
            </a-collapse>
        </a-card>

        <div class="mt-[5px]">
            <a-card class="mt-[10px]" :title="tool.name">
                <div class="mt-[6px] mb-[20px]" style="color: var(--color-symbol-bold)">
                    {{ tool.description }}
                </div>

                <a-form :model="form" autocomplete="off" layout="vertical">
                    <a-form-item
                        v-for="item in toolInputSchemaProperties2Array"
                        :key="item.name"
                        :label="item.name"
                        v-bind="validateInfos[item.name]"
                    >
                        <a-textarea v-model:value="form[item.name]" :placeholder="item.description" allow-clear> </a-textarea>
                    </a-form-item>
                </a-form>

                <a-button @click="run" type="primary" :icon="h(SendOutlined)" :loading="callToolLoading">运行工具</a-button>

                <div v-if="callResult">
                    <div class="mt-[18px] text-[16px] font-bold flex">
                        <div>响应：</div>
                        <div v-if="callResult.isError" class="text-[#DC2626]">Error</div>
                        <div v-else class="text-[#16A34A]">Success</div>
                    </div>

                    <div
                        class="mt-[12px]"
                        v-for="(item, idx) in callResult.content"
                        :key="`${idx}__${uuid()}`"
                        :style="{ color: callResult.isError ? '#DC2626' : '#16A34A' }"
                    >
                        {{
                            // @ts-ignore
                            item.text
                        }}
                    </div>
                </div>
            </a-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { BugOutlined, SendOutlined, PlusOutlined, MinusOutlined } from '@ant-design/icons-vue'
import { Form, notification } from 'ant-design-vue'
import { type Client } from '@modelcontextprotocol/sdk/client/index.js'
import { connectMcp } from '@/utils/connectMcp'
import { uuid } from '@/utils/uuid'

type PromiseValue<T extends Promise<unknown>> = T extends Promise<infer U> ? U : never
type Tool = PromiseValue<ReturnType<Client['listTools']>>['tools'][number]
type InputSchemaArrayProps = {
    name: string
    description: string
    type: string
    required: boolean
}[]
type CallResult = PromiseValue<ReturnType<Client['callTool']>>
interface IProps {
    tool: Tool
    transportType: 'sse' | 'streamable-http'
    url: string
    createType: 'copilot' | 'external'
}

const props = defineProps<IProps>()

const activeKey = ref<('header' | 'query')[]>([])

const toolInputSchemaProperties2Array = computed(() => {
    const { properties } = props.tool.inputSchema
    if (properties == null) return []
    return Object.keys(properties).reduce((arr, key) => {
        const { description, type } = properties[key] as Record<string, any>
        arr.push({
            name: key,
            description,
            type,
            required: ((props.tool.inputSchema.required as string[] | undefined) || []).some((_key) => _key === key)
        })
        return arr
    }, [] as InputSchemaArrayProps)
})

const form = ref<Record<string, string>>({})

const customQueryForm = ref<Record<'key' | 'value' | 'id', string>[]>([])

const customHeaderForm = ref<Record<'key' | 'value' | 'id', string>[]>([])

const rules = computed(() =>
    toolInputSchemaProperties2Array.value.reduce((rules, { name, required }) => {
        rules[name] = [{ required }]
        return rules
    }, {} as Record<string, any>)
)

const { validate, validateInfos } = Form.useForm(form.value, rules.value)

watch(
    () => props.tool,
    () => {
        form.value = {}
        callResult.value = void 0
    }
)

const callResult = ref<CallResult>()

const callToolLoading = ref(false)

const addCustomQuery = () => {
    customQueryForm.value.push({ key: '', value: '', id: uuid() })
}

const deleteCustomQuery = (idx: number) => {
    customQueryForm.value.splice(idx)
}

const addCustomHeader = () => {
    customHeaderForm.value.push({ key: '', value: '', id: uuid() })
}

const deleteCustomHeader = (idx: number) => {
    customHeaderForm.value.splice(idx)
}

const run = async () => {
    await validate()

    const customHeaders = customHeaderForm.value.reduce((headers, { key, value }) => {
        if (key && value) {
            Reflect.set(headers, key, value)
        }
        return headers
    }, {})

    const url = new URL(props.url)
    customQueryForm.value.forEach((query) => {
        if (query.key && query.value) url.searchParams.append(query.key, query.value)
    })

    const customHeaderKeys = Object.keys(customHeaders)

    if (props.createType === 'external' && customHeaderKeys.length > 0) {
        url.searchParams.append('__MCP__CUSTOM__HEADER_KEYS__', customHeaderKeys.join(','))
    }

    try {
        const client = await connectMcp(url.href, props.transportType, customHeaders)
        callToolLoading.value = true
        callResult.value = await client.callTool({
            name: props.tool.name,
            arguments: form.value
        })
        await client.close()
    } catch (err: any) {
        notification.error({
            placement: 'bottomRight',
            message: err.message
        })
    } finally {
        callToolLoading.value = false
    }
}
</script>
