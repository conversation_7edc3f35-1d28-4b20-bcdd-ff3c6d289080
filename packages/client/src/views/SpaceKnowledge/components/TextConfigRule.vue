<template>
    <a-radio-group v-model:value="rule.formatType" class="flex flex-col mt-4">
        <a-radio :value="0" class="radio radio-bottom flex" :class="rule.formatType === 0 && 'active'">
            <div class="text-[#000] font-bold pl-2">自动分段和清洗（推荐）</div>
            <div class="text-[#999] pl-2">自动分段与预处理规则</div>
        </a-radio>
        <a-radio :value="1" class="radio radio-bottom flex mt-4" :class="rule.formatType === 1 && 'active'">
            <div class="text-[#000] font-bold pl-2">自定义</div>
            <div class="text-[#999] pl-2">自定义分段规则、分段长度及预处理规则</div>
            <template v-if="rule.formatType !== 0">
                <a-divider />
                <a-form
                    :model="rule"
                    name="basic"
                    :label-col="{ span: 24 }"
                    :wrapper-col="{ span: 24 }"
                    autocomplete="off"
                    style="width: 600px; margin-left: 5px"
                    ref="chunkForm"
                    :onClick="
                        (e: Event) => {
                            e.preventDefault()
                            e.stopPropagation()
                        }
                    "
                >
                    <a-form-item name="language" class="w-full" :rules="[{ required: true, message: '请选择文本格式' }]">
                        <template #label>
                            <div>文本格式</div>
                        </template>
                        <a-select v-model:value="rule.language" placeholder="请选择文本格式">
                            <a-select-option v-for="item in codeLanguage" :key="item" :value="item">
                                {{ item }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item
                        name="chunkSize"
                        :rules="[{ required: true, message: '请输入分段最大长度', type: 'number' }, { validator: validateMin }]"
                    >
                        <template #label>
                            分段最大长度
                            <span class="text-xs text-[#999] m-2">(不建议小于100)</span>
                        </template>
                        <a-input-number class="w-[600px]" id="inputNumber" v-model:value="rule.chunkSize" :max="10000"> </a-input-number>
                    </a-form-item>
                    <a-form-item
                        name="chunkOverlap"
                        :rules="[{ required: true, type: 'number', message: '请输入数字' }, { validator: validatePercent }]"
                    >
                        <template #label>
                            <div>
                                分段重叠长度
                                <span class="text-xs text-[#999] m-2">(可以提升检索效果，建议设置最大长度的20%)</span>
                                <a-tooltip title="通常建议设置为分段长度的10-20%，对于信息密集度高的文本（如代码、学术文章）可设置为20-30%">
                                    <info-circle-outlined style="color: rgba(0, 0, 0, 0.75)" />
                                </a-tooltip>
                            </div>
                        </template>
                        <a-input-number class="w-[600px]" id="inputNumber" v-model:value="rule.chunkOverlap" :min="0" :max="10000">
                        </a-input-number>
                    </a-form-item>
                </a-form>
            </template>
        </a-radio>
    </a-radio-group>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { notification } from 'ant-design-vue'
import { useRoute } from 'vue-router'

const rule = reactive({
    formatType: 0, // 1：custom  0 auto
    chunkSize: 500,
    chunkOverlap: 100,
    language: ''
})

const codeLanguage = [
    'text',
    'markdown',
    'html',
    'cpp',
    'go',
    'java',
    'js',
    'php',
    'proto',
    'python',
    'rst',
    'ruby',
    'rust',
    'scala',
    'swift',
    'latex',
    'sol'
]

const chunkForm = ref()
const route = useRoute()
switch (route.query.type) {
    case 'localDoc':
        rule.language = 'text'
        break
    case 'infoDoc':
        rule.language = 'markdown'
        break
    case 'urlDoc':
        rule.language = 'markdown'
        break
    case 'manualImportDoc':
        rule.language = 'markdown'
        break
}

const getTextConfigRule = async () => {
    if (rule.formatType === 1) {
        try {
            await chunkForm.value.validate()
            return rule
        } catch {
            notification.error({
                message: '校验失败',
                description: '请检查自定义规则'
            })
            return
        }
    } else {
        return rule
    }
}

const validatePercent = (rules: string, value: number) => {
    if (value / Number(rule.chunkSize) > 0.3) {
        return Promise.reject('分段重叠长度不得超过分端最大长度的30%')
    } else {
        return Promise.resolve()
    }
}

const validateMin = (rules: string, value: number) => {
    if (Number(value) <= 50) {
        return Promise.reject('分段最大长度不得低于50')
    } else {
        return Promise.resolve()
    }
}

defineExpose({ getTextConfigRule })
</script>
<style scoped>
.radio {
    min-height: 80px;
    width: 100%;
    border: 1px solid #cccccc;
    padding: 10px;
    border-radius: 8px;
}

.radio-bottom >>> .ant-radio {
    align-self: flex-start;
    padding-top: 16px;
}

.radio:hover,
.radio.active {
    border: 1px solid #1677ff;
    background-color: #e6f4ff;
}

.radio >>> .ant-form-item-label {
    padding-bottom: 4px;
}

.ant-form-item {
    margin-bottom: 10px;
}
:deep(:where(.css-dev-only-do-not-override-12qsla4).ant-radio-wrapper span.ant-radio + *) {
    width: 100%;
}
:deep(.chunk_overlap_label label) {
    height: 60px;
}
</style>
