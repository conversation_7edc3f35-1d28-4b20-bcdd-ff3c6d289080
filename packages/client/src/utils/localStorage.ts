/**
 * 将数据保存到localStorage中，使用指定的id作为键。
 *
 * @param {string} id - 用于存储数据的唯一标识符。
 * @param {*} data - 要存储的数据，可以是任何可被JSON序列化的对象。
 */
export function saveDataToLocalStorage(id:string, data:any) {
    try {
        // 使用id作为键，data作为值进行存储
        localStorage.setItem(id, JSON.stringify(data));
    } catch (e) {
        throw Error('localStorage 数据保存失败')
    }
}

/**
 * 从localStorage中根据id获取数据。
 *
 * @param {string} id - 用于检索数据的标识符。
 * @returns {*} - 返回与id关联的数据对象，如果没有找到则返回null。
 */
export function getDataFromLocalStorage(id:string,defaultValue='') {
    try {
        // 通过id获取存储的数据
        const data = localStorage.getItem(id);
        if (data) {
            // 使用JSON.parse将字符串转换回对象
            return JSON.parse(data);
        } else {
            return defaultValue;
        }
    } catch (e) {
        return defaultValue;
    }
}
/**
 * 根据指定的ID从localStorage中清除数据。
 *
 * @param {string} id - 要从localStorage中移除数据的标识符。
 * @returns {boolean} - 始终返回true，表示清除操作已完成（不论数据是否存在都会尝试清除）。
 */
export function clearDataFromLocalStorage(id:string) {
    try {
        localStorage.removeItem(id);
        return true;
    } catch (e) {
        return false;
    }
}
