<template>
    <a-modal v-model:open="open" title="切片诊断" width="80%" :footer="null">
        <a-table
            :loading="loading"
            :dataSource="dataSource"
            :columns="columns"
            class="copilot-concise-table"
            :scroll="{ y: '60vh' }"
            :pagination="pagination"
            @change="dataSourceChange"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'operate'">
                    <a-button type="link" @click="onSliceById(record)">诊断</a-button>
                </template>
            </template>
        </a-table>
    </a-modal>
</template>

<script setup lang="ts">
import { computed, ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { sliceDiagnose, type ISliceDiagnose } from '@/api/knowledge'

const props = defineProps<{ visible: boolean }>()
const emits = defineEmits(['update:visible'])

const dataSource = ref<ISliceDiagnose[]>([])

const loading = ref(false)

const open = computed({
    get: () => {
        return props.visible
    },
    set: (val) => {
        emits('update:visible', val)
    }
})

const columns = [
    {
        title: 'ID',
        key: 'id',
        dataIndex: 'id',
        width: 200
    },
    {
        title: '切片内容',
        key: 'content',
        dataIndex: 'content',
        width: 400,
        ellipsis: true
    },
    {
        title: '诊断描述',
        key: 'desc',
        dataIndex: 'desc',
        width: 200
    },
    {
        title: '操作',
        key: 'operate',
        dataIndex: 'operate',
        width: 100
    }
]

const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 20
})
const router = useRouter()
const route = useRoute()

const dataSourceChange = async (pag: { pageSize: number; current: number }) => {
    loading.value = true
    Object.assign(pagination, pag)
    const { datasetId } = route.params
    const { data } = await sliceDiagnose({ datasetId: String(datasetId), page: pag.current, pageSize: pag.pageSize })
    dataSource.value = data.rows
    pagination.total = data.count
    loading.value = false
}

const onSliceById = (record: Record<string, string | number>) => {
    const { spaceId } = route.params
    router.push(`/space/${spaceId}/knowledge/${record.datasetId}/detail/${record.knowledgeId}?id=${record.id}`)
}

onMounted(() => {
    dataSourceChange({ current: pagination.current, pageSize: pagination.pageSize })
})
</script>

<style scoped></style>
