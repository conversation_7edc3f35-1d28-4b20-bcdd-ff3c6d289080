{"name": "client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:uat": "vite build --mode uat", "build:pre": "vite build --mode pre", "build:prod": "vite build --mode prod", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@heroicons/vue": "^2.1.3", "ant-design-vue": "4.x", "axios": "^1.6.8", "cherry-markdown": "^0.8.46", "clipboard": "^2.0.11", "dayjs": "^1.11.10", "fuse.js": "^7.0.0", "js-cookie": "^3.0.5", "marked": "^14.0.0", "monaco-editor": "^0.48.0", "pinia": "^2.1.7", "qs": "^6.13.0", "sass": "^1.77.4", "splitpanes": "^3.1.5", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-json-viewer": "3", "vue-router": "^4.3.0", "vue3-ts-jsoneditor": "2.11.2", "@modelcontextprotocol/sdk": "1.10.1"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node20": "^20.1.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.11.25", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.18", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "npm-run-all2": "^6.1.2", "postcss": "^8.4.36", "prettier": "^3.0.3", "rollup-plugin-copy": "^3.5.0", "tailwindcss": "^3.4.1", "typescript": "~5.4.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.5", "vite-bundle-analyzer": "^0.9.4", "vite-plugin-monaco-editor": "^1.1.0", "vue-tsc": "^2.0.6"}}