export const apiType = `
declare interface IYuumi {
    http<any>(option: IYuumiOption): Promise<any>;
    grpc<any>(option: IYuumiOption): Promise<any>;
}
declare interface IYuumiOption {
    appId: string;
    path: string;
    params: Record<string, any>;
    method?: 'POST' | 'GET';
    color?: string;
    cookie?: string;
    timeout?: number;
    traceId?: string;
}
declare interface IPublisher {
    topic: string;
    group: string;
    appId: string;
    token: string;
    message: {
        key: string;
        content: object;
    };
}
/**
 * 工程维护方 @豆腐花 @小卫
 * @param option
 */
declare class Publisher {
    topic: string;
    group: string;
    appId: string;
    token: string;
    constructor({ topic, group, appId, token }: IPublisher);
    pub(message: {
        key: string;
        content: object;
    }): Promise<any>;
}
    
// 创建yuumi对象，模拟实现IYuumi接口
const $yuumi: IYuumi;
const publisher = new Publisher();
const $cookie = '当前用户cookie';
const $schema = {};
const $flow = {
    input: string,
    env: Record<string, any>,
}
declare const $biliIndex: {
  invoke(command: string | any[], options?: Record<string, any>): Promise<any>;
};

declare const $chatgpt: {
  invoke(command: string | any[], options?: Record<string, any>): Promise<any>;
};

declare const $deepseek: {
  invoke(command: string | any[], options?: Record<string, any>): Promise<any>;
};

declare const $json2MarkdownTable: (json: object[]) => string;
`
