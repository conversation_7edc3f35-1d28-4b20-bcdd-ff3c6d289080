<template>
    <MCPMarketHeader
        v-model:search-keyword="keyword"
        titile="MCP 市场"
        description="所有公开的MCP都可在AI编排里使用"
        @search-input-press-enter="onSearch"
    />

    <InfiniteScroll :end="end" @loadMoreFn="getData" ref="infiniteScrollRef">
        <MCPMarketList :data="data" />
    </InfiniteScroll>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { getMCPList } from '@/api/mcp'
// @ts-ignore
import InfiniteScroll from '@/components/InfiniteScroll.vue'
import MCPMarketHeader from './components/MCPMarketHeader.vue'
import MCPMarketList from './components/MCPMarketList.vue'
import type { IMCPInfo } from '@/interface/mcp'

const end = ref(false)

const keyword = ref('')

const infiniteScrollRef = ref()

const data = ref<IMCPInfo[]>([])

const getData = async (keyword?: string) => {
    const res = await getMCPList({ keyword })
    data.value.push(...res.data.rows)

    if (data.value.length === res.data.count) {
        end.value = true
        return
    }

    await nextTick()

    const container = infiniteScrollRef.value.container

    // 如果内容高度不足以触发滚动，继续加载更多数据
    if (container.scrollHeight - container.clientHeight < 20) {
        getData(keyword)
    }
}

const onSearch = () => {
    end.value = false
    data.value = []
    getData(keyword.value)
}
</script>
