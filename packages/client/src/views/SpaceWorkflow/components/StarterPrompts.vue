<template>
    <a-modal
        v-model:open="open"
        :mask-closable="false"
        destroy-on-close
        title="设置提示语"
        ok-text="提交"
        cancel-text="取消"
        @ok="handleSubmit"
    >
        <a-alert class="mb-[20px]" message="提示语只会在聊天中没有消息时显示" type="info" />
        <a-form :wrapper-col="{ span: 18 }" :label-col="{ style: { width: '98px' } }">
            <a-form-item v-for="(item, index) in formData" :label="`提示语${index + 1}`" :key="item.key">
                <a-input v-model:value="item.prompt" class="w-[300px]" />
                <MinusCircleOutlined class="text-red-600 ml-[10px]" @click="handleRemove(item)" />
            </a-form-item>
            <a-form-item :wrapper-col="{ span: 20, offset: 5 }">
                <a-button type="dashed" class="w-[300px]" @click="handleAdd">
                    <PlusOutlined />
                    添加
                </a-button>
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, defineExpose } from 'vue'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { updateChatFlowV1 } from '@/api/space'
import { message } from 'ant-design-vue'

const open = ref(false)

const formData = ref([{ prompt: '', key: 1 }])

const dataTemp = ref<any>({})

const handleOpen = (data: any) => {
    const { chatbotConfig: chatbotConfigStr } = data

    dataTemp.value = data

    if (chatbotConfigStr !== '') {
        try {
            const chatbotConfig = JSON.parse(chatbotConfigStr)

            if (chatbotConfig.starterPrompts) {
                const data = [] as any
                for (let [key, item] of Object.entries(chatbotConfig.starterPrompts)) {
                    // @ts-ignore
                    data.push({ ...item, key })
                }
                if (data.length > 0) {
                    formData.value = data
                }
            }
        } catch (e) {
            console.log(e)
        }
    }

    open.value = true
}

const handleRemove = (item: any) => {
    const itx = formData.value.indexOf(item)

    if (itx > 0) {
        formData.value.splice(itx, 1)
    } else if (itx == 0) {
        formData.value[0].prompt = ''
    }
}

const handleAdd = () => {
    formData.value.push({ prompt: '', key: Date.now() })
}

const handleSubmit = async () => {
    const data = {} as any
    formData.value.map((item, index) => {
        if (item.prompt != '') {
            data[index] = { prompt: item.prompt }
        }
    })

    await updateChatFlowV1(dataTemp.value.id, { chatbotConfig: JSON.stringify({ starterPrompts: data }) })

    message.success('设置成功!')
    open.value = false
}

defineExpose({ open: handleOpen })
</script>
