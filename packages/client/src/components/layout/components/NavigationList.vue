<template>
    <a-flex class="h-full" vertical>
        <a-menu class="!border-0" v-model:selectedKeys="selectedKeys" mode="inline" @click="handleClick">
            <a-menu-item key="home" :icon="h(HomeIcon, { class: 'h-4' })"> 首页 </a-menu-item>
            <a-menu-item v-if="personalSpace.id" :key="personalSpace.id" :icon="h(UserOutlined)"> {{ personalSpace.title }} </a-menu-item>
            <a-divider style="border-color: var(--color-symbol-light)" />
            <div class="relative">
                <a-menu-item-group title="探索" />
            </div>
            <a-menu-item key="market" :icon="h(ShoppingOutlined)"> Bot 市场 </a-menu-item>
            <a-menu-item key="plugin" :icon="h(CubeIcon, { class: 'h-4' })"> 插件市场 </a-menu-item>
            <a-menu-item key="mcp" :icon="h(ApiOutlined, { class: 'h-4' })"> MCP市场 </a-menu-item>
            <a-menu-item key="hotspots" :icon="h(LightBulbIcon, { class: 'h-4' })"> 热点洞察 </a-menu-item>
            <a-divider style="border-color: var(--color-symbol-light)" />

            <div class="relative">
                <a-menu-item-group title="团队空间" />
                <PlusOutlined class="absolute right-[20px] top-[13px]" @click="addSpaceHandle.open" />
            </div>
        </a-menu>

        <a-menu class="!border-0 flex-1 overflow-y-auto" v-model:selectedKeys="selectedKeys" mode="inline" @click="handleClick">
            <a-menu-item v-for="item in teamSpace" :key="item.id" :icon="h(TeamOutlined)">
                {{ item.title }}
            </a-menu-item>
        </a-menu>
    </a-flex>

    <a-modal v-model:open="addSpace.open" title="创建新团队" @cancel="addSpaceHandle.cancel" :mask-closable="false">
        通过创建团队，将支持工作流和知识库在团队内进行协作和共享。
        <div class="flex justify-center pt-[20px]">
            <a-avatar shape="square" class="bg-[#2AC864]" :size="100">
                <template #icon>
                    <TeamOutlined />
                </template>
            </a-avatar>
        </div>
        <a-form :model="addSpace.data" layout="vertical" class="pt-[10px]" autocomplete="off">
            <a-form-item label="团队名称" name="title" :rules="{ required: true }">
                <a-input placeholder="请输入团队名称" v-model:value="addSpace.data.title" />
            </a-form-item>
            <a-form-item label="描述团队">
                <a-textarea placeholder="描述团队" v-model:value="addSpace.data.description" />
            </a-form-item>
        </a-form>

        <template #footer>
            <a-button @click="addSpaceHandle.cancel">取消</a-button>
            <a-button type="primary" @click="addSpaceHandle.submit" :disabled="!addSpace.data.title">提交</a-button>
        </template>
    </a-modal>
</template>
<script lang="ts" setup>
import { ref, watch, h, toRefs, reactive, nextTick } from 'vue'
import { UserOutlined, TeamOutlined, PlusOutlined, ShoppingOutlined } from '@ant-design/icons-vue'
import { CubeIcon, HomeIcon, LightBulbIcon } from '@heroicons/vue/24/outline'
import { useSpaceStore } from '@/stores/space'
import type { MenuProps } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ApiOutlined } from '@ant-design/icons-vue'

const spaceStore = useSpaceStore()

const route = useRoute()
const router = useRouter()

const { personalSpace, teamSpace, addSpace: _addSpace } = toRefs(spaceStore)

const selectedKeys = ref([route.params?.spaceId || route.name])

const handleClick: MenuProps['onClick'] = (e) => {
    switch (e.key) {
        case 'home':
            router.push(`/home`)
            break
        case 'market':
            router.push(`/market`)
            break
        case 'plugin':
            router.push(`/plugin`)
            break
        case 'mcp':
            router.push(`/mcp/market`)
            break
        case 'hotspots':
            router.push(`/hotspots`)
            break
        default:
            router.push(`/space/${e.key}/workflow`)
    }
}

watch(route, () => {
    selectedKeys.value = [route.params?.spaceId || route.name]
})

/* add space */
const addSpace = reactive({
    open: false,
    data: {
        title: '',
        description: ''
    }
})

const addSpaceHandle = {
    open() {
        addSpace.open = true
    },
    async submit() {
        const { data } = await _addSpace.value(addSpace.data)
        message.success('团队添加成功')
        addSpace.open = false
        await nextTick(() => {
            router.push(`/space/${data.id}/workflow`)
        })
    },
    cancel() {
        addSpace.open = false
    }
}
</script>
