<template>
    <div class="chat-window">
        <div class="h-full flex">
            <div class="fixed top-0 left-0 z-10 w-full bg-white" v-if="route.query.pure != '1'">
                <!-- notice -->
                <div v-if="notice" class="relative group -mb-3 z-50">
                    <div
                        class="p-1 bg-[#FFF4D6] text-neutral-700 text-xs whitespace-nowrap overflow-hidden text-ellipsis w-full lg:text-center text-left"
                    >
                        {{ notice }}
                    </div>
                    <!-- Tooltip -->
                    <div
                        class="absolute w-full top-full left-0 translate-y-[-4px] text-center bg-black text-white text-sm px-2 py-1 rounded opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity duration-300"
                    >
                        {{ notice }}
                    </div>
                </div>

                <div class="p-2 pb-0 flex">
                    <a-tooltip placement="bottomLeft">
                        <template #title>访问 copilot.bilibili.co</template>
                        <a-button class="align-middle" type="text" size="large" @click="router.push({ path: '/' })">
                            <template #icon>
                                <HomeIcon class="h-5 w-5 text-[#7D7D7D]" />
                            </template>
                        </a-button>
                    </a-tooltip>
                    <a-tooltip placement="bottomLeft">
                        <template #title>新聊天</template>
                        <a-button class="align-middle" type="text" size="large" @click="handleNew">
                            <template #icon>
                                <PencilSquareIcon class="h-5 w-5 text-[#7D7D7D]" />
                            </template>
                        </a-button>
                    </a-tooltip>
                    <a-dropdown :trigger="['click']">
                        <template #overlay>
                            <a-menu @click="onMenuClick">
                                <a-menu-item key="about" class="!p-3">
                                    <InfoCircleOutlined class="mr-3" />
                                    关于
                                </a-menu-item>
                                <a-menu-item key="copy" class="!p-3">
                                    <LinkOutlined class="mr-3" />
                                    复制链接
                                </a-menu-item>
                            </a-menu>
                        </template>
                        <a-button
                            type="text"
                            class="top-operate text-[#7D7D7D] bg-[#fff] font-semibold text-[16px] px-3 py-1.5 h-[40px] border-0 shadow-none flex items-center"
                        >
                            {{ name }}
                            <ChevronDownIcon class="h-5 w-5" />
                        </a-button>
                    </a-dropdown>
                </div>
            </div>
            <div class="max-w-3xl w-full mx-auto">
                <b-copilot-chat v-if="props.loading" ref="bCopilotChat"></b-copilot-chat>
            </div>
        </div>
        <a-modal v-model:open="open" :footer="null">
            <div class="flex flex-col items-center gap-4 px-3 py-10">
                <a-avatar v-if="props.avatar" :src="props.avatar" :size="66" />
                <a-avatar v-if="!props.avatar" class="mt-8" :size="66">
                    <template #icon>
                        <UserOutlined />
                    </template>
                </a-avatar>
                <div class="text-lg">{{ props.name }}</div>
                <div class="text-[#B4B4B4] text-xs">
                    创建者: <span class="mx-1">{{ props.creator }}</span
                    ><UserOutlined />
                </div>
                <div class="text-sm text-center break-all">{{ props.description }}</div>
                <div class="relative mt-6">
                    <div class="flex justify-center items-center flex-wrap gap-3">
                        <span class="node-list text-md">插件:</span>
                        <a-tooltip v-for="item in props.nodes" :key="item.src">
                            <template #title>{{ item.name }}</template>
                            <a-avatar class="rounded-full bg-white p-[4px] cursor-help" style="border: 1px solid #ececec" :src="item.src" />
                        </a-tooltip>
                    </div>
                </div>
                <div class="mt-3">
                    <a-space direction="vertical">
                        <a-button class="button-frosted" type="primary" @click="router.push({ path: '/' })"
                            >定制属于你自己的 AI Bot</a-button
                        >
                    </a-space>
                </div>
                <div class="text-[#B4B4B4] text-xs">chatid: {{ currentChatId }}</div>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { defineProps, ref, defineEmits, nextTick, watch, onMounted, onBeforeUnmount } from 'vue'
import type { PropType } from 'vue'
import { InfoCircleOutlined, LinkOutlined, UserOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { copyToClipboard } from '@/utils'
import type { MenuProps } from 'ant-design-vue'
import { HomeIcon, PencilSquareIcon, ChevronDownIcon } from '@heroicons/vue/24/outline'
import { useRoute, useRouter } from 'vue-router'
import { getNoticeKv } from '@/api/chat'

export interface ChatNode {
    name: string
    src?: string
    avatar?: string
    id?: string
    description?: string
}

const route = useRoute()
const router = useRouter()
const open = ref(false)

const bCopilotChat = ref<any>(null)
const props = defineProps({
    chatflowid: {
        type: String
    },
    name: {
        type: String
    },
    nodes: {
        type: Array as PropType<ChatNode[]>,
        default() {
            return []
        }
    },
    description: {
        type: String
    },
    createdDate: {
        type: String
    },
    creator: {
        type: String
    },
    apiHost: {
        type: String
    },
    avatar: {
        type: String
    },
    loading: {
        type: Boolean
    },
    mentionList: {
        type: Array
    },
    type: {
        type: String
    },
    initialMessage: {
        type: String,
        required: false
    }
})

watch(
    () => props,
    (newProps) => {
        nextTick(() => {
            if (bCopilotChat.value) {
                resetChatBot(newProps)
            }
        })
    },
    { deep: true }
)

const currentChatId = ref('')

const getChatBotConfig = (newValue: typeof props) => {
    return {
        chatflowid: newValue.chatflowid,
        apiHost: newValue.apiHost,
        theme: {
            chatWindow: {
                fontSize: 14,
                showAgentMessages: newValue.type === 'MULTIAGENT' || newValue.type === 'AGENTFLOW',
                welcomeMessage: '您好，请问遇到了什么问题？',
                showTitle: false,
                botMessage: {
                    showAvatar: true,
                    avatarSrc:
                        newValue.avatar || 'https://shjd-inner-boss.bilibili.co/copilot/6134103429_Copilot%20LOGO%E5%9B%BE%E6%A0%87.png'
                },
                showMentionPop: newValue.mentionList && newValue?.mentionList.length > 0,
                // 可提及的插件列表
                initialMentionList: newValue.mentionList,
                textInput: {
                    placeholder: '发送消息' + (newValue.type !== 'MULTIAGENT' && newValue.mentionList?.length ? '，输入@来指定插件' : '')
                }
            }
        },
        initialMessage: newValue.initialMessage,
        observersConfig: {
            observeChatId: (chatId: string) => {
                currentChatId.value = chatId
            }
        }
    }
}

// 更新bChatBot
const resetChatBot = (newValue: typeof props) => {
    const config = getChatBotConfig(newValue)
    bCopilotChat.value.chatflowid = config.chatflowid
    bCopilotChat.value.apiHost = config.apiHost
    bCopilotChat.value.theme = config.theme
    bCopilotChat.value.initialMessage = config.initialMessage
    bCopilotChat.value.observersConfig = config.observersConfig
}

const emit = defineEmits(['toHome', 'toSpace'])

const onMenuClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
        case 'about':
            open.value = true
            break
        case 'copy':
            handleShare()
            break
    }
}

const handleNew = () => {
    const event = new CustomEvent('copilot-clear-chat', { bubbles: true, composed: true })
    document.dispatchEvent(event)
}
const handleShare = () => {
    copyToClipboard(window.location.href)
    message.success(`已复制链接，分享给好友吧！`)
}

const onMessage = (event: any) => {
    if (event.data && event.origin === 'https://lego.bilibili.co') {
        // 处理localtostrage
        const id = route.params.id
        const sessionId = event.data
        const localData = localStorage.getItem(id + '_EXTERNAL') || ''
        const _localData = JSON.parse(localData)
        _localData.chatHistory = _localData.chatHistory.map((item: any) => {
            const regex = new RegExp(`<iframe[^>]*sessionId=${sessionId}[^>]*><\\/iframe>`, 'gi')
            const href = `//${
                window.location.host.includes('uat-') ? 'uat-' : ''
            }live-workflow.bilibili.co/#/approval-detail?flow_id=${sessionId}`
            item.message = item.message.replace(
                regex,
                `<div>已为您发起申请，查看详情可前往id：<a target="_blank" href="${href}">${sessionId}</a></div>`
            )
            return item
        })

        localStorage.setItem(id + '_EXTERNAL', JSON.stringify(_localData))
    }
}
const notice = ref('')

onMounted(async () => {
    if (props.loading) {
        resetChatBot(props)
    }

    try {
        const res = await getNoticeKv()
        const data = JSON.parse(res.list[0].value)
        notice.value = data.notice || ''
    } catch {
        notice.value = ''
    }
    // 监听来自 iframe 的消息
    window.addEventListener('message', onMessage)
})

onBeforeUnmount(() => {
    window.removeEventListener('message', onMessage)
})
</script>
<style lang="less">
.chat-window {
    width: 100%;
    height: 100%;
    background: #fff;
    .left {
        border-right: 1px solid #eee;
        width: 360px;
        min-width: 360px;
        background: -webkit-linear-gradient(-45deg, #766dff 0%, #88f3ff 100%);
        .footer {
            position: absolute;
            left: 50%;
            bottom: 36px;
            transform: translateX(-50%);
        }
    }
    .right {
        flex-grow: 1;
        height: 100%;
    }
    .node-list {
        // position: absolute;
        position: relative;
        // left: 4px;
        top: 8px;
    }
    .description {
        font-size: 14px;
    }
}
.button-frosted {
    background-color: #f9f9f9;
    color: #7d7d7d;
    border: 0;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); /* 添加按下时的阴影效果 */
}

.button-frosted:active {
    transform: translateY(2px); /* 当按钮被按下时，稍微向下移动 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 添加按下时的阴影效果 */
}

/* 可选：为按钮添加hover效果 */
.ant-btn.button-frosted:hover {
    color: #7d7d7d;
    background-color: #f9f9f9;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 添加按下时的阴影效果 */
}
.ant-btn-text:not(:disabled):hover {
    color: #7d7d7d;
    background-color: #f9f9f9;
}
</style>
