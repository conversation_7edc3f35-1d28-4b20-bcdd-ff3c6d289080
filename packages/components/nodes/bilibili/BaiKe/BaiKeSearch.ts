import { INode, INodeData, INodeParams } from '../../../src/Interface'
import { Tool, ToolParams } from '@langchain/core/tools'
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager'
import { BaiKeSearchResponse, BaiKeSummaryResponse, getBaiKes, searchBaiKe } from './api'

interface BaiKeSearchArgs extends ToolParams {
    datasetId: string
}

class BaiKeSearch_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'BaiKeSearch'
        this.name = 'baiKeSearch'
        this.version = 1.0
        this.type = 'CustomTool'
        this.icon = 'baike.png'
        this.category = 'Tools'
        this.description = 'BaiKe搜索工具，返回tapd内容摘要'
        this.baseClasses = [this.type, 'Tool']
        this.tags = ['bilibili']
    }

    async init(nodeData: INodeData): Promise<any> {
        const datasetId = 'da950783304476446087'
        return new BaiKeSearchTool({ datasetId })
    }
}

class BaiKeSearchTool extends Tool {
    static lc_name() {
        return 'BaiKeSearch'
    }

    name = 'baike-search'
    description = 'Useful for searching content in BaiKe. Input should be the search keyword.'

    private datasetId: string

    constructor({ datasetId }: BaiKeSearchArgs) {
        super()
        this.datasetId = datasetId
    }

    async _call(inputs: string, runManager?: CallbackManagerForToolRun) {
        if (!inputs) {
            return 'Error searching BaiKe, missing search keyword'
        }

        const results: BaiKeSearchResponse = await searchBaiKe(inputs, this.datasetId)

        if (results.code !== 0) {
            return 'Error searching BaiKe, please try again later'
        }

        const tapd: string[] = []
        for (const result of results.data) {
            if (result.content.includes('id:')) {
                const id = result.content.match(/id:\s*(\d+);/)?.[1]
                if (id) {
                    tapd.push(id)
                }
            }
        }

        const tapdIds = tapd
            .map((item) => item.trim())
            .filter((item) => item !== '')
            .join(',')

        const BaiKeSummary: BaiKeSummaryResponse = await getBaiKes(
            tapdIds,
            'tapd_id,tapd_name,content_summary,ctime,related_pages,prod_owner_nick,tech_owner_nick,updater'
        )

        if (BaiKeSummary.code !== 0) {
            return 'Error searching BaiKeSummary, please try again later'
        }

        let txt = ''
        for (const result of BaiKeSummary.data) {
            txt += `id: ${result.tapd_id} \n`
            txt += `标题: ${result.tapd_name} \n`
            txt += `摘要: ${result.content_summary} \n`
            txt += `创建时间： ${result.ctime} \n`
            const pageTitle = result.related_pages.replace(/https?:\/\/\S+/g, '').replace(/[[\]()：:]/g, '')
            if (pageTitle) {
                txt += `涉及的相关页面: ${pageTitle} \n`
            }

            const ownerString = [result.prod_owner_nick, result.tech_owner_nick].filter(Boolean).join('; ')
            txt += `负责人：${ownerString || result.updater_nick} \n`
            txt += `\n`
        }

        if (!txt) {
            return '未找到任何与之相关的内容'
        }

        return txt
    }
}

module.exports = { nodeClass: BaiKeSearch_Tools }
