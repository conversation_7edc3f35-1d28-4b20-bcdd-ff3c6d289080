<template>
    <a-layout class="h-full overflow-hidden min-w-[1300px]">
        <a-layout-sider theme="light" class="border-r py-[16px] px-[10px]">
            <a-flex vertical class="h-full">
                <logo-header />

                <div class="flex-1 overflow-hidden">
                    <navigation-list />
                </div>
                <link-other />
            </a-flex>
        </a-layout-sider>

        <a-layout class="px-[24px] py-[12px]">
            <RouterView />
        </a-layout>
    </a-layout>
</template>

<script setup lang="ts">
import LogoHeader from '@/components/layout/components/LogoHeader.vue'
import NavigationList from '@/components/layout/components/NavigationList.vue'
import LinkOther from '@/components/layout/components/LinkOther.vue'
</script>
<style scoped lang="less">
.ant-layout-sider {
    width: 230px !important;
    min-width: 230px !important;
    background-color: var(--color-bg-1);
    :deep(.ant-layout-sider-children) {
        background-color: var(--cocor-menu-bg);
        border-radius: 16px;
        padding: 16px 8px;
        .ant-menu {
            background-color: var(--cocor-menu-bg);
        }
    }
}
</style>
