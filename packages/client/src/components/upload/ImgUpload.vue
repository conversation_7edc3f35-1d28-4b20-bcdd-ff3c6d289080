<template>
    <a-upload
        v-model:fileList="fileList"
        name="avatar"
        list-type="picture-card"
        :customRequest="customRequest"
        :max-count="1"
        :show-upload-list="false"
        @change="handleChange"
    >
        <img width="100%" v-if="imageUrl" :src="imageUrl" alt="avatar" />
        <div v-else>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="ant-upload-text">上传</div>
        </div>
    </a-upload>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps, computed } from 'vue'
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { UploadChangeParam, UploadProps } from 'ant-design-vue'
import { uploadFile } from '@/api/space'

const props = defineProps(['avatar'])
const emits = defineEmits(['callback', 'update:avatar'])

const imageUrl = computed({
    get: () => {
        return props.avatar
    },
    set: (value) => {
        emits('update:avatar', value)
    }
})

const loading = ref<boolean>(false)

const fileList = ref<UploadProps['fileList']>([])
const handleChange = (info: UploadChangeParam) => {
    const status = info.file.status
    if (status !== 'uploading') {
        loading.value = true
    }
    if (status === 'done') {
        loading.value = false
        emits('callback', info.file.response[0])
        imageUrl.value = info.file.response[0].location

        message.success(`${info.file.name} 上传成功`)
    } else if (status === 'error') {
        loading.value = false
        message.error(`${info.file.name} 上传失败`)
    }
}

const customRequest: UploadProps['customRequest'] = ({ file, onError, onProgress, onSuccess }) => {
    uploadFile(file, ({ total, loaded }: { total: number; loaded: number }) => {
        //@ts-ignore
        onProgress({ percent: Math.round((loaded / total) * 100).toFixed(2) }, file)
    })
        .then(({ data: response }) => {
            //@ts-ignore
            onSuccess(response, file)
        })
        .catch(onError)

    return {
        abort() {}
    }
}
</script>
