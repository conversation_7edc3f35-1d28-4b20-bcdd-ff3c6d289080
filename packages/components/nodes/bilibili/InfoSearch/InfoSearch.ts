import { INode, INodeData, INodeParams } from '../../../src/Interface'
import { InfoSearch } from './core'

class InfoSearch_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'InfoSearch'
        this.name = 'infoSearch'
        this.version = 1.0
        this.type = 'CustomTool'
        this.icon = 'info.svg'
        this.category = 'Tools'
        this.description = 'Info检索工具，返回标题、摘要、url'
        this.baseClasses = [this.type, 'Tool']
        this.tags = ['bilibili']
        this.inputs = [
            {
                label: 'Secret',
                name: 'secret',
                type: 'string',
                optional: true,
                description: '自定义Info个人中心里的Secret Token，默认则只能获取公开内容'
            }
        ]
    }

    async init(nodeData: INodeData): Promise<any> {
        const secret = nodeData.inputs?.secret as string
        return new InfoSearch({ secret })
    }
}

module.exports = { nodeClass: InfoSearch_Tools }
