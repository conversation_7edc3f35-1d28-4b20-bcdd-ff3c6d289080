<template>
    <a-modal
        v-model:open="open"
        :title="title"
        @ok="handleOk"
        :width="850"
        okText="保存"
        cancelText="取消"
        wrapClassName="copilot-text-edit-modal"
    >
        <div v-if="isTable" class="h-[70vh] overflow-auto mt-[30px]">
            <a-form :model="tableRecord" layout="vertical" ref="refTableRecordForm">
                <a-form-item
                    v-for="(val, key) in tableRecord"
                    :key="key"
                    :label="key"
                    :name="key"
                    :rules="{ required: IndexColumnName.includes(key), message: '请输入' + key }"
                >
                    <a-textarea v-model:value="tableRecord[key]" :auto-size="{ minRows: 1, maxRows: 5 }"></a-textarea>
                </a-form-item>
            </a-form>
        </div>
        <CherryMarkdown
            v-else
            ref="refCherryMarkdown"
            :content="content"
            :cherryConfig="{ editor: { height: '70vh', defaultModel: 'editOnly' } }"
        ></CherryMarkdown>
    </a-modal>
</template>

<script setup lang="ts">
import { computed, toRefs, ref, onMounted } from 'vue'
import { sliceUpdate, sliceAdd, type TableMeta } from '@/api/knowledge'
import { notification } from 'ant-design-vue'
import CherryMarkdown from './CherryMarkdown.vue'
const props = defineProps<{
    visible: boolean
    contentInfo: { title: string; content: string; knowledgeId: string; knowledgeSliceId: string }
    isTable: boolean
    tableRecord: Record<string, any>
    tableMeta: TableMeta[]
}>()
const emits = defineEmits(['update:visible', 'updateSliceList'])

const { title, content, knowledgeId, knowledgeSliceId } = toRefs(props.contentInfo)

const { tableRecord, tableMeta } = toRefs(props)

const refCherryMarkdown = ref()
const refTableRecordForm = ref()

const IndexColumnName = computed(() => tableMeta.value.filter((item) => item.isIndex).map((item) => item.columnName))

const open = computed({
    get: () => {
        return props.visible
    },
    set: (val) => {
        emits('update:visible', val)
    }
})
const handleInit = () => {
    if (props.isTable) {
        knowledgeId.value = tableRecord.value.knowledgeId
        knowledgeSliceId.value = tableRecord.value.knowledge_ks_id
        const filterKeys = ['knowledgeId', 'knowledge_ks_id', 'knowledge_sentence_id', 'knowledge_hit']
        filterKeys.forEach((item) => {
            delete tableRecord.value[item]
        })
    }
}

const handleOk = () => {
    if (props.isTable) {
        handleTableOk()
    } else {
        handleTextOk()
    }
}

const handleTableOk = async () => {
    try {
        await refTableRecordForm.value.validate()
        try {
            if (knowledgeSliceId.value) {
                await sliceUpdate({
                    content: JSON.stringify(tableRecord.value),
                    knowledgeId: knowledgeId.value,
                    knowledgeSliceId: knowledgeSliceId.value
                })
            } else {
                await sliceAdd({
                    content: JSON.stringify(tableRecord.value),
                    knowledgeId: knowledgeId.value
                })
            }

            notification.success({
                message: '成功',
                description: knowledgeSliceId.value ? '表格切片编辑成功' : '表格新增切片成功'
            })
            emits('updateSliceList')
            open.value = false
        } catch (e) {
            console.log(e)
        }
    } catch {
        notification.error({
            message: '校验失败',
            description: '请检查向量存储项是否全部填写'
        })
    }
}

const handleTextOk = async () => {
    const content = await refCherryMarkdown.value.onSubmit()
    try {
        if (knowledgeSliceId.value) {
            await sliceUpdate({
                content,
                knowledgeId: knowledgeId.value,
                knowledgeSliceId: knowledgeSliceId.value
            })
        } else {
            await sliceAdd({
                content,
                knowledgeId: knowledgeId.value
            })
        }

        notification.success({
            message: '成功',
            description: knowledgeSliceId.value ? '切片编辑成功' : '新增切片成功'
        })
        emits('updateSliceList')
        open.value = false
    } catch (e) {
        console.log(e)
    }
}

onMounted(() => {
    handleInit()
})
</script>

<style lang="less">
.copilot-text-edit-modal {
    .ant-modal-content {
        background-color: #f5f7fa;
        .ant-modal-header {
            background-color: #f5f7fa;
        }
    }
    .ant-input {
        background-color: #f5f7fa;
    }
}
</style>
