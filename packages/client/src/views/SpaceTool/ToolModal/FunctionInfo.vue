<template>
    <a-modal title="如何写Function" width="700px" v-model:open="state.open" @ok="onSubmit">
        <div>
            <p></p>
            <p>你可以直接使用如下<strong>变量/函数</strong>：</p>
            <ul>
                <li><code class="bg-[#f5f5f5] border-r-4 pl-2 pr-2">$cookie</code> 当前用户的cookie信息</li>
                <li><code class="bg-[#f5f5f5] border-r-4 pl-2 pr-2">$flow.input</code> 当前用户的提问文本, MCP通讯时无效</li>
                <li><code class="bg-[#f5f5f5] border-r-4 pl-2 pr-2">$flow.env</code> 由MCP通讯时传入的自定义参数，&hello=world&host=123</li>
                <li>
                    <code class="bg-[#f5f5f5] border-r-4 pl-2 pr-2">$yuumi</code> 支持grpc和http协议的接口调用，基于discoveryID进行接口通讯
                    <a href="//dejavu.bilibili.co/material/package/@bilibili-live/node-yuumi" target="_blank">查看文档</a>
                </li>
                <li>
                    <code class="bg-[#f5f5f5] border-r-4 pl-2 pr-2">$json2MarkdownTable()</code> 可将json数组转为markdownTable格式的字符串
                </li>
            </ul>
            <p>
                插件的运行环境中已接入自研模型&nbsp;<strong>index-2.0</strong>&nbsp;、&nbsp;<strong>4o-mini</strong>&nbsp;、&nbsp;<strong>deepseek-v3</strong>&nbsp;，你可以使用如下方法来调用：
            </p>
            <div>
                <pre class="code-block flex flex-col text-[12px]">

<code>const {AIMessage, HumanMessage} = require('@langchain/core/messages')

const input = [
  new AIMessage('尽可能好地回答用户的问题。您必须将输出格式化为符合约定"JSON Schema"实例的JSON值。格式为\n{\nquestion,answer\n}\n'),
  new HumanMessage("你好?"),
];

const answer1 = await $biliIndex.invoke(input)
console.log(answer1.content)

const answer2 = await $chatgpt.invoke(input)
console.log(answer1.content)

const answer3 = await $deepseek.invoke(input)
console.log(answer3.content)</code>

    </pre>
            </div>
            <p style="font-weight: bold; color: red">注意：Function的return 值必须为 字符串！</p>
        </div>
        <template #footer>
            <a-button type="primary" @click="onSubmit">关闭</a-button>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import { defineExpose, reactive } from 'vue'

const state = reactive({
    open: false
})

const open = () => {
    state.open = true
}

const onSubmit = () => {
    state.open = false
}

defineExpose({ open })
</script>
<style scoped>
.code-block {
    background-color: #f5f5f5; /* 浅灰色背景 */
    color: #333333; /* 深蓝色字体 */
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace; /* 代码字体 */
    padding: 16px; /* 内边距，使代码块看起来更舒适 */
    line-height: 1.5; /* 行高，提高可读性 */
    white-space: pre-wrap; /* 保持代码原有空格和换行，同时允许文本换行 */
    overflow-x: auto; /* 如果代码过长，自动出现水平滚动条 */
    border-radius: 4px; /* 边框圆角 */
}
</style>
