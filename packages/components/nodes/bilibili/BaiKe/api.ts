export interface BaiKeSearchResponse {
    code: number
    data: BaiKeSearchContent[]
}

export interface BaiKeSearchContent {
    content: string
    distance: number
    sliceId: string
    sentenceId: number
    knowledgeId: string
    filename: string
    location: string
}

export interface BaiKeSummaryResponse {
    code: number
    data: BaiKeSummaryContent[]
}

export interface BaiKeSummaryContent {
    tapd_id: string
    tapd_name: string
    content: string
    content_summary: string
    ctime: string
    related_pages: string
    prod_owner_nick: string
    tech_owner_nick: string
    creator_nick: string
    updater_nick: string
}

export async function searchBaiKe(query: string, datasetId: string): Promise<BaiKeSearchResponse> {
    const apiUrl = 'https://copilot.bilibili.co/api/knowledge/vector/query'

    try {
        const res = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query,
                datasetId,
                topN: 20,
                distance: 0.7
            })
        })

        const data: BaiKeSearchResponse = await res.json()
        if (!data?.data) throw new Error('Error searching BaiKe, please try again later')

        return data
    } catch (e) {
        throw new Error('Error searching BaiKe, please try again later')
    }
}

export async function getBaiKes(ids: string, fields: string): Promise<BaiKeSummaryResponse> {
    const apiUrl = `http://api.mlive.bilibili.co/baike/encyclopedia/searchByIds?ids=${ids}&fields=${fields}`

    try {
        const res = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })

        const data: BaiKeSummaryResponse = await res.json()
        if (!data?.data) throw new Error('Error searching BaiKe, please try again later')

        return data
    } catch (e) {
        throw new Error('Error searching BaiKe, please try again later')
    }
}
