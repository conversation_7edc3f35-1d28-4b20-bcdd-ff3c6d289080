import { Request, Response } from 'express'
import { App } from '../index'
import { asyncHand<PERSON> } from '../middleware/errorResolver'
import { opsLogger } from '../utils/biliLogger'
import { ScriptSchedule } from '../database/entities/ScriptSchedule'
import moment from 'moment'
import parser from 'cron-parser'
import TaskQueue from '../utils/taskQueue'

// 创建一个任务队列用于处理脚本执行
const scriptTaskQueue = new TaskQueue('script_execution_queue', async (task) => {
    try {
        opsLogger.info('scriptExecution', { msg: '开始执行脚本任务', scriptId: task.id })

        // TODO: 调用chatflow AI接口执行脚本
        // const result = await callChatflowAPI({
        //     chatflowId: task.chatflowid,
        //     content: task.content,
        //     username: task.username,
        //     wxBot: task.wxBot
        // })

        // 更新任务执行状态
        await updateScriptExecutionStatus(task.id, 1, 'SUCCESS')

        opsLogger.info('scriptExecution', {
            msg: '脚本任务执行完成',
            scriptId: task.id
        })
    } catch (error) {
        opsLogger.error('scriptExecution', {
            msg: '脚本任务执行失败',
            scriptId: task.id,
            error
        })
        // 更新任务执行状态为失败
        await updateScriptExecutionStatus(task.id, 0, 'FAILED')
    }
})

// 更新脚本执行状态
async function updateScriptExecutionStatus(scriptId: string, status: number, statusText: string) {
    try {
        const scriptRepo = AppDataSourceInstance.getRepository(ScriptSchedule)
        await scriptRepo.update(
            { id: scriptId },
            {
                lastRunDate: moment().format('YYYY-MM-DD HH:mm:ss'),
                lastRunStatus: status
            }
        )
    } catch (error) {
        opsLogger.error('scriptExecution', {
            msg: '更新任务状态失败',
            scriptId,
            error
        })
    }
}

// 存储AppDataSource实例，以便在队列处理函数中使用
let AppDataSourceInstance: any = null

export function schedule({ app, AppDataSource }: App) {
    // 保存AppDataSource实例
    AppDataSourceInstance = AppDataSource

    app.get(
        '/api/schedule/once',
        asyncHandler(async (req: Request, res: Response) => {
            try {
                // 获取当前时间
                const now = moment()

                // 获取所有启用状态的定时任务
                const scriptRepo = AppDataSource.getRepository(ScriptSchedule)
                const activeScripts = await scriptRepo.find({
                    where: {
                        status: 1 // 假设1表示启用状态
                    }
                })

                opsLogger.info('scheduleCheck', {
                    msg: '检查定时任务',
                    totalScripts: activeScripts.length
                })

                // 需要执行的任务
                const tasksToExecute = []

                // 检查每个任务是否应该执行
                for (const script of activeScripts) {
                    try {
                        // 解析cron表达式
                        const interval = parser.parseExpression(script.cron)

                        // 获取上次应该执行的时间
                        const prevTime = interval.prev().toDate()

                        // 如果上次应该执行的时间在5分钟内，且上次实际执行的时间早于这个时间，那么应该执行这个任务
                        const fiveMinutesAgo = moment().subtract(5, 'minutes').toDate()
                        const lastRunDate = script.lastRunDate ? moment(script.lastRunDate).toDate() : new Date(0)

                        if (prevTime > fiveMinutesAgo && prevTime > lastRunDate) {
                            tasksToExecute.push(script)
                        }
                    } catch (error) {
                        opsLogger.error('scheduleCheck', {
                            msg: 'cron表达式解析失败',
                            scriptId: script.id,
                            cron: script.cron,
                            error
                        })
                    }
                }

                opsLogger.info('scheduleCheck', {
                    msg: '需要执行的任务数',
                    count: tasksToExecute.length
                })

                // 将需要执行的任务加入队列
                for (const task of tasksToExecute) {
                    await scriptTaskQueue.addTask(task)
                }

                // 返回执行结果
                return res.json({
                    code: 0,
                    data: {
                        totalTasks: activeScripts.length,
                        executingTasks: tasksToExecute.length
                    }
                })
            } catch (error) {
                opsLogger.error('scheduleCheck', { msg: '检查定时任务失败', error })
                return res.json({
                    code: 500,
                    message: `检查定时任务失败: ${error}`
                })
            }
        })
    )
}
