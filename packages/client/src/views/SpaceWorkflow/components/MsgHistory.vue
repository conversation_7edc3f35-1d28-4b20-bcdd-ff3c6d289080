<template>
    <a-form class="p-2" layout="inline" :model="formState">
        <a-form-item label="时间范围">
            <a-range-picker v-model:value="formState.range" />
        </a-form-item>
        <a-form-item label="来源">
            <a-select v-model:value="formState.chatType" mode="tags" style="min-width: 140px">
                <a-select-option v-for="item in sourceTypeOptions" :value="item.name" :key="item.name">{{ item.label }}</a-select-option>
            </a-select>
        </a-form-item>
    </a-form>
    <div class="p-2">
        <div class="flex flex-row gap-8">
            <a-card class="basis-1/3" size="small">
                <h3 class="text-slate-400">总消息</h3>
                <span class="text-xl">{{ state.stats.totalMessages }}</span>
            </a-card>
            <a-card class="basis-1/3" size="small">
                <h3 class="text-slate-400">总反馈</h3>
                <span class="text-xl">{{ state.stats.totalFeedback }}</span>
            </a-card>
            <a-card class="basis-1/3" size="small">
                <h3 class="text-slate-400">正向反馈率</h3>
                <span class="text-xl">{{ state.stats.positiveFeedback }}%</span>
            </a-card>
        </div>
    </div>
    <div class="my-2">
        <a-row :wrap="false">
            <a-col flex="none" class="overflow-y-auto p-2" style="max-height: 500px">
                <div>
                    <div
                        class="shadow shadow-blue-500/40 hover:shadow-indigo-500/40 p-2 mb-2 rounded-md"
                        v-for="item in state.chatHistory"
                        :class="{ selected: state.selectChat.chatId === item.chatId }"
                        :key="item.chatId"
                        @click="handleChatClick(item.apiMessage)"
                    >
                        <div class="text-ellipsis overflow-hidden text-nowrap" style="max-width: 240px">
                            User: {{ item.userMessage.content }}
                        </div>
                        <div class="text-ellipsis overflow-hidden text-nowrap" style="max-width: 240px">
                            Bot: {{ item.apiMessage.content }}
                        </div>
                        <div class="text-sm text-slate-400">
                            {{ item.apiMessage.mtime }} {{ item.username ? ` @${item.username}` : '' }}
                        </div>
                    </div>
                </div>
            </a-col>
            <a-col flex="auto" class="overflow-y-auto" style="max-height: 500px">
                <ChatHistory :messages="state.sessionHistory" :chatflowid="props.chatflowId"></ChatHistory>
            </a-col>
        </a-row>
    </div>
</template>
<script lang="ts" setup>
import { reactive, defineProps, onMounted, watch, onBeforeUnmount } from 'vue'
import type { UnwrapRef } from 'vue'
import dayjs from 'dayjs'
import { getChatMessage, getChatStats } from '../../../api/chat'
import type { ChatMessage, MessageType, ChatStats } from '../../../api/chat'
import ChatHistory from '@/components/chat/ChatHistory.vue'

interface FormState {
    range: [dayjs.Dayjs, dayjs.Dayjs]
    chatType: string[]
    order: 'DESC' | 'ESC'
}
interface FilterChatHistory {
    apiMessage: ChatMessage
    userMessage: ChatMessage
    username?: string
    chatId: string
    sessionId?: string
    count: number
}
const sourceTypeOptions = [
    {
        label: 'UI',
        name: 'INTERNAL'
    },
    {
        label: 'API/Embed',
        name: 'EXTERNAL'
    }
]
const chatTheme = {
    chatWindow: {
        fontSize: 12
    }
}
const props = defineProps({
    chatflowId: {
        type: String
    }
})
const formState: UnwrapRef<FormState> = reactive({
    range: [dayjs().subtract(1, 'month'), dayjs()],
    chatType: [],
    order: 'DESC',
    apiHost: `//${window.location.host}`
})
const state = reactive({
    chatHistory: [] as FilterChatHistory[],
    sessionHistory: [] as MessageType[],
    stats: {} as ChatStats,
    selectChat: {} as ChatMessage
})
watch(
    () => props.chatflowId,
    () => {
        getChatList()
        getChatStatsData()
    }
)

watch(
    () => state.selectChat,
    (newValue) => {
        getSessionHistory(newValue.chatId, newValue.sessionId, newValue.memoryType || '')
    }
)
watch(formState, (newValue) => {
    getChatStatsData()
    getChatList()
})
onMounted(() => {
    getChatList()
    getChatStatsData()
})

const handleChatClick = (item: ChatMessage) => {
    state.selectChat = item
}
const getChatStatsData = async () => {
    if (!props.chatflowId) {
        state.stats = {} as any
        return
    }
    const { data } = await getChatStats(props.chatflowId, {
        startDate: formState.range[0].toDate(),
        endDate: formState.range[1].toDate(),
        chatType: formState.chatType.length ? JSON.stringify(formState.chatType) : undefined
    })
    state.stats = data
}
const getChatPK = (chatmsg: ChatMessage) => {
    const chatId = chatmsg.chatId
    const memoryType = chatmsg.memoryType ?? 'null'
    const sessionId = chatmsg.sessionId ?? 'null'
    return `${chatId}_${memoryType}_${sessionId}`
}
const getChatList = async () => {
    state.chatHistory = []
    if (!props.chatflowId) {
        return
    }
    const chatHistory = await getChatMessage(props.chatflowId, {
        startDate: formState.range[0].toDate(),
        endDate: formState.range[1].toDate(),
        order: formState.order,
        chatType: formState.chatType.length ? JSON.stringify(formState.chatType) : undefined
    })
    const map = {} as Record<string, FilterChatHistory>
    const filterHistory: FilterChatHistory[] = []
    chatHistory.forEach((item, index) => {
        item.mtime = dayjs(item.mtime).format('YYYY-MM-DD HH:mm:ss')
        const key = getChatPK(item)
        if (map[key] === undefined) {
            map[key] = {
                chatId: item.chatId,
                username: item.username,
                sessionId: item.sessionId,
                count: 0
            } as FilterChatHistory
        }

        if (item.role === 'apiMessage' && map[key].apiMessage === undefined) {
            map[key].apiMessage = item
            map[key].count++
        } else if (item.role === 'userMessage' && map[key].userMessage === undefined) {
            map[key].userMessage = item
            map[key].count++
        }
        if (map[key].count === 2) {
            filterHistory.push(map[key])
            map[key].count++
            return
        }
        // 设置默认选中
        if (index == 0) {
            state.selectChat = item
        }
    })
    state.chatHistory = filterHistory
}
const getSessionHistory = async (chatId: string, sessionId: string, memoryType: string) => {
    state.sessionHistory = []
    if (!props.chatflowId) {
        return
    }
    const history = await getChatMessage(props.chatflowId, {
        // startDate: formState.range[0].toDate(),
        // endDate: formState.range[1].toDate(),
        order: 'ASC',
        chatId: chatId,
        feedback: true,
        memoryType: memoryType,
        sessionId: sessionId
    })
    // state.sessionHistory = JSON.stringify(history)
    state.sessionHistory = history.map((item) => {
        return {
            messageId: item.id,
            message: item.content,
            type: item.role,
            sourceDocuments: item.sourceDocuments,
            fileAnnotations: item.fileAnnotations,
            fileUploads: [],
            feedback: item.feedback
                ? {
                      id: item.feedback.id,
                      rating: item.feedback.rating,
                      content: item.feedback.content
                  }
                : {},
            ctime: dayjs(item.createdDate).format('YYYY-MM-DD HH:mm:ss')
        } as MessageType
    })
}
</script>
<style lang="css" scoped>
.selected {
    background-color: rgb(247, 248, 255);
}
</style>
