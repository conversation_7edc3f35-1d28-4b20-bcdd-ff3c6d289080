<template>
    <MCPModal
        v-if="showMCPModal"
        v-model="showMCPModal"
        :iconSrc="currentSelected.iconSrc"
        :name="currentSelected.name"
        :title="currentSelected.title"
        :creator="currentSelected.creator"
        :description="currentSelected.description"
        :tools="currentSelected.tools"
        :id="currentSelected.id"
    />
    <a-flex justify="space-between" align="center">
        <a-typography-title class="flex-1 !mb-0" :level="4">插件市场</a-typography-title>

        <a-input class="flex-1 border-none shadow-md leading-8" placeholder="搜索" v-model:value="keyword" @pressEnter="onSearch">
            <template #prefix>
                <MagnifyingGlassIcon class="w-4 h-4 text-[#383743]" />
            </template>
        </a-input>
        <div class="flex-1 text-right">
            <!-- <a-button type="primary">创建插件</a-button> -->
        </div>
    </a-flex>
    <a-typography-text type="secondary" class="mt-2 mb-5 text-center">
        所有公开的插件都可在AI编排里通过Tool组件使用，从而让AI实现Agent Tool能力
    </a-typography-text>
    <InfiniteScroll :end="end" @loadMoreFn="getData" ref="infiniteScrollRef">
        <div class="copilot-card-container">
            <a-card hoverable class="w-full" :bodyStyle="{ paddingBottom: '20px' }" v-for="item in dataSource" :key="item.id">
                <template #actions>
                    <a-button type="link" @click="showMCPConfig(item)">
                        <ApiOutlined />
                        使用MCP服务
                    </a-button>
                </template>
                <a class="block w-full h-full text-black" target="_blank">
                    <a-card-meta class="p-0">
                        <template #title>
                            <div class="flex">
                                <div class="flex items-center">
                                    <a-avatar class="mr-[10px]" :size="68" shape="square" :src="item.iconSrc" />
                                </div>
                                <div>
                                    <div>{{ item.title }}</div>
                                    <a-typography-text type="secondary" class="mr-1" style="color: var(--color-symbol-bold)">{{
                                        item.name
                                    }}</a-typography-text>
                                    <div>
                                        <a-typography-text type="secondary" style="color: var(--color-symbol-dc)"
                                            >@{{ item.creator }}</a-typography-text
                                        >
                                    </div>
                                </div>
                            </div>
                        </template>

                        <template #description>
                            <div class="h-[60px]">
                                <a-typography-paragraph
                                    style="color: var(--color-symbol-bold); margin-bottom: 8px"
                                    type="secondary"
                                    :ellipsis="{ rows: 2 }"
                                    :content="item.description || '暂无描述'"
                                >
                                </a-typography-paragraph>
                            </div>
                        </template>
                    </a-card-meta>
                </a>
            </a-card>
        </div>
    </InfiniteScroll>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { getToolList } from '@/api/tool'
import { mcpSign } from '@/api/mcp'
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline'
// @ts-ignore
import InfiniteScroll from '@/components/InfiniteScroll.vue'
import MCPModal from './components/MCPModal.vue'
import { ApiOutlined } from '@ant-design/icons-vue'

interface IToolList {
    id: string
    description: string
    creator: string
    iconSrc: string
    title: string
    name: string
    mcpUrl: string
    tools: {
        name: string
        label: string
        description: string
    }[]
}

const current = ref(0)
const defaultPageSize = 24
const end = ref(false)

const infiniteScrollRef = ref()

const keyword = ref('')

const dataSource = ref<IToolList[]>([])

const currentSelected = ref<any>()

const showMCPModal = ref(false)

const showMCPConfig = (item: IToolList) => {
    currentSelected.value = item
    showMCPModal.value = true
    console.log(currentSelected)
}

const getData = async (page?: number, keyword?: string) => {
    current.value = page || current.value + 1
    try {
        const res = await getToolList({ page: current.value, pageSize: defaultPageSize, keyword, isPublic: true })
        dataSource.value = [
            ...dataSource.value,
            ...res.data.rows.map((it: Record<string, any>) => {
                return {
                    ...it,
                    tools: [
                        {
                            name: it.name,
                            label: it.title,
                            description: it.description
                        }
                    ]
                }
            })
        ]
        if (res.data.rows.length < defaultPageSize) {
            end.value = true
        } else {
            await nextTick()
            // 如果内容高度不足以触发滚动，继续加载更多数据
            if (infiniteScrollRef.value?.container?.scrollHeight - infiniteScrollRef.value?.container?.clientHeight < 20) {
                getData()
            }
        }
    } catch (e) {
        console.log(e)
    }
}
const onSearch = () => {
    end.value = false
    dataSource.value = []
    getData(1, keyword.value)
}
</script>
