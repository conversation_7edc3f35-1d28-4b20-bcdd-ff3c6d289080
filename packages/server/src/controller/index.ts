import { App } from '../index'
import { space } from './Space'
import { dataset } from './Dataset'
import { knowledge } from './Knowledge'
import { chatflow } from './Chatflow'
import { common } from './Common'
import { feedback } from './Feedback'
import { stats } from './Stats'
import { audit } from './business/Audit'
import { tools } from './Tools'
import { knowledgeSlice } from './KnowledgeSlice'
import { workflow } from './business/Workflow'
import { acceptStats } from './Accept'
import { mcp } from './mcp'
import { hot } from './business/Hot'

export default function (app: App) {
    space(app)
    dataset(app)
    knowledge(app)
    chatflow(app)
    common(app)
    feedback(app)
    stats(app)
    audit(app)
    tools(app)
    knowledgeSlice(app)
    workflow(app)
    acceptStats(app)
    mcp(app)
    hot(app)
}
