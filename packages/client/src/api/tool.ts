import { apiV0, type ResponseData } from './client'

type Tool = {
    func?: string
    name: string
    schema: string
    description?: string
    iconSrc?: string
    id?: string
    isPublic?: string
    spaceId?: string
    title?: string
}

type ToolParams = {
    page?: number
    spaceId?: string
    pageSize?: number
    keyword?: string
    isPublic?: true
}

export interface TestToolLogItem {
    time: number
    cost: string
    logs: Array<any>
    level: 'info' | 'error'
}
export interface TestToolResponse {
    result: Record<string, any>
    logs: TestToolLogItem[]
}

export const getToolList = (params: ToolParams) => {
    return apiV0.get(`/v0/tools/list`, { params })
}

export const getToolDetail = (id: string) => {
    return apiV0.get('/v0/tools?id=' + id)
}

export const addTool = (data: Tool) => {
    return apiV0.post('/v0/tools/add', data)
}

export const updateTool = (data: Tool) => {
    return apiV0.post('/v0/tools/update', data)
}

export const deleteTool = (id: string) => {
    return apiV0.post('/v0/tools/delete', { id })
}

export const functionTestTool = (params: { func: string; input: string; schema: object }) => {
    return apiV0.post<ResponseData<TestToolResponse>>('/v0/tools/functionTest', params)
}
