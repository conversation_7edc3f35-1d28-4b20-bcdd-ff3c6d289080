import { BaseChatModel, type BaseChatModelParams } from '@langchain/core/language_models/chat_models'
import { AIMessage, BaseMessage, ChatMessage } from '@langchain/core/messages'
import { ChatResult } from '@langchain/core/outputs'
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager'
import { getUserCookie } from '../../../src'
import { ConsoleCallbackHandler } from '../../../src/handler'
import { Logger } from 'winston'

/**
 * 消息体里的角色信息
 */
export type DatasetMessageRole = 'system' | 'assistant' | 'user'

/**
 * Index模型里的消息接口字段
 */
interface DatasetMessage {
    role: DatasetMessageRole
    content: string
}

/**
 * 请求时的接口参数
 */
interface ChatCompletionRequest {
    query: DatasetMessage[]
    model: string
    stream?: boolean // 暂不支持
    username?: string
    datasetId?: string
    chatId?: string
    chatflowid?: string
    prompt_template: string
    bot_id: string
}

/**
 * 接口返回时的接口参数
 */
interface ChatCompletionResponse {
    status: number
    message: string
    data: string
}

/**
 * Dataset class的输入.
 */
declare interface DatasetChatInput {
    model: string
    datasetId: string
    streaming?: boolean // 暂不支持
    prefixMessages?: DatasetMessage[]
    prompt: string
    botId: string
}

/**
 * Function that extracts the custom role of a generic chat message.
 * @param message Chat message from which to extract the custom role.
 * @returns The custom role of the chat message.
 */
function extractGenericMessageCustomRole(message: ChatMessage) {
    if (['system', 'assistant', 'user'].includes(message.role) === false) {
        console.warn(`Unknown message role: ${message.role}`)
    }

    return message.role as DatasetMessageRole
}

/**
 * Function that converts a base message to a Dataset message role.
 * @param message Base message to convert.
 * @returns The Dataset message role.
 */
function messageToDatasetRole(message: BaseMessage): DatasetMessageRole {
    const type = message._getType()
    switch (type) {
        case 'ai':
            return 'assistant'
        case 'human':
            return 'user'
        case 'system':
            return 'system'
        case 'function':
            throw new Error('Function messages not supported')
        case 'generic': {
            if (!ChatMessage.isInstance(message)) throw new Error('Invalid generic chat message')
            return extractGenericMessageCustomRole(message)
        }
        default:
            throw new Error(`Unknown message type: ${type}`)
    }
}

/**
 * Dataset chat model.
 */
export class ChatDataset extends BaseChatModel implements DatasetChatInput {
    static lc_name() {
        return 'ChatDataset'
    }

    get callKeys(): string[] {
        return ['stop', 'signal', 'options']
    }

    get lc_secrets(): { [key: string]: string } | undefined {
        return {
            apikey: 'API_KEY'
        }
    }

    get lc_aliases(): { [key: string]: string } | undefined {
        return undefined
    }

    lc_serializable = true

    streaming = false

    prefixMessages?: DatasetMessage[]

    model = 'index-2.0-long-text'

    datasetId: string

    apiUrl: string

    prompt: string

    botId: string

    maxRetries = 1

    constructor(fields?: Partial<DatasetChatInput> & BaseChatModelParams) {
        super(fields ?? {})

        this.streaming = fields?.streaming ?? this.streaming
        this.prefixMessages = fields?.prefixMessages ?? this.prefixMessages
        this.model = fields?.model ?? this.model
        this.datasetId = fields?.datasetId ?? this.datasetId
        this.prompt = fields?.prompt ?? this.prompt
        this.botId = fields?.botId ?? this.botId

        this.apiUrl = 'http://aidata.bilibili.co/knowledge/query/v3'

        if (!this.model) {
            throw new Error('model不能为空')
        }
        if (!this.datasetId) {
            throw new Error('datasetId不能为空')
        }
    }

    /**
     * Get the parameters used to invoke the model
     * 会上报到smith
     */
    invocationParams(): Omit<ChatCompletionRequest, 'query'> {
        return {
            model: this.model,
            datasetId: this.datasetId,
            stream: this.streaming,
            prompt_template: this.prompt,
            bot_id: this.botId
        }
    }

    /** @ignore */
    async _generate(
        messages: BaseMessage[],
        options?: this['ParsedCallOptions'],
        runManager?: CallbackManagerForLLMRun
    ): Promise<ChatResult> {
        const params = this.invocationParams()
        // 获取用户cookie
        const cookie = await getUserCookie((runManager as any)?.metadata?.chatId as string)
        const regex = /username=([^;]+)(?:$|;)/
        const match = cookie?.match(regex)
        let username = ''
        if (match) {
            username = match[1]
        }
        // 其他参数
        const chatflowid = (runManager as any)?.metadata?.chatflowid as string
        const chatId = (runManager as any)?.metadata?.chatId as string

        let logger: Logger | undefined

        runManager?.handlers.forEach((handler) => {
            if (handler instanceof ConsoleCallbackHandler) {
                logger = handler.logger
            }
        })

        const messagesMapped: DatasetMessage[] = messages.map((message) => ({
            role: messageToDatasetRole(message),
            content: message.content as string
        }))

        logger && logger.info('Dataset', { params, messages: messagesMapped, username, chatflowid, chatId })

        const data = params.stream
            ? await new Promise<ChatCompletionResponse>((resolve, reject) => {
                  let response: ChatCompletionResponse
                  let rejected = false
                  let resolved = false
                  this.completionWithRetry(
                      {
                          ...params,
                          query: messagesMapped,
                          username,
                          chatflowid,
                          chatId
                      },
                      true,
                      options?.signal,
                      (event) => {
                          const data = JSON.parse(event.data)

                          if (data?.error_code) {
                              if (rejected) {
                                  return
                              }
                              rejected = true
                              reject(new Error(data?.error_msg))
                              return
                          }

                          const message = data as {
                              status: number
                              message: string
                              data: string
                              is_end: boolean
                          }

                          // on the first message set the response properties
                          if (!response) {
                              response = {
                                  status: message.status,
                                  message: message.message,
                                  data: message.data
                              }
                          } else {
                              response.data += message.data
                          }

                          // TODO this should pass part.index to the callback
                          // when that's supported there
                          // eslint-disable-next-line no-void
                          void runManager?.handleLLMNewToken(message.data ?? '')

                          if (message.is_end) {
                              if (resolved || rejected) {
                                  return
                              }
                              resolved = true
                              resolve(response)
                          }
                      }
                  ).catch((error) => {
                      if (!rejected) {
                          rejected = true
                          reject(error)
                      }
                  })
              })
            : await this.completionWithRetry(
                  {
                      ...params,
                      query: messagesMapped,
                      username,
                      chatflowid,
                      chatId
                  },
                  false,
                  options?.signal
              ).then((data) => {
                  if (data?.status !== 0) {
                      logger &&
                          logger.error('Dataset', {
                              params,
                              error: `status:${data?.status} ${data?.message} ${data?.data}`,
                              username,
                              chatflowid,
                              chatId
                          })
                      throw new Error(`${data?.message} ${data?.data}` || 'Dataset通讯异常')
                  }
                  return data
              })

        const text = data.data

        return {
            generations: [
                {
                    text,
                    message: new AIMessage(text)
                }
            ]
        }
    }

    /** @ignore */
    async completionWithRetry(
        request: ChatCompletionRequest,
        stream: boolean,
        signal?: AbortSignal,
        onmessage?: (event: MessageEvent) => void
    ) {
        const makeCompletionRequest = async () => {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    ...(stream ? { Accept: 'text/event-stream' } : {}),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request),
                signal
            })

            if (response.status !== 200) {
                throw new Error(`Dataset抖了一下：${response.status} ${response.statusText}`)
            }

            if (!stream) {
                return response.json()
            }

            if (response.body) {
                // response will not be a stream if an error occurred
                if (!response.headers.get('content-type')?.startsWith('text/event-stream')) {
                    onmessage?.(
                        new MessageEvent('message', {
                            data: await response.text()
                        })
                    )
                    return
                }
                const reader = response.body.getReader()
                const decoder = new TextDecoder('utf-8')
                let data = ''
                let continueReading = true
                while (continueReading) {
                    const { done, value } = await reader.read()
                    if (done) {
                        continueReading = false
                        break
                    }
                    data += decoder.decode(value)
                    let continueProcessing = true
                    while (continueProcessing) {
                        const newlineIndex = data.indexOf('\n')
                        if (newlineIndex === -1) {
                            continueProcessing = false
                            break
                        }
                        const line = data.slice(0, newlineIndex)
                        data = data.slice(newlineIndex + 1)
                        if (line.startsWith('data:')) {
                            const event = new MessageEvent('message', {
                                data: line.slice('data:'.length).trim()
                            })
                            onmessage?.(event)
                        }
                    }
                }
            }
        }

        return this.caller.call(makeCompletionRequest)
    }

    _llmType() {
        return 'Dataset'
    }

    /** @ignore */
    _combineLLMOutput() {
        return []
    }
}
