<template>
    <div class="overflow-auto h-full">
        <a-breadcrumb class="mb-[25px]">
            <a-breadcrumb-item class="cursor-pointer" @click="router.back">知识库</a-breadcrumb-item>

            <a-breadcrumb-item>添加内容</a-breadcrumb-item>
        </a-breadcrumb>
        <!-- body -->
        <div class="m-auto py-[32px]" style="width: calc(100% - 200px)">
            <!--顶部步骤条-->
            <a-steps :current="current" :items="step" class="w-full px-[20px] mb-[36px]" size="small"></a-steps>
            <!--文件上传-->
            <div v-show="current === 0">
                <unit-upload
                    v-if="['localDoc', 'localExcel'].includes(type)"
                    ref="refUnitUpload"
                    :limit="curUnit.limit"
                    :description="curUnit.description"
                    v-model:is-ready="isReadyUpload"
                    :accept="curUnit.accept"
                    class="mt-3 knowledge-upload"
                />
                <unit-info ref="refUnitInfo" v-if="type === 'infoDoc'" class="w-full" v-model:is-ready="isReadyUpload">
                    <p class="m-0 pb-[8px]">更新频率</p>
                    <a-select class="w-full" v-model:value="autoUpdate" :options="dayOption"> </a-select>
                </unit-info>
                <unit-url ref="refUnitUrl" v-if="type === 'urlDoc'" v-model:is-ready="isReadyUpload">
                    <p class="m-0 pb-[8px]">更新频率</p>
                    <a-select class="w-full" v-model:value="autoUpdate" :options="dayOption"> </a-select>
                </unit-url>
                <div v-if="type === 'manualImportDoc'">
                    <a-input class="mb-5" v-model:value="markdownFilename" placeholder="请输入文件名称" size="large"></a-input>

                    <cherry-markdown ref="refCherryMarkdown" v-model:is-ready="isReadyUpload"></cherry-markdown>
                </div>
            </div>

            <!--配置-->
            <div v-show="current === 1">
                <table-config-rule ref="TableConfigRuleRef" v-if="isTable" :files="files" @change="changeTableRule"></table-config-rule>
                <text-config-rule ref="TextConfigRuleRef" v-else></text-config-rule>
            </div>
            <!--预览-->
            <div v-show="current === 2">
                <table-preview v-if="isTable" :tableRule="tableRule"></table-preview>
            </div>
            <!--解析进度-->
            <div v-show="showProgress">
                <p class="text-sm font-semibold mb-[17px] mt-[24px]">服务器处理中</p>
                <div class="progress-item">
                    <div v-for="ele in knowledgeList" :key="ele.knowledgeId">
                        <span class="label">{{ ele.name }}</span>
                        <a-progress :percent="ele.progress" show-info :status="ele.errMsg ? 'exception' : ''" />
                        <p class="text-[red]" v-if="ele.errMsg">{{ ele.errMsg }}</p>
                    </div>
                </div>
            </div>
            <div class="w-full flex justify-end mt-4">
                <a-button v-if="current > 0 && !showProgress" class="mr-3" @click="revert">上一步</a-button>
                <a-button v-if="!showProgress" type="primary" class="mr-3" :disabled="disabled" :loading="loading" @click="next"
                    >下一步</a-button
                >
                <div v-if="showProgress" class="flex items-center">
                    <span class="text-[#333] text-[12px] mr-4">点击确认不影响数据处理，处理完毕后才能使用</span>
                    <a-button type="primary" @click="submit">确认</a-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { KNOWLEDGE_UPLOAD_STEPS } from '@/constant/space'
import { useRoute, useRouter } from 'vue-router'
import UnitUpload from '@/components/upload/UnitUpload.vue'
const refUnitUpload = ref<any>(null)

import { computed, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import TextConfigRule from '@/views/SpaceKnowledge/components/TextConfigRule.vue'
import TablePreview from '@/views/SpaceKnowledge/components/TablePreview.vue'
import CherryMarkdown from '@/views/SpaceKnowledge/components/CherryMarkdown.vue'
import TableConfigRule from '@/views/SpaceKnowledge/components/TableConfigRule.vue'
import type { IUploadFile } from '@/api/knowledge'
import { createKnowledgeDoc, getKnowledgeProgress, markdownUpload, type IKnowledgeProgress } from '@/api/knowledge'
import UnitInfo from './components/UnitInfo.vue'
import UnitUrl from '@/views/SpaceKnowledge/components/UnitUrl.vue'
import { dayOption, dataSource } from './config'
import { notification } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()

const type = (route.query.type as 'localDoc' | 'localExcel' | 'infoDoc' | 'urlDoc' | 'manualImportDoc') || 'localDoc'
const isDoc = type === 'localDoc'
const isTable = type === 'localExcel'

const step = KNOWLEDGE_UPLOAD_STEPS[type]

const showProgress = computed(() => {
    return (!isTable && current.value === 2) || (isTable && current.value === 3)
})

const disabled = computed(() => {
    return (
        (current.value === 0 && !isReadyUpload.value) ||
        (current.value === 1 &&
            isTable &&
            (!tableRule.tableMeta.some((item: any) => item.isIndex) || tableRule.tableMeta.every((item: any) => item.isIndex))) ||
        (type === 'manualImportDoc' && !markdownFilename.value)
    )
})

const loading = ref(false)

const current = ref(0)
const isReadyUpload = ref(false)
const files = ref<IUploadFile[]>([])
const curUnit = dataSource.find((item: any) => item.key === type) || {
    description: '',
    limit: 0,
    accept: ''
}
const tableRule = reactive({
    tableSheet: {
        sheetId: 0,
        headerLineIdx: 1,
        startLineIdx: 2
    },
    previewData: [],
    tableMeta: [],
    totalRow: 0
}) // 表格规则
const textRule = reactive({
    formatType: 0,
    language: '',
    chunkSize: 0,
    chunkOverlap: 0
})

const autoUpdate = ref('0')
const markdownFilename = ref('')

const knowledgeList = ref<IKnowledgeProgress[]>([])

const knowledgeId = ref<string[]>([])

const changeTableRule = (value: any) => {
    Object.assign(tableRule, value)
}

const TableConfigRuleRef = ref()
const TextConfigRuleRef = ref()
const refUnitInfo = ref()
const refUnitUrl = ref()
const refCherryMarkdown = ref()
// 下一步
const next = async () => {
    loading.value = true
    // 获取表格配置数据
    if (isTable) {
        switch (current.value) {
            case 0:
                files.value = refUnitUpload.value?.getFiles() //当前上传的文件
                await nextTick()
                TableConfigRuleRef.value?.getTableUpload()
                current.value++
                break
            case 1: {
                await nextTick()
                const { previewData, totalRow } = await TableConfigRuleRef.value.getTablePreview()
                tableRule.previewData = previewData
                tableRule.totalRow = totalRow
                current.value++
                break
            }
            case 2:
                await getCreateKnowledgeDoc()
                current.value++
        }
    } else {
        switch (current.value) {
            case 0: {
                if (isDoc) {
                    files.value = refUnitUpload.value?.getFiles() //当前上传的文件
                } else if (type === 'manualImportDoc') {
                    try {
                        const content = await refCherryMarkdown.value.onSubmit()
                        const { data } = await markdownUpload({ content, filename: markdownFilename.value })
                        files.value = [{ ...data, mimeType: 'text/markdown' }]
                    } catch (e) {
                        console.log(e)
                    }
                }
                current.value++
                break
            }
            case 1: {
                const textSplit = await TextConfigRuleRef.value.getTextConfigRule()
                if (textSplit) {
                    Object.assign(textRule, textSplit)
                    await getCreateKnowledgeDoc()
                    current.value++
                }
                break
            }
        }
    }
    loading.value = false
}
// 上一步
const revert = () => {
    current.value--
}
const getCreateKnowledgeDoc = async () => {
    let params
    switch (type) {
        case 'localExcel':
            params = {
                datasetId: String(route.params.datasetId),
                documents: files.value,
                tableSheet: tableRule.tableSheet,
                tableMeta: tableRule.tableMeta
            }
            break
        case 'localDoc':
            params = {
                datasetId: String(route.params.datasetId),
                documents: files.value,
                textSplit: textRule.formatType
                    ? {
                          language: textRule.language,
                          chunkSize: textRule.chunkSize,
                          chunkOverlap: textRule.chunkOverlap
                      }
                    : {}
            }
            break

        case 'infoDoc': {
            const { type, data } = (await refUnitInfo.value?.getFormData()) as {
                type: number
                data: any
            }
            type === 2 ? (data as IUploadFile[]) : (data as [{ file: { fileKey: any; filename: string }; url: string }])
            params = {
                datasetId: String(route.params.datasetId),
                documents:
                    type === 2
                        ? data.map((item: IUploadFile) => {
                              item.autoUpdate = autoUpdate.value
                              return item
                          })
                        : [{ ...data[0].file, remoteUrl: data[0].url, autoUpdate: autoUpdate.value }],
                textSplit: textRule.formatType
                    ? {
                          language: textRule.language,
                          chunkSize: textRule.chunkSize,
                          chunkOverlap: textRule.chunkOverlap
                      }
                    : {}
            }
            break
        }
        case 'urlDoc': {
            const { urlInfo, textSplit } = (await refUnitUrl.value?.getContent()) as { urlInfo: IUploadFile; textSplit: string }
            params = {
                datasetId: String(route.params.datasetId),
                documents: [urlInfo],
                textSplit: textRule.formatType
                    ? {
                          language: textRule.language,
                          chunkSize: textRule.chunkSize,
                          chunkOverlap: textRule.chunkOverlap,
                          web: JSON.parse(textSplit)
                      }
                    : { web: JSON.parse(textSplit) }
            }

            break
        }
        case 'manualImportDoc':
            params = {
                datasetId: String(route.params.datasetId),
                documents: files.value,
                textSplit: textRule.formatType
                    ? {
                          language: textRule.language,
                          chunkSize: textRule.chunkSize,
                          chunkOverlap: textRule.chunkOverlap
                      }
                    : {}
            }
            break
    }
    const { data } = await createKnowledgeDoc(params)
    knowledgeId.value = data.map((item: any) => item.id)
    knowledgeList.value = data.map((item: any) => {
        return { name: item.filename + item.extname, knowledgeId: item.id, progress: 0, errMsg: '' }
    })

    // 轮询请求获取progress状态为100结束
    handleStartInterval(knowledgeId.value)
}
const intervalTimer = ref<any>(null)

const task = async (knowledgeId: string[]) => {
    if (knowledgeList.value.every((item) => item.progress >= 100 || item.errMsg)) {
        return handleClearInterval()
    }
    try {
        const { data } = await getKnowledgeProgress({ knowledgeId })
        knowledgeList.value = Object.values(data)
    } catch {
        notification.error({
            message: '失败',
            description: '文件解析失败'
        })
        handleClearInterval()
        return Promise.reject()
    }
}
// 轮询请求获取进度条
const handleStartInterval = async (knowledgeId: string[]) => {
    if (!knowledgeId.length) return
    await task(knowledgeId)
    intervalTimer.value = setInterval(async () => {
        await task(knowledgeId)
    }, 5000)
}

const handleClearInterval = () => {
    clearInterval(intervalTimer.value)
    intervalTimer.value = null
}

// 最后一步提交确认
const submit = () => {
    // 根据type跳转到详情页
    const { spaceId, datasetId } = route.params
    router.push(`/space/${spaceId}/knowledge/${datasetId}/detail/${knowledgeId.value[0]}`)
}
onMounted(() => {
    document.addEventListener('visibilitychange', onVisibilitychange)
})
onBeforeUnmount(() => {
    document.removeEventListener('visibilitychange', onVisibilitychange)
    if (intervalTimer.value) {
        handleClearInterval()
    }
})
const onVisibilitychange = () => {
    if (document.visibilityState === 'hidden') {
        handleClearInterval()
    }
    // 用户打开或回到页面
    if (document.visibilityState === 'visible') {
        handleStartInterval(knowledgeId.value)
    }
}
</script>
<style scoped lang="less">
.knowledge-upload {
    display: block;

    :deep(.ant-upload-drag) {
        background: #fff;
        height: 200px;
    }
}
.processing-page {
    margin: 10px auto 0;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
}

.progress-item {
    border: 1px solid rgba(29, 28, 37, 0.08);
    border-radius: 8px;
    padding: 23px 35px 23px 24px;
    margin-bottom: 20px;
}

.label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
}
</style>
