import { INode, INodeData, INodeParams } from '../../../src/Interface'
import { BaseLanguageModel } from '@langchain/core/language_models/base'
import { z } from 'zod'
import { DynamicStructuredTool } from '@langchain/core/tools'
import { RunnableSequence } from '@langchain/core/runnables'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { fetchInfo } from './fetch'

class InfoTool_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'Info工具'
        this.name = 'infoTool'
        this.version = 1.0
        this.type = 'InfoTool'
        this.icon = 'info.svg'
        this.category = 'Tools'
        this.description = 'Info Api工具,可通过自然语言判断是否需要对info url进行内容获取'
        this.baseClasses = [this.type, 'Tool']
        this.tags = ['bilibili']
        this.inputs = [
            {
                label: 'Language Model',
                name: 'model',
                type: 'BaseLanguageModel'
            },
            {
                label: 'Description',
                name: 'description',
                type: 'string',
                description: '描述工具的作用'
            },
            {
                label: 'Return Direct',
                name: 'returnDirect',
                type: 'boolean',
                optional: true
            }
        ]
    }

    async init(nodeData: INodeData): Promise<any> {
        const model = nodeData.inputs?.model as BaseLanguageModel
        const description = (nodeData.inputs?.description as string) || '打开一个或多个带有pageId的info.bilibili.co网站，完成用户需求'
        const returnDirect = nodeData.inputs?.returnDirect as boolean

        const tool = new DynamicStructuredTool({
            name: 'info-fetch',
            description: description,
            returnDirect: returnDirect,
            schema: z.object({
                pageId: z.string().describe('请填写网址里的pageId,多个用逗号隔开'),
                task: z.string().describe('描述需求')
            }),
            func: async ({ pageId, task }, runManager) => {
                const txt = await fetchInfo(pageId)

                if (txt) {
                    const input = `文档:\n${txt}\n\n通过上述内容,现在需要你:${
                        task || '获取内容摘要'
                    }\n输出范围: 回复内容必须控制在300字内完成`

                    // 这里是一个新的chain,所以需要重新传入日志组件
                    const chain = RunnableSequence.from([model, new StringOutputParser()])
                    return chain.invoke(input, { callbacks: runManager?.handlers, metadata: (runManager as any).metadata }).catch((err) => {
                        return `${err}`
                    })
                } else {
                    return '非常抱歉,无法获取该链接中的内容'
                }
            }
        })

        return tool
    }
}

module.exports = { nodeClass: InfoTool_Tools }
