import { BaseChatModel, type BaseChatModelParams } from '@langchain/core/language_models/chat_models'
import { AIMessage, BaseMessage, ChatMessage } from '@langchain/core/messages'
import { ChatResult } from '@langchain/core/outputs'
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager'
import { Logger } from 'winston'
import { ConsoleCallbackHandler } from '../../../src/handler'

/**
 * 消息体里的角色信息
 */
export type BiliIndexMessageRole = 'system' | 'assistant' | 'user'

/**
 * Index模型里的消息接口字段
 */
interface BiliIndexMessage {
    role: BiliIndexMessageRole
    content: string
}

/**
 * 聊天完成时的使用的token记录
 */
interface TokenUsage {
    input_tokens?: number
    output_tokens?: number
    total_tokens?: number
}

/**
 * 请求时的接口参数
 */
interface ChatCompletionRequest {
    messages: BiliIndexMessage[]
    stream?: boolean
}

/**
 * 接口返回时的接口参数
 */
interface ChatCompletionResponse {
    code: number
    message: string
    model: string
    result: string
    usage: TokenUsage
}

/**
 * BiliIndex class的输入.
 */
declare interface BiliIndexChatInput {
    source: string
    model: string
    streaming?: boolean // 暂不支持
    prefixMessages?: BiliIndexMessage[]
    apikey: string
}

/**
 * Function that extracts the custom role of a generic chat message.
 * @param message Chat message from which to extract the custom role.
 * @returns The custom role of the chat message.
 */
function extractGenericMessageCustomRole(message: ChatMessage) {
    if (['system', 'assistant', 'user'].includes(message.role) === false) {
        console.warn(`Unknown message role: ${message.role}`)
    }

    return message.role as BiliIndexMessageRole
}

/**
 * Function that converts a base message to a BiliIndex message role.
 * @param message Base message to convert.
 * @returns The BiliIndex message role.
 */
function messageToBiliIndexRole(message: BaseMessage): BiliIndexMessageRole {
    const type = message._getType()
    switch (type) {
        case 'ai':
            return 'assistant'
        case 'human':
            return 'user'
        case 'system':
            return 'assistant'
        case 'function':
            throw new Error('Function messages not supported')
        case 'generic': {
            if (!ChatMessage.isInstance(message)) throw new Error('Invalid generic chat message')
            return extractGenericMessageCustomRole(message)
        }
        default:
            throw new Error(`Unknown message type: ${type}`)
    }
}

/**
 * 基于bilibili index-2.0的聊天模型.
 * 使用时应该设置apikey
 *
 * @example
 * ```typescript
 * const biliIndex = new ChatBiliIndex({
 *   apikey: "YOUR-API-KEY",
 * });
 *
 * const messages = [new HumanMessage("Hello")];
 *
 * const res = await biliIndex.call(messages);
 *
 * ```
 */
export class ChatBiliIndex extends BaseChatModel implements BiliIndexChatInput {
    static lc_name() {
        return 'ChatBiliIndex'
    }

    get callKeys(): string[] {
        return ['stop', 'signal', 'options']
    }

    get lc_secrets(): { [key: string]: string } | undefined {
        return {
            apikey: 'API_KEY'
        }
    }

    get lc_aliases(): { [key: string]: string } | undefined {
        return undefined
    }

    lc_serializable = true

    apikey: string

    streaming = false

    prefixMessages?: BiliIndexMessage[]

    source = 'bili'

    model = 'index-2.0-long-text'

    apiUrl: string

    maxRetries = 0

    maxConcurrency = 2

    constructor(fields?: Partial<BiliIndexChatInput> & BaseChatModelParams) {
        super(fields ?? {})

        this.apikey = fields?.apikey ?? this.apikey
        if (!this.apikey) {
            throw new Error('apikey API key not found')
        }

        this.streaming = fields?.streaming ?? this.streaming
        this.prefixMessages = fields?.prefixMessages ?? this.prefixMessages
        this.model = fields?.model ?? this.model

        if (this.model === 'index-2.0') {
            this.apiUrl = 'http://ai-gateway.bilibili.co/api/v1/chat/completions?source=bili&model=index-2.0'
        } else {
            throw new Error(`Invalid model name: ${this.model}`)
        }
    }

    /**
     * Get the parameters used to invoke the model
     */
    invocationParams(): Omit<ChatCompletionRequest, 'messages'> {
        return {
            stream: this.streaming
        }
    }

    /**
     * Get the identifying parameters for the model
     */
    identifyingParams() {
        return {
            model: this.model,
            source: this.source,
            ...this.invocationParams()
        }
    }

    /** @ignore */
    async _generate(
        messages: BaseMessage[],
        options?: this['ParsedCallOptions'],
        runManager?: CallbackManagerForLLMRun
    ): Promise<ChatResult> {
        const params = this.invocationParams()

        const chatflowid = (runManager as any)?.metadata?.chatflowid as string
        const chatId = (runManager as any)?.metadata?.chatId as string

        // 日志上报
        let logger: Logger | undefined
        runManager?.handlers.forEach((handler) => {
            if (handler instanceof ConsoleCallbackHandler) {
                logger = handler.logger
            }
        })

        const messagesMapped: BiliIndexMessage[] = messages.map((message) => ({
            role: messageToBiliIndexRole(message),
            content: message.content as string
        }))

        logger?.info('bili-index-2', { params, messages: messagesMapped, chatflowid, chatId })

        const data = params.stream
            ? await new Promise<ChatCompletionResponse>((resolve, reject) => {
                  let response: ChatCompletionResponse
                  let rejected = false
                  let resolved = false
                  this.completionWithRetry(
                      {
                          // ...params,
                          messages: messagesMapped
                      },
                      true,
                      options?.signal,
                      (event) => {
                          const data = JSON.parse(event.data)

                          if (data?.error_code) {
                              if (rejected) {
                                  return
                              }
                              rejected = true
                              reject(new Error(data?.error_msg))
                              return
                          }

                          const message = data as {
                              code: number
                              message: string
                              model: string
                              result: string
                              usage: TokenUsage
                              is_end: boolean
                          }

                          // on the first message set the response properties
                          if (!response) {
                              response = {
                                  code: message.code,
                                  message: message.message,
                                  model: message.model,
                                  result: message.result,
                                  usage: message.usage
                              }
                          } else {
                              response.result += message.result
                              response.usage = message.usage
                          }

                          // TODO this should pass part.index to the callback
                          // when that's supported there
                          // eslint-disable-next-line no-void
                          void runManager?.handleLLMNewToken(message.result ?? '')

                          if (message.is_end) {
                              if (resolved || rejected) {
                                  return
                              }
                              resolved = true
                              resolve(response)
                          }
                      }
                  ).catch((error) => {
                      if (!rejected) {
                          rejected = true
                          reject(error)
                      }
                  })
              })
            : await this.completionWithRetry(
                  {
                      // ...params,
                      messages: messagesMapped
                  },
                  false,
                  options?.signal
              )
                  .then((data) => {
                      if (data?.code !== 0) {
                          logger?.error('bili-index-2', {
                              params,
                              error: `status:${data?.status} ${data?.message} ${data?.data}`,
                              chatflowid,
                              chatId
                          })
                          throw new Error(data?.message || 'LLM通讯异常')
                      }
                      return data
                  })
                  .catch((err) => {
                      logger?.error('bili-index-2', {
                          params,
                          error: `${err}`,
                          chatflowid,
                          chatId
                      })
                      throw new Error(err)
                  })

        const { input_tokens = 0, output_tokens = 0, total_tokens = 0 } = data.usage

        const text = data.result

        return {
            generations: [
                {
                    text,
                    message: new AIMessage(text)
                }
            ],
            llmOutput: {
                tokenUsage: {
                    promptTokens: input_tokens,
                    completionTokens: output_tokens,
                    totalTokens: total_tokens
                }
            }
        }
    }

    /** @ignore */
    async completionWithRetry(
        request: ChatCompletionRequest,
        stream: boolean,
        signal?: AbortSignal,
        onmessage?: (event: MessageEvent) => void
    ) {
        const makeCompletionRequest = async () => {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    ...(stream ? { Accept: 'text/event-stream' } : {}),
                    apikey: `${this.apikey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request),
                signal
            })

            if (response.status !== 200) {
                throw new Error(`LLM抖了一下：${response.status} ${response.statusText}`)
            }

            if (!stream) {
                return response.json()
            }

            if (response.body) {
                // response will not be a stream if an error occurred
                if (!response.headers.get('content-type')?.startsWith('text/event-stream')) {
                    onmessage?.(
                        new MessageEvent('message', {
                            data: await response.text()
                        })
                    )
                    return
                }
                const reader = response.body.getReader()
                const decoder = new TextDecoder('utf-8')
                let data = ''
                let continueReading = true
                while (continueReading) {
                    const { done, value } = await reader.read()
                    if (done) {
                        continueReading = false
                        break
                    }
                    data += decoder.decode(value)
                    let continueProcessing = true
                    while (continueProcessing) {
                        const newlineIndex = data.indexOf('\n')
                        if (newlineIndex === -1) {
                            continueProcessing = false
                            break
                        }
                        const line = data.slice(0, newlineIndex)
                        data = data.slice(newlineIndex + 1)
                        if (line.startsWith('data:')) {
                            const event = new MessageEvent('message', {
                                data: line.slice('data:'.length).trim()
                            })
                            onmessage?.(event)
                        }
                    }
                }
            }
        }

        return this.caller.call(makeCompletionRequest)
    }

    _llmType() {
        return 'bili_Index_2'
    }

    /** @ignore */
    _combineLLMOutput() {
        return []
    }
}
