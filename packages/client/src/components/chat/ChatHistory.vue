<template>
    <div>
        <div
            v-for="(message, index) in messages"
            :key="index"
            :class="[
                'p-4',
                'flex',
                'justify-center',
                'items-start',
                'vertical-top',
                {
                    'bg-blue-100': message.type === 'apiMessage',
                    'bg-gray-50': message.type === 'userMessage'
                }
            ]"
        >
            <img v-if="message.type === 'apiMessage'" src="@/assets/images/robot.png" alt="AI" class="w-6 h-6 ml-2 mr-2" />
            <img v-else-if="message.type === 'userMessage'" src="@/assets/images/account.png" alt="Me" class="w-6 h-6 ml-2 mr-2" />

            <div class="flex flex-col w-full">
                <!-- 消息内容 -->
                <div v-if="message.message" class="break-all">
                    {{ message.message }}
                </div>
                <div class="text-gray-400 text-xs">({{ message.ctime }})</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineProps, watch } from 'vue'
import type { MessageType } from '@/api/chat'

const props = defineProps({
    messages: {
        type: Array as () => MessageType[],
        required: true
    }
})
</script>

<style scoped>
.markdown-answer {
    white-space: pre-wrap;
}
</style>
