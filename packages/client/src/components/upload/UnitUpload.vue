<template>
    <a-upload-dragger
        v-model:file-list="fileList"
        :accept="accept"
        action="/api/v0/common/upload"
        name="files"
        multiple
        showUploadList
        :beforeUpload="beforeUpload"
        @change="handleChange"
        list-type="picture"
    >
        <div class="flex flex-col justify-center items-center h-full">
            <p class="mb-2">
                <img class="w-6 h-6" :src="UploadSvg" alt="UploadSvg" />
            </p>
            <p class="text-sm text-[#383743] mb-2">点击上传或拖拽文档到这里</p>
            <a-typography-text class="text-xs block" type="secondary">{{ description }}</a-typography-text>
            <a-typography-text class="text-xs" type="secondary" v-if="limit"
                >最多可上传 {{ fileList?.length }}\{{ limit }} 个文件
            </a-typography-text>
            <a-typography-text type="danger" v-if="errFileCount"> 失败 {{ errFileCount }} 个</a-typography-text>
        </div>
    </a-upload-dragger>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps, toRaw, computed } from 'vue'
import { message } from 'ant-design-vue'
import type { UploadChangeParam, UploadProps } from 'ant-design-vue'
import UploadSvg from '@/assets/images/upload.svg'

const props = defineProps(['accept', 'description', 'isReady', 'limit'])

const emits = defineEmits(['callback', 'update:isReady'])

const fileList = ref<UploadProps['fileList']>([])
const errFileCount = computed(() => {
    return fileList.value?.filter((item: any) => item.response && item.response?.code !== 0).length || 0
})
const successUids = ref<string[]>([])

const handleChange = (info: UploadChangeParam) => {
    const status = info.file.status
    if (status === 'done') {
        if (info.file.response.code !== 0) {
            info.file.status = 'error'
            message.error(`${info.file.response.message} `)
        } else {
            message.success(`${info.file.name} 上传成功`)
            successUids.value.push(info.file.uid)
        }
    } else if (status === 'error') {
        message.error(`${info.file.name} 上传失败`)
    }

    if (fileList.value?.length && fileList.value?.every((item) => successUids.value.includes(item.uid))) {
        emits('update:isReady', true)
    } else {
        emits('update:isReady', false)
    }
}

const beforeUpload = (file: any) => {
    const reg = new RegExp(`(${props.accept.split(',').join('|')})$`)

    if (props.limit && fileList.value && fileList.value?.length >= props.limit) {
        message.warn(`已达到上传上限(${props.limit})！`)
        return false
    }

    if (!reg.test(file.name)) {
        message.warn(`${file.name} 不支持该文件格式`)
        return false
    }

    fileList.value = [...(fileList.value || []), file]
    return true
}

defineExpose({
    getFiles: () => {
        return fileList.value?.filter((item: any) => item.response.code === 0).map((item: any) => toRaw(item?.response?.data[0]))
    }
})
</script>
