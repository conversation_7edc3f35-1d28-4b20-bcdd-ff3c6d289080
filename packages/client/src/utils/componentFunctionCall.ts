import { createApp, h, type Component } from 'vue'
import { ConfigProvider } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'

export function componentFunctionCall<C extends Component>(
    component: C
): (props?: C extends Component<infer P> ? Partial<P> : Record<string, any>) => void {
    return (props) => {
        const app = createApp(h(ConfigProvider, { locale: zhCN }, [h(component, props)]))
        const root = document.createElement('div')
        app.mount(root)
    }
}
