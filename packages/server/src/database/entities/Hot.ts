import moment from 'moment'
import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('ai_hot')
export class Hot {
    @PrimaryGeneratedColumn()
    id: number

    @Column()
    platform: string

    @Column()
    title: string

    @Column()
    url: string

    @Column()
    hot: number

    @Column()
    rank: number

    @Column()
    log_date: string

    @Column()
    log_hour: string

    @CreateDateColumn({
        type: 'datetime',
        update: false,
        transformer: {
            to: (value: string) => value,
            from: (value: Date) => moment(value).format('YYYY-MM-DD HH:mm:ss')
        }
    })
    ctime: Date

    @UpdateDateColumn({
        type: 'datetime',
        update: false,
        transformer: {
            to: (value: string) => value,
            from: (value: Date) => moment(value).format('YYYY-MM-DD HH:mm:ss')
        }
    })
    mtime: Date
}
