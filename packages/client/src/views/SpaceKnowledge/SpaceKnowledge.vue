<template>
    <div class="flex justify-between items-center mb-[20px]">
        <a-input-search
            allowClear
            v-model:value="searchName"
            placeholder="请输入数据集名称或描述"
            style="width: 250px"
            @search="onSearch"
        />
        <a-button type="primary" @click="handleOpen">创建数据集</a-button>
    </div>

    <a-table :dataSource="searchedItems" :columns="columns" class="copilot-concise-table" :scroll="{ y: tableH, x: 1000 }">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
                <div style="color: var(--color-symbol-medium)">{{ record.title }}</div>
                <div @click.stop>{{ record.id }}</div>
            </template>
            <template v-if="column.key === 'model'">
                <div v-if="Number(record.model) === 2">
                    <svg class="h-4 w-4 fill-sky-500" viewBox="0 0 24 24">
                        <path
                            fill-rule="evenodd"
                            d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813A3.75 3.75 0 007.466 7.89l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    AI开放平台
                </div>
                <div v-else>数平RAG平台</div>
            </template>
            <template v-else-if="column.key === 'action'">
                <a-space>
                    <router-link :to="`${route.path}/${record.id}`">
                        <a-button type="link"> 管理 </a-button>
                    </router-link>

                    <!-- <a-dropdown v-if="record.model === 2">
                        <a class="ant-dropdown-link flex items-center" @click.prevent>
                            一键生成流程
                            <ChevronDownIcon class="h-4 w-4 ml-1" />
                        </a>
                        <template #overlay>
                            <a-menu
                                @click="
                                    ({ key }: any) => {
                                        onNewDuplicate(key, 'AiVector Chain', record.id, record.spaceId)
                                    }
                                "
                            >
                                <a-menu-item key="AiVectorRAG"> 使用自研模型创建 </a-menu-item>
                                <a-menu-item key="AiVectorRAG Moonshot"> 使用Moonshot模型创建 </a-menu-item>
                                <a-menu-item key="AiVectorRAG Coze"> 使用4o模型创建 </a-menu-item>
                                <a-menu-item key="AiVectorRAG Agent"> 基于Agent多工作流的方式创建 </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>

                    <a-button
                        v-else
                        type="link"
                        @click="onNewDuplicate('DatasetModel QnA', '知识库DatasetModel', record.id, record.spaceId)"
                    >
                        一键生成流程
                    </a-button> -->

                    <a-button type="link" @click="handleOpen(record)" :disabled="!isSelf(record.creator)">编辑</a-button>
                    <a-button type="link" danger @click="handleDel(record)" :disabled="!isSelf(record.creator)">删除</a-button>
                </a-space>
            </template>
        </template>
    </a-table>

    <create-dataset ref="createRef" :space-id="route.params.spaceId" @callback="getData" />
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { onMounted, ref, computed } from 'vue'
import { updateDataset, getDatasets, getMarketplacesTmpl } from '@/api/space'
import CreateDataset from '@/views/SpaceKnowledge/components/CreateDataset.vue'
import { message, Modal, notification } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import Fuse from 'fuse.js'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import type { MenuProps } from 'ant-design-vue'
const { isSelf } = useUserStore()
const createRef = ref<any>(null)

const route = useRoute()

const searchName = ref('')

const dataSource = ref([])

const searchedItems = ref([])

const fuse = ref<any>(null)

const tmplData = ref<any>({})

const optionsFuse = {
    keys: ['title', 'description'],
    threshold: 0.3,
    includeScore: true
}

const columns = [
    {
        key: 'title',
        title: '数据集',
        dataIndex: 'title',
        minWidth: 300
    },
    {
        title: '描述',
        dataIndex: 'description',
        key: 'description'
    },
    {
        title: '知识库服务',
        dataIndex: 'model',
        key: 'model',
        minWidth: 200
    },
    {
        title: '编辑时间',
        dataIndex: 'mtime',
        key: 'mtime',
        width: 200
    },
    {
        title: '操作',
        key: 'action',
        width: 340,
        fixed: 'right'
    }
]

const tableH = computed(() => {
    return document.body.offsetHeight - 240
})
onMounted(() => {
    getData()
    getTmpl()
})

const getData = async () => {
    const res = await getDatasets(route.params.spaceId as string)
    dataSource.value = res.data
    fuse.value = new Fuse(dataSource.value, optionsFuse)
    onSearch()
}

const getTmpl = async () => {
    const res = (await getMarketplacesTmpl()) as any
    tmplData.value = res
}

const onSearch = () => {
    if (!searchName.value) {
        searchedItems.value = dataSource.value.slice() // 使用slice()创建items的副本，避免直接引用
        return
    }
    searchedItems.value = fuse.value.search(searchName.value).map((result: any) => result.item)
}

const handleOpen = (data: any) => {
    createRef.value?.open(data ? { ...data } : {})
}

const handleDel = (data: any) => {
    Modal.confirm({
        title: `警告`,
        content: `确认要删除 ${data.title} 数据集`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await updateDataset({ id: data.id, isDeleted: 1, spaceId: data.spaceId })
            message.success(`${data.title} 删除成功`)
            await getData()
        }
    })
}

const onNewDuplicate = (templateName: string, labelName: string, datasetId: string, spaceId: string) => {
    const tempInfo = tmplData.value.find((item: any) => item.templateName == templateName)

    if (!tempInfo) return notification.error({ message: '失败', description: '一键生成流程失败' })
    const flowData = JSON.parse(tempInfo.flowData)

    flowData.nodes.map((item: any) => {
        console.log(item.data)
        if (item.data.label == labelName) {
            item.data.inputs.datasetId = datasetId
        }
    })

    localStorage.setItem('duplicatedFlowData', JSON.stringify(flowData))
    window.open(`/develop/canvas/?spaceId=${spaceId}`, '_blank')
}
</script>
