<template>
    <a-layout class="h-full">
        <div class="flex flex-0 justify-between items-center mb-[20px]">
            <a-input-search
                allowClear
                v-model:value="flowName"
                placeholder="请输入工作流名称或描述"
                style="width: 250px"
                @search="onSearch"
            />
            <div>
                <a-button class="mr-3" :href="`/develop/agentcanvas?spaceId=${route.params.spaceId}`"> 旧版 AgentFlow </a-button>
                <a-badge count="V2" class="mr-5">
                    <a-button type="primary" :href="`/develop/v2/agentcanvas?spaceId=${route.params.spaceId}`"> 创建 AgentFlow </a-button>
                </a-badge>
            </div>
        </div>
        <a-layout-content class="flex-1 overflow-y-auto">
            <a-skeleton :loading="loading" active :paragraph="{ rows: 4 }">
                <a-empty class="pt-[150px]" v-if="searchedItems.length === 0">
                    <template #description>
                        <span> 构建属于你的第一个工作流 </span>
                    </template>
                    <a-button type="primary" :href="`/develop/v2/agentcanvas?spaceId=${route.params.spaceId}`">创建AgentFlowV2</a-button>
                </a-empty>
                <a-row class="w-full" :gutter="[10, 10]" :wrap="true">
                    <a-col v-for="item in searchedItems" :span="8" :xxl="6" :key="item.id">
                        <a-card hoverable class="w-full" :bodyStyle="{ paddingBottom: '14px' }">
                            <a class="block w-full h-full text-black" :href="`/chat/${item.id}`">
                                <a-card-meta class="p-0">
                                    <template #title>
                                        <div class="flex">
                                            <a-avatar class="flex-0" shape="square" :size="48" :src="item.avatar || logoPng" />
                                            <div class="ml-[10px] flex-1 overflow-hidden">
                                                <div class="overflow-hidden text-ellipsis text-nowrap">
                                                    {{ item.name }}
                                                </div>
                                                <a-tag color="cyan">{{ item.type || 'CHATFLOW' }}</a-tag>

                                                <span v-if="item.category">
                                                    <a-tag color="orange" v-for="ytem in item.category.split(';')" :key="ytem">
                                                        {{ ytem }}
                                                    </a-tag>
                                                </span>
                                            </div>
                                        </div>
                                    </template>

                                    <template #description>
                                        <div class="h-[20px] mb-[4px] overflow-hidden text-ellipsis text-nowrap">
                                            {{ item.description || '暂无描述' }}
                                        </div>

                                        <div class="pt-[10px]">
                                            <!-- <div v-if="item?.flowData?.nodes" class="flex gap-[8px]">
                                                <a-avatar
                                                    v-for="it in [...item?.flowData?.nodes].splice(0, 6)"
                                                    :key="it.id"
                                                    style="background-color: #e3f1fd"
                                                    :src="'/api/v1/node-icon/' + it.data.name"
                                                />
                                                <a-avatar
                                                    v-if="item?.flowData?.nodes.length >= 6"
                                                    style="color: black; background-color: #e3f1fd"
                                                >
                                                    +{{ item?.flowData?.nodes.length - 6 }}
                                                </a-avatar>
                                            </div> -->
                                        </div>
                                        <a-space class="h-[24px] mt-[10px] flex justify-between">
                                            <a-space>
                                                <UserOutlined />
                                                <a-tooltip title="创建人" :mouseEnterDelay="0.3">{{ item?.creator }}</a-tooltip>
                                            </a-space>
                                            <a-space>
                                                <HistoryOutlined />
                                                <a-tooltip title="最近编辑" :mouseEnterDelay="0.3">
                                                    {{ dayjs(item?.updatedDate).format('YY-MM-DD HH:mm') }}
                                                </a-tooltip>
                                            </a-space>
                                        </a-space>
                                    </template>
                                </a-card-meta>
                            </a>
                            <template #actions>
                                <a-button
                                    type="link"
                                    class="!text-black hover:!text-[#1677ff]"
                                    :href="
                                        (item.type === 'MULTIAGENT'
                                            ? '/develop/agentcanvas/'
                                            : item.type === 'AGENTFLOW'
                                            ? '/develop/v2/agentcanvas/'
                                            : '/develop/canvas/') + `${item.id}?spaceId=${route.params.spaceId}`
                                    "
                                    target="_blank"
                                    block
                                >
                                    <NodeIndexOutlined /> 编排
                                </a-button>
                                <a-button
                                    type="link"
                                    class="text-black hover:!text-[#1677ff]"
                                    :disabled="!isSelf(item.creator)"
                                    @click="handleSetting(item)"
                                    block
                                >
                                    <EditOutlined /> 设置
                                </a-button>
                                <a-dropdown>
                                    <a-button type="link" class="text-black hover:!text-[#1677ff]" block> <MenuOutlined />操作 </a-button>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item key="history" @click="handleOpenMessageHistoryModal(item)"
                                                >查看历史消息</a-menu-item
                                            >
                                            <a-menu-item key="1" @click="handleDuplicate(item)"> 复制</a-menu-item>
                                            <a-menu-item key="3" @click="handleExport(item)"> 导出</a-menu-item>
                                            <a-menu-divider />
                                            <a-menu-item key="4" :disabled="!isSelf(item.creator)" @click="handleStarterPrompts(item)">
                                                设置提示语
                                            </a-menu-item>
                                            <a-menu-divider />
                                            <a-menu-item key="6" @click="handleDel(item)" :disabled="!isSelf(item.creator)"
                                                >删除</a-menu-item
                                            >
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </template>
                        </a-card>
                    </a-col>
                </a-row>
            </a-skeleton>
        </a-layout-content>
    </a-layout>
    <starter-prompts ref="starterPromptsRef" />
    <flow-edit ref="flowEditRef" @callback="getFlowsData" />
    <a-modal v-model:open="messageVisible" title="查看消息" :width="1000" :footer="null">
        <MsgHistory :chatflowId="selectData.id" />
    </a-modal>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { onMounted, ref, watch } from 'vue'
import { delChatFlowV1, getChatFlowList } from '@/api/space'
import type { IChatflows } from '@/api/space'
import { EditOutlined, MenuOutlined, NodeIndexOutlined, UserOutlined, HistoryOutlined } from '@ant-design/icons-vue'
import { generateExportFlowData } from '@/utils/genericHepler'
import FlowEdit from '@/views/SpaceWorkflow/components/FlowEdit.vue'
import logoPng from '@/assets/images/bilibili.png'
import StarterPrompts from '@/views/SpaceWorkflow/components/StarterPrompts.vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/stores/user'
import MsgHistory from './components/MsgHistory.vue'
import Fuse from 'fuse.js'

const { isSelf } = useUserStore()
const loading = ref(true)

const flowList = ref<IChatflows[]>([])

const searchedItems = ref<(IChatflows & { flowData: any })[]>([])

const fuse = ref<Fuse<IChatflows>>(new Fuse([], {}))

const route = useRoute()

const flowName = ref('')

const starterPromptsRef = ref<any>(null)

const flowEditRef = ref<any>(null)

const messageVisible = ref(false)

const selectData = ref<any>({})

const optionsFuse = {
    keys: ['name', 'description', 'creator'],
    threshold: 0.3,
    includeScore: true
}

onMounted(() => {
    getFlowsData()
})
watch(route, () => {
    getFlowsData()
})

const getFlowsData = async () => {
    const flowsRes = await getChatFlowList(route.params.spaceId as string)

    flowList.value = flowsRes.data.map((item) => {
        return {
            ...item,
            flowDataStr: item.flowData,
            flowData: JSON.parse(item.flowData) as any
        }
    })
    fuse.value = new Fuse(flowList.value, optionsFuse)
    loading.value = false
    onSearch()
}

const onSearch = () => {
    if (!flowName.value) {
        searchedItems.value = flowList.value.slice() // 使用slice()创建items的副本，避免直接引用
        return
    }
    searchedItems.value = fuse.value?.search(flowName.value).map((result) => result.item)
}

const handleDuplicate = (data: any) => {
    localStorage.setItem('duplicatedFlowData', data.flowDataStr)
    if (data.type === 'MULTIAGENT') {
        window.open(`/develop/agentcanvas/?spaceId=${data.spaceId}`, '_blank')
    } else if (data.type === 'AGENTFLOW') {
        window.open(`/develop/v2/agentcanvas/?spaceId=${data.spaceId}`, '_blank')
    } else {
        window.open(`/develop/canvas/?spaceId=${data.spaceId}`, '_blank')
    }
}
const handleOpenMessageHistoryModal = (data: any) => {
    messageVisible.value = true
    selectData.value = data
}
const handleExport = (data: any) => {
    let dataStr = JSON.stringify(generateExportFlowData(data.flowData), null, 2)
    let dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr)

    let exportFileDefaultName = `${data.name} Chatflow.json`

    let linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
}

const handleDel = async (data: any) => {
    Modal.confirm({
        title: `警告`,
        content: `确认要删除 ${data.name} 工作流`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await delChatFlowV1(data.id)
            message.success(`${data.name} 删除成功`)
            await getFlowsData()
        }
    })
}
const handleStarterPrompts = (data: any) => {
    starterPromptsRef.value?.open(data)
}

const handleSetting = (data: any) => {
    flowEditRef.value?.open(data)
}
</script>
