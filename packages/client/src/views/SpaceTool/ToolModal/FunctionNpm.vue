<template>
  <a-modal
      title="NPM 库支持列表"
      width="600px"
      v-model:open="state.open"
      @ok="onSubmit"
      :bodyStyle="{maxHeight:'600px',overflow:'auto'}"
  >
    使用<code class="bg-[#f5f5f5] border-r-4 pl-2 pr-2">const http = require('node-fetch');</code>来引入npm库

    <h5>已包含在NodeVM中的nodejs原生库</h5>
    <ul>
      <li>assert</li>
      <li>buffer</li>
      <li>crypto</li>
      <li>events</li>
      <li>http</li>
      <li>https</li>
      <li>net</li>
      <li>path</li>
      <li>querystring</li>
      <li>timers</li>
      <li>tls</li>
      <li>url</li>
      <li>zli</li>
    </ul>

    <h5>已包含在NodeVM中的第三方库</h5>
    <ul>
      <li>@aws-sdk/client-bedrock-runtime</li>
      <li>@aws-sdk/client-dynamodb</li>
      <li>@aws-sdk/client-s3</li>
      <li>@elastic/elasticsearch</li>
      <li>@dqbd/tiktoken</li>
      <li>@getzep/zep-js</li>
      <li>@gomomento/sdk</li>
      <li>@gomomento/sdk-core</li>
      <li>@google-ai/generativelanguage</li>
      <li>@huggingface/inference</li>
      <li>@notionhq/client</li>
      <li>@opensearch-project/opensearch</li>
      <li>@pinecone-database/pinecone</li>
      <li>@qdrant/js-client-rest</li>
      <li>@supabase/supabase-js</li>
      <li>@upstash/redis</li>
      <li>@zilliz/milvus2-sdk-node</li>
      <li>apify-client</li>
      <li>axios</li>
      <li>cheerio</li>
      <li>chromadb</li>
      <li>cohere-ai</li>
      <li>d3-dsv</li>
      <li>faiss-node</li>
      <li>form-data</li>
      <li>google-auth-library</li>
      <li>graphql</li>
      <li>html-to-text</li>
      <li>ioredis</li>
      <li>langchain</li>
      <li>langfuse</li>
      <li>langsmith</li>
      <li>linkifyjs</li>
      <li>lunary</li>
      <li>mammoth</li>
      <li>moment</li>
      <li>mongodb</li>
      <li>mysql2</li>
      <li>node-fetch</li>
      <li>node-html-markdown</li>
      <li>notion-to-md</li>
      <li>openai</li>
      <li>pdf-parse</li>
      <li>pdfjs-dist</li>
      <li>pg</li>
      <li>playwright</li>
      <li>puppeteer</li>
      <li>redis</li>
      <li>replicate</li>
      <li>srt-parser-2</li>
      <li>typeorm</li>
      <li>weaviate-ts-clien</li>
    </ul>
    <template #footer>
      <a-button type="primary"  @click="onSubmit">关闭</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { defineExpose, reactive} from 'vue'

const state = reactive({
  open: false,
})

const open = ()=>{
  state.open = true
}

const onSubmit = ()=>{
  state.open = false
}

defineExpose({open})
</script>
<style scoped>
ul li{
  color: #a89f9f
}
</style>