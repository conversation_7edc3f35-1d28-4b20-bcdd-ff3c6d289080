import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { z } from 'zod'
import { BaseDynamicToolInput, DynamicTool, StructuredTool, ToolInputParsingException } from '@langchain/core/tools'
import { RunnableConfig, RunnableSequence } from '@langchain/core/runnables'
import { CallbackManager, CallbackManagerForToolRun, Callbacks, parseCallbackConfigArg } from '@langchain/core/callbacks/manager'
import { getBaseClasses } from '../../../src'
import { getBaiKes } from './api'
import { ChatPromptTemplate } from '@langchain/core/prompts'

type ZodObjectAny = z.ZodObject<any, any, any, any>
type IFlowConfig = { sessionId?: string; chatId?: string; input?: string; state?: ICommonObject }
interface DynamicStructuredToolInput<T extends z.ZodObject<any, any, any, any> = z.ZodObject<any, any, any, any>>
    extends BaseDynamicToolInput {
    func?: (input: z.infer<T>, runManager?: CallbackManagerForToolRun, flowConfig?: IFlowConfig) => Promise<string>
    schema: T
}

const PROMPT = `请根据以下知识库文档中的关键信息，回答用户问题，当如果文档内容与用户的问题不相关时请回复“无相关内容”。

知识库内的信息
{content}
`

class BaiKeGenerate_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'BaiKeDetail'
        this.name = 'baiKeDetail'
        this.version = 1.0
        this.type = 'CustomTool'
        this.icon = 'baike.png'
        this.category = 'Tools'
        this.description = '用于百科知识库内容查询，使用时填入百科知识库的id'
        this.baseClasses = [this.type, 'DynamicTool', ...getBaseClasses(DynamicTool)]
        this.tags = ['bilibili']
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const flow = { chatflowId: options.chatflowid }

        const input = {
            name: this.name,
            description: this.description
        }

        const schema = z.object({
            id: z.string().describe('填入BaiKe或Tapd的id')
        }) as any

        const func = async ({ id }: { id: string }, _?: CallbackManagerForToolRun, flowConfig?: IFlowConfig) => {
            if (!id) return ''

            const res = await getBaiKes(id, 'tapd_name,content,prod_owner_nick,tech_owner_nick,updater,ctime')

            if (res.code !== 0) return ''

            let txt = ''
            for (const result of res.data) {
                txt += `创建时间: ${result.ctime} \n`
                txt += `标题: ${result.tapd_name} \n`
                const ownerString = [result.prod_owner_nick, result.tech_owner_nick].filter(Boolean).join('; ')
                txt += `负责人：${ownerString || result.updater_nick} \n`
                // content是一个markdown格式的文本，删除markdown里的图片链接和http开头的jpg/png图片链接
                let content = result.content.replace(/!\[.*?\]\(.*?\)/g, '').replace(/https?:\/\/\S+\.(jpg|png)/g, '')
                txt += `详细内容: ${content} \n`
                txt += `\n`
            }

            return txt
        }

        const tool = new DynamicStructuredTool({ ...input, func, schema })
        tool.setFlowObject(flow)
        return tool
    }
}

class DynamicStructuredTool<T extends z.ZodObject<any, any, any, any> = z.ZodObject<any, any, any, any>> extends StructuredTool<
    T extends ZodObjectAny ? T : ZodObjectAny
> {
    static lc_name() {
        return 'DynamicStructuredTool'
    }

    name: string

    description: string

    func: DynamicStructuredToolInput['func']

    // @ts-ignore
    schema: T

    private flowObj: any

    constructor(fields: DynamicStructuredToolInput<T>) {
        super(fields)
        this.name = fields.name
        this.description = fields.description
        this.func = fields.func
        this.schema = fields.schema
    }

    async call(arg: any, configArg?: RunnableConfig | Callbacks, tags?: string[], flowConfig?: IFlowConfig): Promise<string> {
        const config = parseCallbackConfigArg(configArg)
        if (config.runName === undefined) {
            config.runName = this.name
        }
        let parsed
        try {
            parsed = await this.schema.parseAsync(arg)
        } catch (e) {
            throw new ToolInputParsingException(`Received tool input did not match expected schema`, JSON.stringify(arg))
        }
        const callbackManager_ = await CallbackManager.configure(
            config.callbacks,
            this.callbacks,
            config.tags || tags,
            this.tags,
            config.metadata,
            this.metadata,
            { verbose: this.verbose }
        )
        const runManager = await callbackManager_?.handleToolStart(
            this.toJSON(),
            typeof parsed === 'string' ? parsed : JSON.stringify(parsed),
            undefined,
            undefined,
            undefined,
            undefined,
            config.runName
        )
        let result
        try {
            result = await this._call(parsed, runManager, flowConfig)
        } catch (e) {
            await runManager?.handleToolError(e)
            throw e
        }
        if (result && typeof result !== 'string') {
            result = JSON.stringify(result)
        }
        await runManager?.handleToolEnd(result)
        return result
    }

    // @ts-ignore
    protected _call(arg: any, runManager?: CallbackManagerForToolRun, flowConfig?: IFlowConfig): Promise<string> {
        let flowConfiguration: ICommonObject = {}
        if (typeof arg === 'object' && Object.keys(arg).length) {
            for (const item in arg) {
                flowConfiguration[`$${item}`] = arg[item]
            }
        }

        // inject flow properties
        if (this.flowObj) {
            flowConfiguration['$flow'] = { ...this.flowObj, ...flowConfig }
        }

        return this.func!(arg as any, runManager, flowConfiguration)
    }

    setFlowObject(flow: any) {
        this.flowObj = flow
    }
}

module.exports = { nodeClass: BaiKeGenerate_Tools }
