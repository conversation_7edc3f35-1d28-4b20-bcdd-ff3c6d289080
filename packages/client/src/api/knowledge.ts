import { apiV0, type ResponseData } from './client'

export type IUploadFile = {
    extname: string
    fileKey: string
    filename: string
    location: string
    mimeType: string
    autoUpdate?: string
    remoteUrl?: string
}

type Document = {
    filename: string
    extname: string
    fileKey: string
    location: string
    mimeType: string
}
type TableSheet = {
    sheetId: number
    headerLineIdx: number
    startLineIdx: number
}
type TextSplitOptions = {
    chunkSize: number
    separator: string
}
export type TableMeta = {
    id: number
    columnName: String
    isIndex: Boolean
}
type KnowledgeDoc = {
    datasetId: string
    documents: IUploadFile[]
    textSplit?: TextSplitOptions | {}
    formatType?: number
    tableSheet?: TableSheet
    tableMeta?: TableMeta | {}
}

type KnowledgeTable = {
    document: Document
    tableSheet: TableSheet
    tableMeta?: any[]
}
type ISliceParams = {
    knowledgeId: string
    knowledgeSliceId?: string
    content: string
}

// 知识创建
export const createKnowledgeDoc = (data: KnowledgeDoc) => {
    return apiV0.post<ResponseData<IKnowledgeDetail[]>>('/v0/knowledge/document/create', data)
}
export type IKnowledgeProgress = {
    errMsg: string
    knowledgeId: string
    name: string
    progress: number
}

// 进度条
export const getKnowledgeProgress = (data: { knowledgeId: string[] }) => {
    return apiV0.post<ResponseData<Record<string, IKnowledgeProgress>>>('/v0/knowledge/progress', data)
}
export type IKnowledgeSliceList = {
    content: string
    ctime: string
    datasetId: string
    documentIndex: number
    hit: number
    id: string
    knowledgeId: string
    sentenceId: string
}
// 获取切片列表
export const getKnowledgeSliceList = (datasetId: string, knowledgeId: string, page: number, pageSize: number) => {
    return apiV0.get<ResponseData<{ rows: IKnowledgeSliceList[]; count: number }>>(
        `/v0/knowledge/slice/list?datasetId=${datasetId}&knowledgeId=${knowledgeId}&page=${page}&pageSize=${pageSize}`
    )
}

type ITableSchema = {
    previewData: Record<string, string>[]
    sheetList: { id: number; isIndex: number; name: string; totalRow: number }[]
    tableMeta: TableMeta[]
}

// 获取表格结构
export const getKnowledgeTable = (data: KnowledgeTable) => {
    return apiV0.post<ResponseData<ITableSchema>>('/v0/knowledge/tableSchema/get', data)
}
type IKnowledgeDetail = {
    autoUpdate: number
    creator: string
    ctime: string
    datasetId: string
    extname: string
    fileKey: string
    filename: string
    id: string
    location: string
    mimeType: string
    mtime: string
    remoteUrl: string
    spaceId: string
    status: number
    tableMeta: TableMeta[]
    tableSheet: TableSheet | {}
    textSplit: TextSplitOptions | {}
}
export const getKnowledgeDetail = (datasetId: string, knowledgeId: string) => {
    return apiV0.get<ResponseData<IKnowledgeDetail>>(`/v0/knowledge/detail?datasetId=${datasetId}&knowledgeId=${knowledgeId}`)
}
// 新增单个切片
export const sliceAdd = (data: ISliceParams) => {
    return apiV0.post('/v0/knowledge/slice/add', data)
}
// 文档切片更新
export const sliceUpdate = (data: ISliceParams) => {
    return apiV0.post('/v0/knowledge/slice/update', data)
}
// 文档切片删除
export const sliceDelete = (data: { knowledgeId: string; knowledgeSliceId: string[] }) => {
    return apiV0.post('/v0/knowledge/slice/delete', data)
}
type IMarkdownUpload = {
    extname: string
    fileKey: string
    filename: string
    location: string
}
// markdown文本转文件上传
export const markdownUpload = (data: { content: string; filename: string }) => {
    return apiV0.post<ResponseData<IMarkdownUpload>>('/v0/common/markdown/upload', data)
}
export type ISliceDiagnose = {
    id: string
    datasetId: string
    knowledgeId: string
    documentIndex: number
    sentenced: number
    ctime: string
    content: string
    hit: number
    udesc: string
}
/**
 * 切片诊断优化
 */
export const sliceDiagnose = (data: { datasetId: string; page: number; pageSize: number }) => {
    return apiV0.post<ResponseData<{ rows: ISliceDiagnose[]; count: number }>>('/v0/knowledge/slice/diagnose', data)
}
/**
 * 文档迁移
 * @param data
 * @returns
 */
export const knowldegeMove = (data: { knowledgeIds: string[]; datasetId: string }) => {
    return apiV0.post<ResponseData>('/v0/knowledge/move', data)
}
