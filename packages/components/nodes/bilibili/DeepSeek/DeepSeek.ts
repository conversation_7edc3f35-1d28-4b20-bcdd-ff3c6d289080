import { ChatOpenAI, ChatOpenAIFields } from '@langchain/openai'
import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'

class DeepSeek_ChatModels implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    description: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'DeepSeek'
        this.name = 'deepSeek'
        this.version = 2.0
        this.type = 'DeepSeek'
        this.icon = 'deepseek.svg'
        this.category = 'Chat Models'
        this.tags = ['bilibili']
        this.description = '支持V3和R1'
        this.baseClasses = [this.type, 'BaseChatModel', ...getBaseClasses(ChatOpenAI)]
        this.inputs = [
            {
                label: 'Model Name',
                name: 'modelName',
                type: 'string',
                description: '指定模型名称，注意：官网的模型名称和云厂商的可能存在不同，DeepSeek R1不支持Function Call',
                placeholder: 'deepseek-chat',
                default: 'deepseek-chat'
            },
            {
                label: 'Base Path',
                name: 'basePath',
                type: 'string',
                description: '支持填写任意符合openai sdk规范的url',
                placeholder: 'https://api.deepseek.com/v1',
                default: 'https://api.deepseek.com/v1',
                additionalParams: true
            },
            {
                label: 'API Key',
                name: 'apiKey',
                type: 'string',
                additionalParams: true
            },
            {
                label: 'Stream',
                name: 'streaming',
                description: '开启流模式',
                type: 'boolean',
                additionalParams: true
            },
            {
                label: 'Temperature',
                name: 'temperature',
                type: 'number',
                step: 0.1,
                default: 0.7,
                optional: true,
                additionalParams: true
            },
            {
                label: 'Max Tokens',
                name: 'maxTokens',
                type: 'number',
                step: 1,
                default: 4096,
                optional: true,
                additionalParams: true
            }
        ]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const apiKey = nodeData.inputs?.apiKey as string
        const temperature = nodeData.inputs?.temperature as string
        const maxTokens = nodeData.inputs?.maxTokens as string
        const streaming = nodeData.inputs?.streaming as boolean
        const modelName = (nodeData.inputs?.modelName as string) || 'deepseek-chat'
        const basePath = (nodeData.inputs?.basePath as string) || 'https://api.deepseek.com/v1'

        const obj: ChatOpenAIFields = {
            openAIApiKey: apiKey,
            temperature: parseFloat(temperature),
            model: modelName,
            streaming: streaming ?? true,
            configuration: {
                baseURL: basePath
            }
        }

        if (maxTokens) obj.maxTokens = parseInt(maxTokens, 10)

        const model = new ChatOpenAI(obj)

        return model
    }
}

module.exports = { nodeClass: DeepSeek_ChatModels }
