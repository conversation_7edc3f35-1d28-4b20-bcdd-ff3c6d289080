import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '',
            name: 'layout',
            component: () => import('@/components/layout/LayoutWrapper.vue'),
            redirect: { name: 'home' },
            children: [
                { path: 'home', name: 'home', component: () => import('@/views/home/<USER>') },
                { path: 'market', name: 'market', component: () => import('@/views/SpaceMarket/MarketMain.vue') },
                { path: 'plugin', name: 'plugin', component: () => import('@/views/SpaceTool/ToolMarket.vue') },
                {
                    path: 'hotspots',
                    name: 'hotspots',
                    children: [
                        {
                            path: '',
                            name: 'hotspots-main',
                            component: () => import('@/views/Hotspots/HotspotsMain.vue')
                        }
                    ]
                },
                {
                    path: 'mcp',
                    name: 'mcp',
                    children: [
                        {
                            path: 'market',
                            name: 'mcp-market',
                            component: () => import('@/views/mcp/MCPMarket.vue')
                        },
                        {
                            path: 'detail/:id',
                            name: 'mcp-detail',
                            component: () => import('@/views/mcp/MCPDetail.vue')
                        }
                    ]
                },
                {
                    path: '/space/:spaceId',
                    name: 'space',
                    children: [
                        {
                            path: '',
                            name: 'space',
                            component: () => import('@/components/space/SpaceWrapper.vue'),
                            redirect: { name: 'workflow' },
                            children: [
                                {
                                    path: 'workflow',
                                    name: 'workflow',
                                    component: () => import('@/views/SpaceWorkflow/SpaceWorkflow.vue')
                                },
                                {
                                    path: 'knowledge',
                                    name: 'knowledge',
                                    component: () => import('@/views/SpaceKnowledge/SpaceKnowledge.vue')
                                },
                                {
                                    path: 'tool',
                                    name: 'tool',
                                    component: () => import('@/views/SpaceTool/SpaceToolList.vue')
                                },
                                {
                                    path: 'space-mng',
                                    name: 'space-mng',
                                    component: () => import('@/views/SpaceMng/index.vue')
                                },
                                {
                                    path: 'mcp',
                                    name: 'space-mcp',
                                    component: () => import('@/views/mcp/SpaceMCPList.vue')
                                }
                            ]
                        },
                        {
                            path: 'knowledge/:datasetId',
                            name: 'knowledgeUnit',
                            component: () => import('@/views/SpaceKnowledge/KnowledgeUnit.vue')
                        },
                        {
                            path: 'knowledge/:datasetId/upload',
                            name: 'KnowledgeUpload',
                            component: () => import('@/views/SpaceKnowledge/KnowledgeUpload.vue')
                        },
                        {
                            path: 'knowledge/:datasetId/detail/:knowledgeId',
                            name: 'KnowledgeReview',
                            component: () => import('@/views/SpaceKnowledge/KnowledgeReview.vue')
                        }
                    ]
                }
            ]
        },
        {
            path: '/chat/:id',
            component: () => import('@/views/chat/ChatMain.vue')
        },
        {
            path: '/toolMain/:spaceId/tool/:id',
            name: '/toolMain',
            component: () => import('@/views/SpaceTool/ToolMain.vue')
        }
    ]
})

export default router
