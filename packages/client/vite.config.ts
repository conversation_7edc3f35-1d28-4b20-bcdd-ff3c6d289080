import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv, Plugin } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import copy from 'rollup-plugin-copy'

// import BundleAnalyzerPlugin from 'vite-bundle-analyzer'
// https://vitejs.dev/config/
import monacoEditorPlugin from 'vite-plugin-monaco-editor'
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd())

    return {
        base: process.env.NODE_ENV === 'production' ? env.VITE_PUBLIC_PATH : '',
        build: {
            modulePreload: false,
            rollupOptions: {
                plugins: [
                    copy({
                        targets: [{ src: 'public/*.html', dest: 'dist' }],
                        hook: 'writeBundle' // 在构建完成后执行复制
                    })
                ]
            }
        },
        plugins: [
            // @ts-ignore
            monacoEditorPlugin.default({
                publicPath: '/',
                forceBuildCDN: true,
                languageWorkers: ['editorWorkerService', 'typescript'],
                customDistPath: () => './dist'
            }),
            vue({
                template: {
                    compilerOptions: {
                        isCustomElement: (tag) => tag.startsWith('b-')
                    }
                }
            }),
            Components({
                resolvers: [
                    AntDesignVueResolver({
                        importStyle: false // css in js
                    })
                ]
            }) as unknown as Plugin
            // BundleAnalyzerPlugin()
        ],
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            }
        },
        optimizeDeps: {
            include: [`monaco-editor/esm/vs/language/typescript/ts.worker`, `monaco-editor/esm/vs/editor/editor.worker`]
        },
        server: {
            port: 8082,
            host: '0.0.0.0',
            proxy: {
                '/api': 'http://127.0.0.1:3000',
                // '/api': 'http://pre-copilot.bilibili.co',
                '/canvas': 'http://uat-copilot.bilibili.co'
            }
        }
    }
})
