<template>
    <div class="pt-[10px] overflow-scroll">
        <MCPDetailHeader
            v-if="mcp.id"
            :title="mcp.title"
            :name="mcp.name"
            :creator="mcp.creator"
            :icon-src="mcp.iconSrc || MCPIcon"
            :scene="mcp.scene"
            :transport-type="mcp.transportType"
        />

        <a-tabs v-model:activeKey="activeKey">
            <a-tab-pane key="overview">
                <template #tab>
                    <InfoCircleOutlined />
                    概览
                </template>

                <div class="flex">
                    <div class="flex-1">
                        <div class="mt-[6px]" style="color: var(--color-symbol-bold)">
                            {{ mcp.description }}
                        </div>

                        <div class="tools mt-[20px] flex items-center text-[20px]">
                            <ToolOutlined class="mr-[10px]" />
                            <span>工具</span>
                        </div>

                        <MCPDetailToolList :tools="tools" @inspector="onInspector" />
                    </div>

                    <MCPDetailInstall
                        :name="mcp.name"
                        :sse-url="sseUrl"
                        :streamable-http-url="streamableHTTPUrl"
                        :mcp-create-type="mcp.createType"
                    />
                </div>
            </a-tab-pane>

            <a-tab-pane key="tools">
                <template #tab>
                    <ToolOutlined />
                    工具（{{ tools.length }}）
                </template>

                <div class="flex">
                    <div class="flex-1">
                        <MCPDetailToolList
                            :show-inspector-btn="false"
                            :show-selection-effect="true"
                            :show-hover-effect="true"
                            :tools="tools"
                            :selected-index="curInspectorToolIdx"
                            @click="({ idx }) => (curInspectorToolIdx = idx)"
                        />
                    </div>

                    <MCPInspector
                        v-if="curInspectorTool"
                        :tool="curInspectorTool"
                        :transport-type="transportType"
                        :url="transportType === 'sse' ? sseUrl : streamableHTTPUrl"
                        :create-type="mcp.createType"
                    />
                </div>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { InfoCircleOutlined, ToolOutlined } from '@ant-design/icons-vue'
import { getMCP } from '@/api/mcp'
import MCPDetailHeader from './components/MCPDetailHeader.vue'
import MCPDetailInstall from './components/MCPDetailInstall.vue'
import MCPDetailToolList from './components/MCPDetailToolList.vue'
import MCPInspector from './components/MCPInspector.vue'
import { mcpSign } from '@/api/mcp'
import { type IMCPInfo } from '@/interface/mcp'
import MCPIcon from '@/assets/images/mcp.png'
import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import { connectMcp } from '@/utils/connectMcp'
import { notification } from 'ant-design-vue'

type PromiseValue<T extends Promise<unknown>> = T extends Promise<infer U> ? U : never
type Tool = PromiseValue<ReturnType<Client['listTools']>>['tools'][number]

const route = useRoute()

const mcp = ref({} as IMCPInfo)

const tools = ref<Tool[]>([])

const mcpSignData = ref({
    sign: '',
    username: '',
    host: ''
})

const sseUrl = computed(() => {
    if (!mcp.value.id) return ''
    if (mcp.value.createType === 'copilot') {
        return `${'http:'}//${mcpSignData.value.host}/api/mcp/sse/${mcp.value.id}?sign=${mcpSignData.value.sign}&username=${
            mcpSignData.value.username
        }`
    }
    if (mcp.value.transportType.includes('SSE')) {
        return `${'http:'}//${mcpSignData.value.host}/api/mcp/external/sse/${mcp.value.id}`
    }
    return ''
})

const streamableHTTPUrl = computed(() => {
    if (!mcp.value.id) return ''
    if (mcp.value.createType === 'copilot') {
        return `${location.protocol}//${mcpSignData.value.host}/api/mcp/${mcp.value.id}?sign=${mcpSignData.value.sign}&username=${mcpSignData.value.username}`
    }
    if (mcp.value.transportType.includes('Streamable HTTP')) {
        return `${location.protocol}//${mcpSignData.value.host}/api/mcp/external/${mcp.value.id}`
    }
    return ''
})

type TabKey = 'overview' | 'tools'
const activeKey = ref<TabKey>('overview')

const curInspectorToolIdx = ref(0)

const curInspectorTool = computed(() => tools.value[curInspectorToolIdx.value])

const transportType = computed(() => {
    const type = mcp.value.transportType.split(',').pop()
    return type === 'SSE' ? 'sse' : 'streamable-http'
})

const getToolList = async () => {
    try {
        const client = await connectMcp(transportType.value === 'sse' ? sseUrl.value : streamableHTTPUrl.value, transportType.value)

        const { tools } = await client.listTools()

        await client.close()

        return tools
    } catch (err: any) {
        notification.error({
            placement: 'topRight',
            message: err.message
        })
    }
}

const getData = async () => {
    const { data: _mcp } = await getMCP(route.params.id as string)
    mcp.value = _mcp

    const { data: _mcpSign } = await mcpSign()
    mcpSignData.value = _mcpSign

    const _tools = await getToolList()
    // @ts-ignore
    tools.value = _tools
}

void getData()

const onInspector = (idx: number) => {
    activeKey.value = 'tools'
    curInspectorToolIdx.value = idx
}
</script>

<style>
.jv-container .jv-code {
    padding: 0 !important;
}

.jv-container .jv-code.open {
    padding-bottom: 0 !important;
}
</style>
