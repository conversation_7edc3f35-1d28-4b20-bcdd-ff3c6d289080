/**
 * 异步检查给定值是否遵循指定的命名规则（此处预期为PascalCase）。
 *
 * @param rule - 用于描述或区分的规则名称，尽管在这个实现中未直接使用，但可以保留以备未来扩展。
 * @param value - 需要检查的字符串值，判断其是否符合PascalCase命名规范。
 *
 * @returns 返回一个Promise，如果`value`遵循PascalCase命名规则则解析为 void，
 *          否则以Error形式拒绝Promise，携带错误信息说明命名不合规。
 *
 */
export const CheckPascalCaseAsync: (rule:string, value:string) => void = (rule, value,) => {
    if(!value){
        return Promise.resolve()
    }
    // 验证规则：大写字母开头，其余单词首字母大写，其余小写，无空格或下划线
    const pattern = /^[A-Z][a-z]*([A-Z][a-z]*)*$/;

    if (pattern.test(value)) {
        return Promise.resolve()
    } else {
        return Promise.reject('名称必须为纯字母，且遵循大写开头的驼峰命名法')
    }
};