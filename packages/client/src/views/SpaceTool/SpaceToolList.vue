<template>
    <div class="flex justify-between items-center mb-[20px]">
        <a-input-search v-model:value="keyword" placeholder="搜索智能体插件" style="width: 250px" @search="onSearch" />
        <a-button type="primary" @click="handleOpen">创建智能体插件</a-button>
    </div>

    <a-table class="copilot-concise-table"
             :dataSource="dataSource"
             :columns="columns"
             :scroll="{ y: tableH, x: 1000 }"
             @change="getPageData"
             :pagination="{current,pageSize:defaultPageSize,total}"
    >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'id'">
                <div class="m-[-16px] p-[16px]" @click.stop>{{ record.id }}</div>
            </template>
          <template v-else-if="column.key === 'isPublic'">
            <span v-if="record.isPublic" class="text-[12px] font-bold text-[#57D622]">已上架 </span>
            <span v-else class="text-[12px] font-bold text-[#f50]">未上架 </span>
          </template>
            <template v-else-if="column.key === 'action'">
                <a-space>
                    <a-button type="link" target="_blank" :href="`/toolMain/${spaceId}/tool/${record.id}`">
                        管理
                    </a-button>
                    <a-button type="link" @click="handleOpen(record)">信息编辑</a-button>
                    <a-button type="link" danger @click="handleDel(record)">删除</a-button>
                </a-space>
            </template>
        </template>
    </a-table>
    <create-tool ref="createRef" :space-id="spaceId" @callback="getData" />
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { computed, onMounted, ref } from 'vue'
import { getMarketplacesTmpl } from '@/api/space'
import { deleteTool, getToolList } from '@/api/tool'
import CreateTool from './components/CreateTool.vue'
import { message, Modal } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'

const { isSelf } = useUserStore()
const createRef = ref<any>(null)

const route = useRoute()
const router = useRouter()

const searchName = ref('')

const dataSource = ref([])

const tmplData = ref<any>({})

const columns = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id'
  },
  {
    title: '名称',
    dataIndex: 'title',
    minWidth: 80
  },
  {
    title: 'name',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '状态',
    dataIndex: 'isPublic',
    key: 'isPublic',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    minWidth: 300
  },
  {
    title: '创建时间',
    dataIndex: 'createdDate',
    key: 'createdDate'
  },
  {
    title: '更新时间',
    dataIndex: 'updatedDate',
    key: 'updatedDate'
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    fixed: 'right'
  }
]
const id: any = route.query.id
const loading = ref(false)
const current = ref(1)
const total = ref(0)
const defaultPageSize = 12
const keyword = ref('')

const spaceId =  route.params.spaceId as string

const tableH = computed(() => {
  return document.body.offsetHeight - 240
})

const getPageData = ({current:page}: { current: string }) => {
  current.value = Number(page);
  getData()
}

const getData = async () => {
  loading.value = true;
  try {
    const res = await getToolList({spaceId, page: current.value, pageSize: defaultPageSize,keyword: keyword.value})
    dataSource.value = res.data.rows
    total.value = Number(res.data.count)
  } finally {
    loading.value = false
  }
}

const getTmpl = async () => {
    const res = (await getMarketplacesTmpl()) as any
    tmplData.value = res.find((item: any) => item.templateName == 'DatasetModel QnA')
}

const onSearch = () => {
    current.value = 1
    getData()
}

const handleOpen = (data: any) => {
    createRef.value?.open(data || {})
}

const handleDel = (data: any) => {
    Modal.confirm({
        title: `警告`,
        content: `确认要删除 "${data.title}" 智能体插件？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await deleteTool(data.id)
            message.success(`${data.title} 删除成功`)
            await getData()
        }
    })
}

onMounted(() => {
    getData()
    getTmpl()
})
</script>
