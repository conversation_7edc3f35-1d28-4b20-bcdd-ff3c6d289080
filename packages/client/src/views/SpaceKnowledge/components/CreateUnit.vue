<template>
    <a-modal
        v-model:open="open"
        :title="step === 1 ? '选择数据源' : '新增单元'"
        :width="930"
        class="min-h-[300px]"
        :destroy-on-close="true"
        :mask-closable="false"
        :after-close="afterClose"
    >
        <a-spin :spinning="loading" tip="努力处理中！！！">
            <section v-if="step === 1">
                <a-row :gutter="[12, 12]" :wrap="true">
                    <a-col :span="8" v-for="item in dataSource" :key="item.key">
                        <a-card
                            class="w-full transition"
                            :class="[
                                item.disabled ? 'cursor-not-allowed' : 'cursor-pointer',
                                curUnit.key === item.key ? 'border-[#1677ff]' : ''
                            ]"
                            @click="handleSelectUnit(item)"
                        >
                            <div class="flex gap-2 flex items-start h-[70px]">
                                <div
                                    class="bg-[#f5faff] w-8 h-8 rounded flex justify-center items-center"
                                    style="border: 0.5px solid #d1e9ff"
                                    :class="[item.disabled ? '!border-[#e0e2e4] bg-[#f1f1f1]' : '']"
                                >
                                    <component
                                        :is="item.icon"
                                        class="h-4 w-4"
                                        :class="[item.disabled ? 'text-[#ccc]' : 'text-[#1677ff]']"
                                    />
                                </div>

                                <div class="flex-1">
                                    <div class="font-medium">{{ item.title }}</div>
                                    <a-typography-text class="text-xs" type="secondary">{{ item.description }}</a-typography-text>
                                </div>
                            </div>
                        </a-card>
                    </a-col>
                </a-row>
            </section>
            <section v-else>
                <unit-upload
                    ref="refUnitUpload"
                    v-if="['localDoc', 'localExcel'].includes(curUnit.key)"
                    :limit="20"
                    :description="curUnit.description"
                    v-model:is-ready="isReady"
                    :accept="curUnit.accept"
                    @callback="(file) => (unitInfo.file = file)"
                />
                <div v-if="curUnit.key == 'localDoc'">
                    <a-form-item class="my-[8px] flex" label="分段设置">
                        <a-select value="0">
                            <a-select-option value="0">自动分段与清洗</a-select-option>
                            <a-select-option value="2" disabled>自定义</a-select-option>
                        </a-select>
                    </a-form-item>
                </div>
                <unit-info ref="refUnitInfo" v-if="curUnit.key === 'infoDoc'" v-model:is-ready="isReady">
                    <a-form-item class="my-[8px] flex" label="更新频率">
                        <a-select v-model:value="autoUpdate" :options="dayOption"> </a-select>
                    </a-form-item>
                </unit-info>
                <unit-url ref="refUnitUrl" v-if="curUnit.key === 'urlDoc'" v-model:is-ready="isReady">
                    <a-form-item class="my-[8px] flex" label="更新频率">
                        <a-select v-model:value="autoUpdate" :options="dayOption"> </a-select>
                    </a-form-item>
                </unit-url>
            </section>
        </a-spin>
        <template #footer>
            <template v-if="step == 1">
                <a-button @click="open = false"> 取消</a-button>
                <a-button type="primary" @click="handleOk"> 下一步</a-button>
            </template>
            <template v-if="step == 2">
                <a-button @click="step = 1"> 上一步</a-button>
                <a-button type="primary" @click="handleOk" :disabled="!isReady" :loading="loading">提交</a-button>
            </template>
        </template>
    </a-modal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import {
    DocumentTextIcon,
    TableCellsIcon,
    GlobeAltIcon,
    BookOpenIcon,
    LinkIcon,
    RocketLaunchIcon,
    BarsArrowUpIcon
} from '@heroicons/vue/24/solid'
export default defineComponent({
    components: {
        DocumentTextIcon,
        TableCellsIcon,
        GlobeAltIcon,
        BookOpenIcon,
        LinkIcon,
        RocketLaunchIcon,
        BarsArrowUpIcon
    }
})
</script>
<script lang="ts" setup>
import { computed, ref, defineProps, defineEmits, defineExpose } from 'vue'
import UnitUpload from '@/components/upload/UnitUpload.vue'
import { batchUpdateKnowledge, updateKnowledge } from '@/api/space'
import UnitInfo from './UnitInfo.vue'
import { message } from 'ant-design-vue'
import UnitUrl from '@/views/SpaceKnowledge/components/UnitUrl.vue'
import { useRouter, useRoute } from 'vue-router'
import { dayOption, dataSource } from '../config'

const props = defineProps(['datasetInfo'])
const emits = defineEmits(['callback'])

const autoUpdate = ref('0')
const router = useRouter()
const route = useRoute()

const open = ref<boolean>(false)

const refUnitInfo = ref<any>(null)
const refUnitUrl = ref<any>(null)
const refUnitUpload = ref<any>(null)

const step = ref(1)

const unitInfo = ref({ file: { fileKey: null, filename: '' } })

const isReady = ref(false)

const loading = ref(false)

const curUnit = ref<any>({ ...dataSource[0] })

const showModal = () => {
    open.value = true
}

const handleOk = () => {
    const { key } = curUnit.value
    if (step.value === 1 && Number(props.datasetInfo.model) === 2) {
        const { spaceId, datasetId } = route.params
        router.push(`/space/${spaceId}/knowledge/${datasetId}/upload?type=${key}`)
        return
    }
    switch (step.value) {
        case 1:
            step.value = 2
            isReady.value = false
            break
        case 2:
            handleSubmit()
            break
    }
}

const handleSubmit = async () => {
    const formData = { datasetId: props.datasetInfo.id }

    loading.value = true
    if (curUnit.value.key == 'infoDoc') {
        const { type, data } = (await refUnitInfo.value?.getFormData()) as { type: number; data: any[] }

        switch (type) {
            case 1:
                {
                    // 使用后续提交
                    Object.assign(formData, data[0].file)

                    // @ts-ignore
                    formData.autoUpdate = autoUpdate.value
                    // @ts-ignore
                    formData.remoteUrl = data[0].url

                    await updateKnowledge(formData)
                }
                break
            case 2:
                await batchUpdateKnowledge({
                    datasetId: props.datasetInfo.id,
                    items: data.map((item) => {
                        item.autoUpdate = autoUpdate.value
                        return item
                    })
                })
                break
        }
    } else if (curUnit.value.key == 'urlDoc') {
        const { urlInfo, textSplit } = (await refUnitUrl.value?.getContent()) as { urlInfo: Record<string, any>; textSplit: string }
        Object.assign(formData, { autoUpdate: autoUpdate.value, ...urlInfo, textSplit })
        await updateKnowledge(formData)
    } else {
        // 文件上传 localExcel localDoc
        const data = refUnitUpload.value?.getFiles()
        await batchUpdateKnowledge({ datasetId: props.datasetInfo.id, items: data })
    }
    loading.value = false
    message.success('新建单元成功！')
    open.value = false

    emits('callback')
}

const afterClose = () => {
    unitInfo.value = { file: { fileKey: null, filename: '' } }
    curUnit.value = { ...dataSource[0] }

    step.value = 1
    isReady.value = false
    loading.value = false
    autoUpdate.value = '0'
}

const handleSelectUnit = (data: any) => {
    if (data.disabled) return
    curUnit.value = { ...data }
}

defineExpose({ open: showModal })
</script>
<style scoped>
/* tile uploaded pictures */
.ant-upload-wrapper :deep(.ant-upload-list) {
    max-height: 300px;
    overflow: auto;
    margin-top: 10px;
}
.ant-upload-wrapper :deep(.ant-upload-list .ant-upload-list-item-container:first-child .ant-upload-list-item) {
    margin-top: 0;
}
</style>
