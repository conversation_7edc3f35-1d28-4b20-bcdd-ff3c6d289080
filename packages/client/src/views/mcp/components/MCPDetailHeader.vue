<template>
    <div class="flex">
        <a-avatar class="mr-[10px]" :size="68" shape="square" :src="props.iconSrc" />
        <div>
            <div class="text-[20px] flex items-center">
                <span class="mr-[10px]">{{ props.title }}</span
                ><a-tag v-for="type in props.transportType.split(',')" :key="type" color="blue">
                    <span class="text-[10px]">{{ type }}</span>
                </a-tag>
            </div>
            <div class="mt-[5px]">
                <a-typography-text type="secondary" style="color: var(--color-symbol-bold)">{{ props.name }}</a-typography-text>
            </div>
            <div>
                <a-typography-text type="secondary" style="color: var(--color-symbol-dc)">@{{ props.creator }}</a-typography-text>
            </div>
        </div>
    </div>

    <div class="mt-[8px]">
        <a-tag v-for="tag in props.scene.split(',')" :key="tag" color="green">{{ tag }}</a-tag>
    </div>
</template>

<script setup lang="ts">
interface IProps {
    title: string
    name: string
    creator: string
    iconSrc: string
    scene: string
    transportType: string
}

const props = defineProps<IProps>()
</script>
