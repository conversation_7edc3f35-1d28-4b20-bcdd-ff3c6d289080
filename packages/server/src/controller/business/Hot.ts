import { Request, Response } from 'express'
import { App } from '../../index'
import { asyncHandler } from '../../middleware/errorResolver'
import { opsLogger } from '../../utils/biliLogger'
import { query, validationResult } from 'express-validator'
import axios from 'axios'
import { Hot } from '../../database/entities/Hot'
import moment from 'moment'

export function hot({ app, AppDataSource }: App) {
    /**
     * 定时获取全平台热搜
     */
    app.get(
        '/api/hot/syncHotspots',
        asyncHandler(async (req: Request, res: Response) => {
            try {
                getAndSaveHotspots()
                return res.json({ code: 0, data: '', message: '获取热搜' })
            } catch (error) {
                opsLogger.error('GetHotspots', { message: `获取热搜失败 ${error}`, error })
                return res.json({ code: 500, data: '', message: '获取热搜失败' })
            }
        })
    )

    app.get(
        '/api/v0/hot/getHotspotsList',
        [query('platform').isString().optional()],
        asyncHandler(async (req: Request, res: Response) => {
            const errors = validationResult(req)
            if (!errors.isEmpty()) {
                return res.status(400).json({ code: 400, message: '参数错误', errors: errors.array() })
            }

            const { platform } = req.query as any
            const hotRepository = AppDataSource.getRepository(Hot)

            const date = await hotRepository.findOne({
                where: { platform },
                order: { log_date: 'DESC', log_hour: 'DESC' },
                select: ['log_date', 'log_hour']
            })

            const queryBuilder = hotRepository
                .createQueryBuilder('hot')
                .select(['hot.platform', 'hot.title', 'hot.url', 'hot.hot', 'hot.rank', 'hot.ctime'])
                .orderBy('hot.rank')

            if (platform) {
                queryBuilder.where('hot.platform = :platform', { platform })
                queryBuilder.andWhere('hot.log_date = :log_date', { log_date: date?.log_date })
                queryBuilder.andWhere('hot.log_hour = :log_hour', { log_hour: date?.log_hour })
            }

            const [data] = await queryBuilder.getManyAndCount()

            return res.json({
                code: 0,
                data,
                message: '获取热搜列表成功'
            })
        })
    )

    async function getAndSaveHotspots() {
        const date = moment().format('YYYYMMDD')
        const hour = moment().format('HH')
        const platforms = [
            'bilibili',
            'bilibili-bi',
            'rednote',
            'weibo',
            'douyin',
            'netease-news',
            'tencent-news',
            'toutiao',
            'zhihu',
            'douban'
        ]

        // b站内网数据
        const biliIntranet = await axios.post(
            'http://oneservice.bilibili.co/oneservice/dispatch/api/query/v3',
            {
                osHeader: {
                    apiId: 'api_4189',
                    appKey: '9b2e66cc1dbbc4ebbd229fcbdfb16432',
                    secret: 'z0mAyZGxLRvz/t87OifaTWWSRAHUPI/n4E8bnpH8RdQ='
                },
                reqs: [
                    {
                        field: 'log_date',
                        operator: '=',
                        values: [date]
                    }
                ]
            },
            {
                headers: {
                    'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
                    'Content-Type': 'application/json'
                }
            }
        )
        const rowList = biliIntranet.data.data.rowList
        const maxLogHour = rowList.reduce((max: string, item: any) => (Number(item.log_hour) > Number(max) ? item.log_hour : max), '0')
        const latestRows = rowList.filter((item: any) => item.log_hour === maxLogHour)
        const biliIntranetRank = latestRows.map((item: any) => ({
            platform: 'bilibili-bi',
            title: item.hotword_content,
            url: `https://search.bilibili.com/all?vt=26772785&keyword=${item.query}&from_source=webtop_search&spm_id_from=333.1007&search_source=4`,
            hot: 0,
            rank: item.rank,
            log_date: date,
            log_hour: hour
        }))
        // ai-copilot 热搜
        const result = await axios.post(
            'https://copilot.bilibili.co/api/v1/prediction/84474214-403a-4729-aaef-048f9e476fa6',
            { question: '1' },
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        )
        const hotData = JSON.parse(result.data.text)
        const hotDataRank = hotData.map((item: any) => ({
            platform: item.platform,
            title: item.title,
            url: item.url,
            hot: item.hot,
            rank: item.rank,
            log_date: date,
            log_hour: hour
        }))

        // 小红书热搜数据
        const red = await axios.get(
            'https://trends.zhiweidata.com/hotSearchTrend/search/longTimeInListSearch?type=little-red-book&sortType=realTime'
        )
        const redRank = red.data.data.map((item: any) => ({
            platform: 'rednote',
            title: item.name,
            url: item.url,
            hot: item.lastCount,
            rank: item.rank,
            log_date: date,
            log_hour: hour
        }))

        // b站数据
        const bili = await axios.get(
            'https://trends.zhiweidata.com/hotSearchTrend/search/longTimeInListSearch?type=bilibili&sortType=realTime'
        )
        const biliRank = bili.data.data.map((item: any) => ({
            platform: 'bilibili',
            title: item.name,
            url: item.url,
            hot: item.lastCount,
            rank: item.rank,
            log_date: date,
            log_hour: hour
        }))

        const hotspotData = [...hotDataRank, ...redRank, ...biliRank, ...biliIntranetRank]
        // upsert到数据库中
        const hotRepository = AppDataSource.getRepository(Hot)
        // const query = hotRepository.createQueryBuilder()
        // query.where()
        for (const platform of platforms) {
            const hasNewData = hotspotData.some((item) => item.platform === platform)
            if (hasNewData) {
                await hotRepository.delete({
                    platform,
                    log_date: date,
                    log_hour: hour
                })
            }
        }

        await AppDataSource.getRepository(Hot).save(hotspotData)
    }

    app.post(
        '/api/hot/getHotspots',
        asyncHandler(async (req: Request, res: Response) => {
            const { platforms = [], topn = 10, field = [], log_date, log_hour } = req.body as any
            const selectFields = field.length > 0 ? field.map((f: string) => `hot.${f}`) : undefined
            const hotRepository = AppDataSource.getRepository(Hot)

            let allResults: Hot[] = []

            if (platforms.length > 0) {
                for (const pf of platforms) {
                    let query = hotRepository
                        .createQueryBuilder('hot')
                        .orderBy('hot.rank')
                        .where('hot.platform = :platform', { platform: pf })
                        .andWhere('hot.log_date = :log_date', { log_date })
                        .andWhere('hot.log_hour = :log_hour', { log_hour })
                        .limit(topn)
                    if (selectFields) {
                        query = query.select(selectFields)
                    }
                    const result = await query.getMany()
                    allResults = allResults.concat(result)
                }
            } else {
                // 如果没有指定平台，则查全部
                let query = hotRepository
                    .createQueryBuilder('hot')
                    .orderBy('hot.rank')
                    .where('hot.log_date = :log_date', { log_date })
                    .andWhere('hot.log_hour = :log_hour', { log_hour })
                    .limit(topn)
                if (selectFields) {
                    query = query.select(selectFields)
                }
                allResults = await query.getMany()
            }

            return res.json({
                code: 0,
                data: allResults,
                message: '获取热搜数据成功'
            })
        })
    )
}
