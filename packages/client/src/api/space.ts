import { apiV1, apiV0, type ResponseData } from './client'

export interface IChatflows {
    analytic: string
    apiConfig: string
    apikeyid: string
    avatar: string
    category: string
    chatbotConfig: string
    createdDate: string
    creator: string
    deployed: boolean
    description: string
    flowData: string
    followUpPrompts: unknown
    id: string
    isPublic: boolean
    name: string
    spaceId: string
    speechToText: string
    type: 'CHATFLOW' | 'MULTIAGENT'
    updatedDate: string
}

export const getAllSpaces = () => apiV0.get('/v0/spaces')
export const getSpaceDetail = (spaceId: string) => apiV0.get('/v0/space', { params: { id: spaceId } })
export const updateSpace = (data: { id: string; description: string }) => apiV0.post('/v0/space', data)

export const getDatasets = (id: string) => apiV0.get(`/v0/datasets?spaceId=${id}`)

export const updateDataset = (data: any) => apiV0.post('/v0/dataset', data)
export const getDataset = (data: { spaceId: string; id: string }) => apiV0.get(`/v0/dataset?spaceId=${data.spaceId}&id=${data.id}`)

export const getKnowledge = (datasetId: string, page: number, pageSize: number, keyword: string) =>
    apiV0.get(`/v0/knowledge/list?datasetId=${datasetId}&page=${page}&pageSize=${pageSize}&keyword=${keyword}`)

export const updateKnowledge = (data: any) => apiV0.post('/v0/knowledge', data)
export const delKnowledge = (knowledgeId: string) => apiV0.post('/v0/knowledge/delete', { knowledgeId })

export const updateInfo = (infoPath: string) => apiV0.post('/v0/knowledge/infoUpload', { infoPath })

export const getSpaceMembers = (spaceId: string) => apiV0.get('/v0/space/members', { params: { spaceId: spaceId } })
export const updateMembers = (spaceId: string, members: string) => apiV0.post('/v0/space/updateMembers', { spaceId, members })

export const uploadFile = (files: any, onUploadProgress: any) =>
    apiV0.post(
        '/v0/common/upload',
        { files },
        {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress
        }
    )

export const updateChatFlowInfo = (data: any) => apiV0.post('/v0/chatflow/infoUpdate', data)

export const getChatFlow = (id: string) => apiV0.get(`/v0/chatflow?id=${id}`)
export const getChatFlowList = (id: string) => apiV0.get<ResponseData<IChatflows[]>>(`/v0/chatflows?spaceId=${id}`)

export const updateChatFlowV1 = (id: string, data: any) => apiV1.put(`/v1/chatflows/${id}`, data).then((res) => res.data)
export const delChatFlowV1 = (id: string) => apiV1.delete(`/v1/chatflows/${id}`).then((res) => res.data)
export const getMarketplacesTmpl = () => apiV1.get('/v1/marketplaces/templates')

export const getPublicList = ({ page, pageSize = 24 }: { page: number; pageSize?: number }) =>
    apiV0.get(`/v0/chatflow/publicList?page=${page}&pageSize=${pageSize}`)

export const getInfoChild = (infoPath: string) => apiV0.get(`/v0/knowledge/infoChild?infoPath=${infoPath}`)

export const batchUpdateKnowledge = (data: { datasetId: string; items: any[] }) => apiV0.post('/v0/knowledge/batch', data)
export const getWebInfo = (url: string, rootEle: string) =>
    apiV0.get(`/v0/knowledge/webInfo?url=${encodeURIComponent(url)}&rootEle=${encodeURIComponent(rootEle)}`)
export const webUpload = (data: { title: string; content: string }) => apiV0.post('/v0/knowledge/webUpload', data)
