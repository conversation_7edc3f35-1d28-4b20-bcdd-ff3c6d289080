export function copy2Clipboard(text: string, cb: (successful: boolean) => void) {
    // 检查浏览器是否支持 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        // 使用 navigator.clipboard.writeText 将文本复制到剪贴板
        navigator.clipboard.writeText(text).then(
            function () {
                cb(true)
            },
            function () {
                cb(false)
            }
        )
    } else {
        // 如果 Clipboard API 不可用，则使用回退方法（例如，创建一个临时的文本区域元素）
        const textArea = document.createElement('textarea')
        textArea.value = text
        // 使文本区域不可见
        textArea.style.position = 'fixed' // 防止滚动
        textArea.style.top = '0'
        textArea.style.left = '0'
        textArea.style.width = '2em'
        textArea.style.height = '2em'
        textArea.style.padding = '0'
        textArea.style.border = 'none'
        textArea.style.outline = 'none'
        textArea.style.boxShadow = 'none'
        textArea.style.background = 'transparent'

        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        try {
            const successful = document.execCommand('copy')
            cb(successful)
        } catch {
            cb(false)
        }

        document.body.removeChild(textArea)
    }
}
