import { Conversation<PERSON>hain } from 'langchain/chains'
import { ChatPromptTemplate, HumanMessagePromptTemplate, MessagesPlaceholder, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { RunnableSequence } from '@langchain/core/runnables'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { BaseMessage, HumanMessage } from '@langchain/core/messages'
import { ConsoleCallbackHandler as LCConsoleCallbackHandler } from '@langchain/core/tracers/console'
import { checkInputs, Moderation, streamResponse } from '../../moderation/Moderation'
import { formatResponse } from '../../outputparsers/OutputParserHelpers'
import { addImagesToMessages } from '../../../src/multiModalUtils'
import { ChatOpenAI } from '../../chatmodels/ChatOpenAI/FlowiseChatOpenAI'
import {
    FlowiseMemory,
    FlowiseWindowMemory,
    ICommonObject,
    IMessage,
    INode,
    INodeData,
    INodeParams,
    MemoryMethods,
    IServerSideEventStreamer
} from '../../../src/Interface'
import { ConsoleCallbackHandler, CustomChainHandler, additionalCallbacks } from '../../../src/handler'
import { convertBaseMessagetoIMessage, getBaseClasses, handleEscapeCharacters } from '../../../src/utils'
import { BufferWindowMemoryInput } from 'langchain/memory'

let systemMessage = `你是全球顶尖的QA问答系统，从上下文中找出最匹配信息`
const inputKey = 'input'

/**
 * 拷贝自LLMChain的prompt硬编码版本
 */
class FastChain_Chains implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    baseClasses: string[]
    description: string
    inputs: INodeParams[]
    tags: string[]
    sessionId?: string

    constructor(fields?: { sessionId?: string }) {
        this.label = 'Fast Chain'
        this.name = 'fastChain'
        this.version = 2.0
        this.type = 'FastChain'
        this.icon = 'chain.svg'
        this.category = 'Chains'
        this.description = '无需自定义prompt,会使用语言模型默认的prompt'
        this.tags = ['bilibili']
        this.baseClasses = [this.type, ...getBaseClasses(ConversationChain)]
        this.inputs = [
            {
                label: 'Language Model',
                name: 'model',
                type: 'BaseLanguageModel'
            },
            {
                label: 'Output Parser',
                name: 'outputParser',
                type: 'BaseLLMOutputParser',
                optional: true
            }
        ]
        this.sessionId = fields?.sessionId
    }

    async init(nodeData: INodeData, input: string, options: ICommonObject): Promise<any> {
        const chain = prepareChain(nodeData, options, this.sessionId)
        return chain
    }

    async run(nodeData: INodeData, input: string, options: ICommonObject): Promise<string | object> {
        const metadata = {
            chatflowid: options.chatflowid,
            chatId: options.chatId
        }

        const chain = await prepareChain(nodeData, options, this.sessionId)

        const loggerHandler = new ConsoleCallbackHandler(options.logger)
        const additionalCallback = await additionalCallbacks(nodeData, options)

        let res = ''
        let callbacks = [loggerHandler, ...additionalCallback]

        if (process.env.DEBUG === 'true') {
            callbacks.push(new LCConsoleCallbackHandler())
        }

        if (options.socketIO && options.socketIOClientId) {
            const handler = new CustomChainHandler(options.socketIO, options.socketIOClientId)
            callbacks.push(handler)
            res = await chain.invoke({ input }, { callbacks, metadata })
        } else {
            res = await chain.invoke({ input }, { callbacks, metadata })
        }

        await memory.addChatMessages(
            [
                {
                    text: input,
                    type: 'userMessage'
                },
                {
                    text: res,
                    type: 'apiMessage'
                }
            ],
            this.sessionId
        )

        return res
    }
}

const prepareChatPrompt = (nodeData: INodeData, humanImageMessages: HumanMessage[]) => {
    // const prompt = nodeData.inputs?.systemMessagePrompt as string

    const messages = [
        // 不使用systemPrompt
        // SystemMessagePromptTemplate.fromTemplate(prompt ? prompt : systemMessage),
        new MessagesPlaceholder(memory.memoryKey ?? 'chat_history'),
        HumanMessagePromptTemplate.fromTemplate(`{${inputKey}}`)
    ]
    if (humanImageMessages.length) messages.push(...(humanImageMessages as any[]))

    const chatPrompt = ChatPromptTemplate.fromMessages(messages)

    return chatPrompt
}

const prepareChain = async (nodeData: INodeData, options: ICommonObject, sessionId?: string) => {
    const chatHistory = options.chatHistory
    let model = nodeData.inputs?.model as ChatOpenAI
    const memoryKey = memory.memoryKey

    let humanImageMessages: HumanMessage[] = []
    if (model instanceof ChatOpenAI) {
        const messageContent = await addImagesToMessages(nodeData, options, model.multiModalOption)

        if (messageContent?.length) {
            // Change model to gpt-4-vision
            model.modelName = 'gpt-4-vision-preview'

            // Change default max token to higher when using gpt-4-vision
            model.maxTokens = 1024

            for (const msg of messageContent) {
                humanImageMessages.push(new HumanMessage({ content: [msg] }))
            }
        } else {
            // revert to previous values if image upload is empty
            model.modelName = model.configuredModel
            model.maxTokens = model.configuredMaxToken
        }
    }

    const chatPrompt = prepareChatPrompt(nodeData, humanImageMessages)
    let promptVariables = {}
    const promptValuesRaw = (chatPrompt as any).promptValues
    if (promptValuesRaw) {
        const promptValues = handleEscapeCharacters(promptValuesRaw, true)
        for (const val in promptValues) {
            promptVariables = {
                ...promptVariables,
                [val]: () => {
                    return promptValues[val]
                }
            }
        }
    }

    const conversationChain = RunnableSequence.from([
        {
            [inputKey]: (input: { input: string }) => input.input,
            [memoryKey]: async () => {
                const history = await memory.getChatMessages(sessionId, true, chatHistory)
                return history
            },
            ...promptVariables
        },
        prepareChatPrompt(nodeData, humanImageMessages),
        model,
        new StringOutputParser()
    ])

    return conversationChain
}

/**
 * 取自 nodes/memory/BufferWindowMemory.ts
 */
class BufferWindowMemoryExtended extends FlowiseWindowMemory implements MemoryMethods {
    constructor(fields: BufferWindowMemoryInput) {
        super(fields)
    }

    async getChatMessages(_?: string, returnBaseMessages = false, prevHistory: IMessage[] = []): Promise<IMessage[] | BaseMessage[]> {
        await this.chatHistory.clear()

        // Insert into chatHistory
        for (const msg of prevHistory) {
            if (msg.type === 'userMessage') await this.chatHistory.addUserMessage(msg.message)
            else if (msg.type === 'apiMessage') await this.chatHistory.addAIChatMessage(msg.message)
        }

        const memoryResult = await this.loadMemoryVariables({})
        const baseMessages = memoryResult[this.memoryKey ?? 'chat_history']
        return returnBaseMessages ? baseMessages : convertBaseMessagetoIMessage(baseMessages)
    }

    async addChatMessages(): Promise<void> {
        // adding chat messages will be done on the fly in getChatMessages()
        return
    }

    async clearChatMessages(): Promise<void> {
        await this.clear()
    }
}

const memory = new BufferWindowMemoryExtended({
    returnMessages: true,
    memoryKey: 'aidata_chat_history',
    inputKey: 'aidata_input',
    k: 4
}) as FlowiseWindowMemory

module.exports = { nodeClass: FastChain_Chains }
