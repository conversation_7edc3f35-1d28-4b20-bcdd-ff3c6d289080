<template>
    <div class="data-form">
        <a-form :layout="'vertical'">
            <div class="form-header">
                <a-form-item label="数据表">
                    <a-select v-model:value="formData.sheetId" placeholder="选择表" @change="changeSheet()">
                        <a-select-option v-for="item in sheetList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="表头">
                    <a-select v-model:value="formData.headerLineIdx" placeholder="选择行" @change="changeSheet()">
                        <a-select-option v-for="num in headerTotal" :key="num" :value="num">第{{ num }}行</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="数据起始行">
                    <a-select v-model:value="formData.startLineIdx" placeholder="选择行">
                        <a-select-option v-for="num in dataTotal" :key="num" :value="num">第{{ num }}行</a-select-option>
                    </a-select>
                </a-form-item>
            </div>

            <a-form-item label="表结构">
                <a-table
                    :dataSource="dataSource"
                    :columns="tableColumns"
                    class="copilot-knowledge-table"
                    rowKey="key"
                    :pagination="false"
                    :scroll="{
                        y: 'calc(100vh - 470px)'
                    }"
                >
                    <template #headerCell="{ column }">
                        <template v-if="column.dataIndex === 'index'">
                            <span class="flex items-center">
                                {{ column.title }}
                                <a-tooltip>
                                    <template #title>AI查询时的索引列</template>
                                    <QuestionMarkCircleIcon class="h-4 w-4" />
                                </a-tooltip>
                            </span>
                        </template>
                    </template>
                    <template #bodyCell="{ column, record }">
                        <div v-if="column.key === 'index'">
                            <a-checkbox
                                v-model:checked="record.isIndex"
                                :disabled="checkedDisabled"
                                @change="handleIndexChange()"
                            ></a-checkbox>
                        </div>
                    </template>
                </a-table>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import type { IUploadFile, TableMeta } from '@/api/knowledge'
import { getKnowledgeTable } from '@/api/knowledge'
import { QuestionMarkCircleIcon } from '@heroicons/vue/24/outline'
import { notification } from 'ant-design-vue'

const props = defineProps<{ files: IUploadFile[] }>()
const emits = defineEmits(['change'])

const formData = reactive({
    sheetId: 0,
    headerLineIdx: 1,
    startLineIdx: 2
})
const sheetList = ref<{ id: number; name: string }[]>([])
const tableMeta = ref<TableMeta[]>([])

const dataSource = ref<TableMeta[]>([])

const checkedDisabled = ref(false)

const headerTotal = ref(0)
const dataTotal = computed(() => {
    const list = []
    for (let index = formData.headerLineIdx + 1; index <= headerTotal.value + 1; index++) {
        list.push(index)
    }
    return list
})

const tableColumns = [
    { title: '向量存储', key: 'index', dataIndex: 'index', width: 160 },
    { title: '列名', key: 'columnName', dataIndex: 'columnName' }
]

watch(
    () => props.files,
    () => {
        formData.sheetId = 0
        formData.headerLineIdx = 1
        formData.startLineIdx = 2
    }
)

const getTableUpload = async () => {
    const tableFile = props.files[0]
    const { data } = await getKnowledgeTable({
        document: tableFile,
        tableSheet: {
            sheetId: formData.sheetId || 0,
            headerLineIdx: formData.headerLineIdx || 1,
            startLineIdx: formData.startLineIdx || 2
        }
    })
    if (data.tableMeta.length <= 1) {
        checkedDisabled.value = true
        notification.error({
            message: '错误',
            description: '表格文件不得低于两列，请重新选择！'
        })
    } else {
        checkedDisabled.value = false
    }
    dataSource.value = data.tableMeta.map((item: any) => {
        return {
            id: item.id,
            columnName: item.columnName,
            isIndex: false
        }
    })
    sheetList.value = data.sheetList
    formData.sheetId = formData.sheetId || data.sheetList[0].id
    const totalRow = data.sheetList.find((item: any) => item.id === formData.sheetId)?.totalRow
    headerTotal.value = totalRow ? totalRow - 1 : 1
}
const changeSheet = () => {
    if (formData.headerLineIdx) {
        formData.startLineIdx = formData.headerLineIdx + 1
    }
    emits('change', {
        tableSheet: formData,
        tableMeta: tableMeta.value
    })
    getTableUpload()
}

const getTablePreview = async () => {
    const tableFile = props.files[0]
    const { data } = await getKnowledgeTable({
        document: tableFile,
        tableSheet: {
            sheetId: formData.sheetId,
            headerLineIdx: formData.headerLineIdx,
            startLineIdx: formData.startLineIdx
        },
        tableMeta: tableMeta.value
    })
    const total = data.sheetList.find((item: any) => item.id === formData.sheetId)?.totalRow || 0
    return { previewData: data.previewData, totalRow: total + 1 - formData.startLineIdx }
}

const handleIndexChange = () => {
    if (dataSource.value.every((item) => item.isIndex)) {
        notification.warning({
            message: '警告',
            description: '向量存储请勿全选'
        })
    }
    tableMeta.value = dataSource.value.slice()
    emits('change', {
        tableSheet: formData,
        tableMeta: tableMeta.value
    })
}

defineExpose({ getTableUpload, getTablePreview })
</script>

<style scoped lang="less">
.data-form {
    :deep(.ant-form-item-label) {
        font-weight: 600;
        margin-bottom: 20px;
    }
}
.form-header {
    display: flex;
    justify-content: space-between;
}
.form-header .ant-form-item {
    flex: 1;
    margin-right: 20px;
}
.form-header .ant-form-item:last-child {
    margin-right: 0;
}
</style>
