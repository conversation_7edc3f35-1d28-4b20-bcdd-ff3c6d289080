<template>
    <div class="pl-[20px] w-[500px]">
        <context-holder />
        <div class="text-[20px]">
            <CloudDownloadOutlined />
            <span class="ml-[8px]">安装</span>
        </div>
        <div class="mt-[5px]">
            <a-tabs v-model:activeKey="activeKey">
                <a-tab-pane key="1">
                    <template #tab>
                        <span>
                            <GlobalOutlined />
                            URL
                        </span>
                    </template>
                    <a-card size="small" class="mb-[10px]" v-if="sseUrl">
                        <template #title>
                            <div class="flex justify-between items-center">
                                <div class="text-[13px]">SSE</div>
                                <a-button size="small" type="link" :icon="h(CopyOutlined)" @click="copyUrl('sse')">复制</a-button>
                            </div>
                        </template>
                        <a :href="props.sseUrl" target="_blank">{{ props.sseUrl }}</a>
                    </a-card>
                    <a-card size="small" v-if="streamableHttpUrl">
                        <template #title>
                            <div class="flex justify-between items-center">
                                <div class="text-[13px]">Streamable HTTP</div>
                                <a-button size="small" type="link" :icon="h(CopyOutlined)" @click="copyUrl('streamable-http')"
                                    >复制</a-button
                                >
                            </div>
                        </template>
                        <a :href="props.streamableHttpUrl" target="_blank">{{ props.streamableHttpUrl }}</a>
                    </a-card>
                </a-tab-pane>

                <a-tab-pane key="2">
                    <template #tab>
                        <span>
                            <CodeOutlined />
                            JSON
                        </span>
                    </template>
                    <a-card size="small" class="mb-[10px]" v-if="sseUrl">
                        <template #title>
                            <div class="flex justify-between items-center">
                                <div class="text-[13px]">SSE</div>
                                <a-button size="small" type="link" :icon="h(CopyOutlined)" @click="copyJson('sse')">复制</a-button>
                            </div>
                        </template>
                        <JsonViewer :value="sseJson" />
                    </a-card>
                    <a-card size="small" v-if="streamableHttpUrl">
                        <template #title>
                            <div class="flex justify-between items-center">
                                <div class="text-[13px]">Streamable HTTP</div>
                                <a-button size="small" type="link" :icon="h(CopyOutlined)" @click="copyJson('streamable-http')"
                                    >复制</a-button
                                >
                            </div>
                        </template>
                        <JsonViewer :value="streamableJson" />
                    </a-card>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { GlobalOutlined, CodeOutlined, CopyOutlined, CloudDownloadOutlined } from '@ant-design/icons-vue'
import JsonViewer from 'vue-json-viewer'
import { copy2Clipboard } from '@/utils/copy2Clipboard'
import { message } from 'ant-design-vue'

interface IProps {
    name: string
    sseUrl: string
    streamableHttpUrl: string
    mcpCreateType: 'copilot' | 'external'
}

const props = defineProps<IProps>()

const [messageApi, contextHolder] = message.useMessage()

const activeKey = ref('1')

const sseJson = computed(() => ({
    mcpServers: {
        [props.name]: {
            url: props.sseUrl
        }
    }
}))

const streamableJson = computed(() => ({
    mcpServers: {
        [props.name]: {
            url: props.streamableHttpUrl
        }
    }
}))

function copy(context: string) {
    copy2Clipboard(context, (successful) => {
        if (successful) {
            messageApi.success(
                props.mcpCreateType === 'copilot'
                    ? '已复制到粘贴板，sign具有个人身份权限，请不要随意分享给他人，本次sign在dashboard重登后失效'
                    : '已复制到粘贴板'
            )
        } else {
            messageApi.error('复制失败，请重试')
        }
    })
}

function copyUrl(type: 'sse' | 'streamable-http') {
    copy(type === 'sse' ? props.sseUrl : props.streamableHttpUrl)
}

function copyJson(type: 'sse' | 'streamable-http') {
    copy(JSON.stringify(type === 'sse' ? sseJson.value : streamableJson.value))
}
</script>
