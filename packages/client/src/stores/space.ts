import { ref, computed, onMounted, watch } from 'vue'
import { defineStore } from 'pinia'
import { getAllSpaces, updateSpace } from '@/api/space'
import { useRoute, useRouter } from 'vue-router'

export const useSpaceStore = defineStore('space', () => {
    const route = useRoute()
    const router = useRouter()
    const allSpaces = ref<any[]>([])

    const personalSpace = computed(() => {
        return allSpaces.value.find((item) => item.spaceType == 1) || {}
    })
    const teamSpace = computed(() => {
        return allSpaces.value.filter((item) => item.spaceType !== 1)
    })

    onMounted(async () => {
        await getData()
        redirectWorkPath()
    })

    const getData = async () => {
        const data = await getAllSpaces()
        allSpaces.value = data.data
    }

    watch(route, () => {
        redirectWorkPath()
    })

    const redirectWorkPath = () => {
        if (route.path === '/') {
            router.replace(`/space/${personalSpace.value.id}`).then()
        }
    }

    const addSpace = async (data: any) => {
        return await updateSpace(data).then(async (data) => {
            await getData()
            return data
        })
    }

    const getSpaceById = (id: string) => {
        return allSpaces.value.find((item) => item.id == id)
    }

    return { allSpaces, personalSpace, teamSpace, getSpaceById, addSpace }
})
