<template>
    <a-layout class="h-full">
        <a-row class="border-b border-[#1d1c2314] mb-[12px] flex-0">
            <a-col :span="6">
                <a-flex align="center" class="h-[44px]" gap="10">
                    <a-avatar :size="32" :style="{ background: curSpace?.spaceType == 2 ? '#2AC864' : '#00AEEC' }">
                        <TeamOutlined v-if="curSpace?.spaceType == 2" />
                        <UserOutlined v-else />
                    </a-avatar>
                    <a-flex vertical justify="center">
                        <a-typography-text strong>{{ curSpace.title }}</a-typography-text>
                        <a-typography-text
                            class="w-[200px]"
                            type="secondary"
                            :ellipsis="true"
                            v-if="curSpace?.spaceType == 2 && curSpace.description"
                            :content="curSpace.description"
                        >
                        </a-typography-text>
                    </a-flex>
                </a-flex>
            </a-col>
            <a-col :span="12" class="flex">
                <a-menu
                    class="bg-inherit !border-0 m-auto"
                    disabled-overflow
                    v-model:selectedKeys="current"
                    mode="horizontal"
                    :items="items"
                    @click="handleClick"
                />
            </a-col>
        </a-row>
        <a-layout-content class="h-full">
            <router-view />
        </a-layout-content>
    </a-layout>
</template>
<script lang="ts" setup>
import { computed, ref, watch, onMounted, type Ref } from 'vue'
import type { MenuProps } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { useSpaceStore } from '@/stores/space'
import { SPACE_TYPE } from '@/constant/space'
import { TeamOutlined, UserOutlined } from '@ant-design/icons-vue'

const privateItems = [
    {
        key: 'workflow',
        label: '工作流'
    },
    {
        key: 'knowledge',
        label: '知识库'
    },
    {
        key: 'tool',
        label: '智能体插件'
    },
    {
        key: 'mcp',
        label: 'MCP服务'
    }
]
const teamItems = privateItems.concat({
    key: 'space-mng',
    label: '空间管理'
})

const router = useRouter()
const route = useRoute()
const current = ref<string[]>([route.name as string])
const spaceStore = useSpaceStore()
const items = ref<MenuProps['items']>(privateItems)

const curSpace = computed(() => {
    return route.params?.spaceId ? spaceStore.getSpaceById(route.params.spaceId as string) || {} : {}
})

watch(route, () => {
    if (route.name) {
        current.value = [route.name as string]
    }
})
watch(curSpace, (newValue) => {
    checkMenu(newValue)
})

onMounted(() => {
    if (curSpace.value) {
        checkMenu(curSpace.value)
    }
})

const handleClick: MenuProps['onClick'] = (e) => {
    router.push(e.key as string)
}

const checkMenu = (space: Record<string, any>) => {
    if (!space || !space.spaceType) {
        return
    }
    if (space.spaceType === SPACE_TYPE.team) {
        items.value = teamItems
    } else {
        items.value = privateItems
    }
}
</script>
