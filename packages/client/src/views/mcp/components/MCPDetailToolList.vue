<template>
    <div>
        <a-card
            class="mt-[10px] cursor-pointer"
            :class="{
                'tool-card': props.showHoverEffect,
                'tool-card-selected': props.showSelectionEffect && internalSelectedIndex === idx
            }"
            v-for="(tool, idx) in props.tools"
            :key="tool.id"
            size="small"
            @click="handleClick(idx, tool)"
        >
            <div>
                <div class="flex justify-between">
                    <div class="text-[16px]">{{ tool.name }}</div>
                    <a-button v-if="props.showInspectorBtn" type="text" :icon="h(BugOutlined)" @click.stop="emit('inspector', idx)"
                        >调试</a-button
                    >
                </div>
                <div class="mt-[10px]" style="color: var(--color-symbol-bold)">{{ tool.description }}</div>
            </div>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { h, ref, watch } from 'vue'
import { BugOutlined } from '@ant-design/icons-vue'

interface IProps {
    tools: Record<string, any>[]
    showInspectorBtn: boolean
    showSelectionEffect: boolean
    showHoverEffect: boolean
    selectedIndex?: number | null
}

const emit = defineEmits(['click', 'inspector'])

const props = withDefaults(defineProps<IProps>(), {
    showInspectorBtn: true,
    showSelectionEffect: false,
    showHoverEffect: false,
    selectedIndex: null
})

// 内部选中索引，可以由外部控制或内部更新
const internalSelectedIndex = ref<number | null>(props.selectedIndex)

// 监听外部 selectedIndex 变化
watch(
    () => props.selectedIndex,
    (newVal) => {
        internalSelectedIndex.value = newVal
    }
)

const handleClick = (idx: number, tool: Record<string, any>) => {
    internalSelectedIndex.value = idx
    emit('click', { idx, tool })
}
</script>

<style scoped>
.tool-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.tool-card:hover {
    background-color: var(--color-hover-bg);
    border-color: #1890ff; /* Ant Design 默认主题蓝色 */
}

.tool-card-selected {
    background-color: rgba(24, 144, 255, 0.1); /* Ant Design 蓝色的透明版本 */
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}
</style>
