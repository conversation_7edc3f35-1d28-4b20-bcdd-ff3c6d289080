<template>
    <div>
        <div :id="id || 'markdown-container'"></div>
        <a-modal v-model:open="modalState.visible" :closable="modalState.error" :footer="null" title="图片上传中" @cancel="onCancel">
            <div class="my-1">{{ modalState.current }} / {{ modalState.total }}</div>
            <a-progress
                :stroke-color="{
                    from: '#108ee9',
                    to: '#87d068'
                }"
                :percent="modalState.percent"
            />
            <div v-if="modalState.error" class="text-[#ff4d4f]">图片（{{ modalState.current }}）上传失败</div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import Cherry from 'cherry-markdown'
import 'cherry-markdown/dist/cherry-markdown.min.css'
import { onMounted, reactive, toRefs, watch } from 'vue'
import { uploadFile } from '@/api/space'

import { notification } from 'ant-design-vue'

const props = defineProps({
    isReady: Boolean,
    cherryConfig: Object,
    id: String,
    content: String
})

const { content } = toRefs(props)

const emits = defineEmits(['update:isReady'])

const modalState = reactive({
    visible: false,
    percent: 0,
    current: 0,
    total: 0,
    error: false
})

let cherry: Cherry

watch(
    () => content?.value,
    (val: any) => {
        cherry.setMarkdown(val)
    }
)

const onCancel = () => {
    Object.assign(modalState, {
        visible: false,
        percent: 0,
        current: 0,
        total: 0,
        error: false
    })
}
const base64ToFile = (base64Data: string) => {
    // 提取 Base64 数据部分
    const [base64Header, base64Content] = base64Data.split(',')
    // 解码 Base64
    const byteString = atob(base64Content)
    // 创建一个包含二进制数据的数组
    const byteArray = new Uint8Array(byteString.length)
    for (let i = 0; i < byteString.length; i++) {
        byteArray[i] = byteString.charCodeAt(i)
    }
    // 提取 MIME 类型
    const header = base64Header.match(/data:([^;]+);/)
    const mimeType = header ? header[0] : ''
    const fileExtension = mimeType.split('/')[1]

    const fileName = `image_${Date.now()}.${fileExtension.replace(';', '')}`

    // 创建并返回一个 File 对象
    return new File([byteArray], fileName, { type: mimeType })
}

const uploadFileFn = async (file: File) => {
    try {
        const { data } = await uploadFile(file, () => {})
        return data[0].location
    } catch {
        // 失败一次后可重试一次
        try {
            const { data } = await uploadFile(file, () => {})
            return data[0].location
        } catch (e) {
            console.error(e)
            modalState.error = true
            return Promise.reject()
        }
    }
}
const fetchSequentially = async (base64List: string[]) => {
    const uploadResults = []
    for (const base64 of base64List) {
        let file = base64ToFile(base64)

        try {
            const location = await uploadFileFn(file)
            uploadResults.push({ base64, location })
            modalState.current = uploadResults.length
            modalState.percent = +((modalState.current / modalState.total) * 100).toFixed()
        } catch {
            notification.error({
                message: '错误',
                description: '图片上传失败，请稍后重试。。。'
            })
            return Promise.reject()
        }
    }
    return uploadResults
}

const replaceBase64WithUrls = async (text: String) => {
    const base64Pattern = /data:\w+\/[a-zA-Z+\-.]+;base64,[a-zA-Z0-9+/=]+/g
    const matches = text.match(base64Pattern)

    if (!matches) return text
    modalState.visible = true
    modalState.total = matches.length
    const uploadResults = await fetchSequentially(matches)

    onCancel()

    let resultText = text
    uploadResults.forEach(({ base64, location }: { base64: string; location: string }) => {
        resultText = resultText.replace(base64, location)
    })

    return resultText
}

// 自定义图片上传
const myFileUpload = (file: any, callback: any) => {
    if (/image/i.test(file.type)) {
        uploadFile(file, () => {})
            .then(({ data: response }) => {
                callback(response[0].location, {
                    width: '50%',
                    height: 'auto',
                    name: `${file.name.replace(/\.[^.]+$/, '')}`
                })
            })
            .catch((e) => {
                console.log(e)
            })
    } else {
        notification.warning({
            message: '提示',
            description: '当前仅支持图片上传'
        })
    }
}

const onPaste = (clipboardData: DataTransfer | null) => {
    if (!clipboardData) return ''
    const htmlData = clipboardData.getData('text/html')
    const plainText = clipboardData.getData('text/plain')

    let markdownContent = ''

    if (htmlData) {
        // 如果有 HTML 数据，将其转换为 Markdown
        markdownContent = cherry.engine.makeMarkdown(htmlData)

        const spaceReg = /(?<=\n)\s*/g
        markdownContent = markdownContent
            .replace(spaceReg, '')
            .replace(/:-:/g, ':-')
            .replace(/(^|\n)#+\s+/gm, '\n')
            .replace(/(^\d+(\.\d+)?\.\s)\n/g, '$1')
            .replace(/(\n\d+(\.\d+)?\.\s)\n/g, '$1')
            .replace(/●/g, '-')
            .replace(/○/g, '  -')
            .replace(/■/g, '    -')
    } else if (plainText) {
        // 如果只有纯文本，则使用纯文本
        markdownContent = plainText
    }
    return markdownContent
}

const afterChange = (val: String) => {
    if (!val.trim()) {
        emits('update:isReady', false)
    } else {
        emits('update:isReady', true)
    }
}

const onSubmit = async () => {
    const markdownContent = await replaceBase64WithUrls(cherry.getMarkdown())
    return markdownContent
}

defineExpose({ onSubmit })

onMounted(() => {
    cherry = new Cherry({
        id: props.id || 'markdown-container',
        value: content?.value || '',
        editor: {
            height: 'calc(100vh - 300px)',
            defaultModel: 'editOnly'
        },
        engine: {
            syntax: {
                table: {
                    enableChart: false
                }
            }
        },
        toolbars: {
            theme: 'light',
            // 定义顶部工具栏
            toolbar: [
                'undo',
                'redo',
                '|',
                'bold',
                'italic',
                'strikethrough',
                '|',
                'color',
                'header',
                'ruby',
                '|',
                'list',
                'panel',
                'detail',
                { insert: ['image', 'link', 'hr', 'br', 'code', 'toc', 'table', 'bar-table'] },
                'graph',
                'togglePreview'
            ],
            toolbarRight: ['fullScreen', 'export']
        },
        callback: {
            fileUpload: myFileUpload,
            onPaste: onPaste
        },
        event: {
            afterChange: afterChange
        },
        previewer: {
            enablePreviewerBubble: false
        } as any,
        // 预览区域跟随编辑器光标自动滚动
        autoScrollByCursor: false,

        ...props.cherryConfig
    })
})
</script>
<style lang="scss" scoped>
:deep(.cherry) {
    border: 1px solid rgba(6, 7, 9, 0.1);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: none;
    .cherry-toolbar {
        box-shadow: none;
        border-bottom: 1px solid rgba(6, 7, 9, 0.1);
    }
    .cherry-editor {
        background-color: #fff;
        .CodeMirror {
            background-color: #fff;
        }
    }
    .cherry-previewer {
        border-left: 1px solid rgba(6, 7, 9, 0.1);
        background-color: #fff;
    }
    img {
        max-width: 300px;
        display: block;
    }
}
</style>
