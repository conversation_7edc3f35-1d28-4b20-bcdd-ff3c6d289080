import { ChatFlow } from './ChatFlow'
import { ChatMessage } from './ChatMessage'
import { ChatMessageFeedback } from './ChatMessageFeedback'
import { Credential } from './Credential'
import { Tool } from './Tool'
import { Assistant } from './Assistant'
import { Variable } from './Variable'
import { Space } from './Space'
import { UserSpace } from './UserSpace'
import { Dataset } from './Dataset'
import { Knowledge } from './Knowledge'
import { KnowledgeSlice } from './KnowledgeSlice'
import { ChatMessageAccept } from './ChatMessageAccept'
import { Execution } from './Execution'
import { MCP } from './MCP'
import { Hot } from './Hot'

export const entities = {
    ChatFlow,
    ChatMessage,
    ChatMessageFeedback,
    Credential,
    Tool,
    Assistant,
    Variable,
    Space,
    UserSpace,
    Dataset,
    Knowledge,
    KnowledgeSlice,
    ChatMessageAccept,
    Execution,
    MCP,
    Hot
}
