<template>
    <a-modal v-model:open="open" title="文档迁移">
        <p>请选择要迁移的数据集：</p>
        <a-select v-model:value="datasetId" :options="datasetOptions" class="w-full">
            <template #option="{ value, label }">
                {{ label }}<span class="text-[#06070980] ml-2">id:{{ value }}</span>
            </template>
        </a-select>
        <template #footer>
            <a-button @click="emits('update:visible', false)">取消</a-button>
            <a-button type="primary" :disabled="!datasetId" @click="onMove">确认</a-button>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, toRefs } from 'vue'

const props = defineProps<{ visible: boolean; knowledge: string[] }>()
const emits = defineEmits(['update:visible', 'refresh'])
import { getDatasets } from '@/api/space'
import { knowldegeMove } from '@/api/knowledge'
import { useRoute } from 'vue-router'
import { notification } from 'ant-design-vue'
const open = computed({
    get: () => {
        return props.visible
    },
    set: (val) => {
        emits('update:visible', val)
    }
})

const { knowledge } = toRefs(props)

const datasetId = ref('')

const datasetOptions = ref([])

const route = useRoute()

const getDatasetOptions = async () => {
    const res = await getDatasets(route.params.spaceId as string)
    datasetOptions.value = res.data
        .map((item: any) => {
            return {
                label: item.title,
                value: item.id
            }
        })
        .filter((item: any) => item.value !== route.params.datasetId)
}

const onMove = async () => {
    const res = await knowldegeMove({ knowledgeIds: knowledge.value, datasetId: datasetId.value })
    notification.success({
        message: '成功',
        description: res.message
    })
    emits('update:visible', false)
    emits('refresh')
}

onMounted(() => {
    getDatasetOptions()
})
</script>

<style scoped></style>
