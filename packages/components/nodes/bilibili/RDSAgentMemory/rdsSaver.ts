import { BaseCheckpointSaver, Checkpoint, CheckpointMetadata } from '@langchain/langgraph'
import { RunnableConfig } from '@langchain/core/runnables'
import { BaseMessage } from '@langchain/core/messages'
import { DataSource } from 'typeorm'
import { CheckpointTuple, SaverOptions, SerializerProtocol } from './interface'
import { IMessage, MemoryMethods } from '../../../src/Interface'
import { mapChatMessageToBaseMessage } from '../../../src/utils'

export class RDSSaver extends BaseCheckpointSaver implements MemoryMethods {
    config: SaverOptions
    threadId: string
    tableName = 'ai_checkpoints'

    constructor(config: SaverOptions, serde?: SerializerProtocol<Checkpoint>) {
        super(serde)
        this.config = config
        const { threadId } = config
        this.threadId = threadId
    }

    private async getDataSource(): Promise<DataSource> {
        return this.config.appDataSource
    }

    async getTuple(config: RunnableConfig): Promise<CheckpointTuple | undefined> {
        const dataSource = await this.getDataSource()

        const thread_id = config.configurable?.thread_id || this.threadId
        const checkpoint_id = config.configurable?.checkpoint_id

        try {
            const queryRunner = dataSource.createQueryRunner()
            const sql = checkpoint_id
                ? `SELECT checkpoint, parent_id, metadata FROM ${this.tableName} WHERE thread_id = ? AND checkpoint_id = ?`
                : `SELECT thread_id, checkpoint_id, parent_id, checkpoint, metadata FROM ${this.tableName} WHERE thread_id = ? ORDER BY checkpoint_id DESC LIMIT 1`

            const rows = await queryRunner.manager.query(sql, checkpoint_id ? [thread_id, checkpoint_id] : [thread_id])
            await queryRunner.release()

            if (rows && rows.length > 0) {
                const row = rows[0]
                return {
                    config: {
                        configurable: {
                            thread_id: row.thread_id || thread_id,
                            checkpoint_id: row.checkpoint_id || checkpoint_id
                        }
                    },
                    checkpoint: (await this.serde.parse(row.checkpoint.toString())) as Checkpoint,
                    metadata: (await this.serde.parse(row.metadata.toString())) as CheckpointMetadata,
                    parentConfig: row.parent_id
                        ? {
                              configurable: {
                                  thread_id,
                                  checkpoint_id: row.parent_id
                              }
                          }
                        : undefined
                }
            }
        } catch (error) {
            console.error(`Error retrieving ${this.tableName}`, error)
            throw new Error(`Error retrieving ${this.tableName}`)
        }
        return undefined
    }

    async *list(config: RunnableConfig, limit?: number, before?: RunnableConfig): AsyncGenerator<CheckpointTuple, void, unknown> {
        const dataSource = await this.getDataSource()
        const queryRunner = dataSource.createQueryRunner()
        try {
            const threadId = config.configurable?.thread_id || this.threadId
            let sql = `SELECT thread_id, checkpoint_id, parent_id, checkpoint, metadata FROM ${this.tableName} WHERE thread_id = ? ${
                before ? 'AND checkpoint_id < ?' : ''
            } ORDER BY checkpoint_id DESC`
            if (limit) {
                sql += ` LIMIT ${limit}`
            }
            const args = [threadId, before?.configurable?.checkpoint_id].filter(Boolean)

            const rows = await queryRunner.manager.query(sql, args)
            await queryRunner.release()

            if (rows && rows.length > 0) {
                for (const row of rows) {
                    yield {
                        config: {
                            configurable: {
                                thread_id: row.thread_id,
                                checkpoint_id: row.checkpoint_id
                            }
                        },
                        checkpoint: (await this.serde.parse(row.checkpoint.toString())) as Checkpoint,
                        metadata: (await this.serde.parse(row.metadata.toString())) as CheckpointMetadata,
                        parentConfig: row.parent_id
                            ? {
                                  configurable: {
                                      thread_id: row.thread_id,
                                      checkpoint_id: row.parent_id
                                  }
                              }
                            : undefined
                    }
                }
            }
        } catch (error) {
            console.error(`Error listing checkpoints`, error)
            throw new Error(`Error listing checkpoints`)
        }
    }

    async put(config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata): Promise<RunnableConfig> {
        const dataSource = await this.getDataSource()

        if (!config.configurable?.checkpoint_id) return {}
        try {
            const queryRunner = dataSource.createQueryRunner()

            if (checkpoint.channel_values?.messages) {
                let msgs = checkpoint.channel_values.messages as BaseMessage[]

                // 1) 先折叠多余的 AI 或用户消息
                msgs = this.collapseMessages(msgs)

                // 2) 再只保留最近 6 条 (3 轮对话)
                msgs = msgs.slice(-6)

                // 写回 checkpoint
                checkpoint.channel_values.messages = msgs
            }

            const row = [
                config.configurable?.thread_id || this.threadId,
                checkpoint.id,
                config.configurable?.checkpoint_id,
                Buffer.from(this.serde.stringify(checkpoint)), // Encode to binary
                Buffer.from(this.serde.stringify(metadata)) // Encode to binary
            ]

            const query = `INSERT INTO ${this.tableName} (thread_id, checkpoint_id, parent_id, checkpoint, metadata)
                           VALUES (?, ?, ?, ?, ?)
                               ON DUPLICATE KEY UPDATE checkpoint = VALUES(checkpoint), metadata = VALUES(metadata)`

            await queryRunner.manager.query(query, row)

            // Delete older checkpoints, keeping only the most recent 4
            const deleteQuery = `DELETE FROM ${this.tableName} WHERE thread_id = ? AND checkpoint_id NOT IN (
                SELECT checkpoint_id FROM (
                                              SELECT checkpoint_id FROM ${this.tableName} WHERE thread_id = ? ORDER BY checkpoint_id DESC LIMIT 4
                                          ) AS subquery
            )`
            await queryRunner.manager.query(deleteQuery, [this.threadId, this.threadId])

            await queryRunner.release()
        } catch (error) {
            console.error('Error saving checkpoint', error)
            throw new Error('Error saving checkpoint')
        }

        return {
            configurable: {
                thread_id: config.configurable?.thread_id || this.threadId,
                checkpoint_id: checkpoint.id
            }
        }
    }

    async delete(threadId: string): Promise<void> {
        if (!threadId) return

        const dataSource = await this.getDataSource()

        try {
            const queryRunner = dataSource.createQueryRunner()
            const query = `DELETE FROM ${this.tableName} WHERE thread_id = ?;`
            await queryRunner.manager.query(query, [threadId])
            await queryRunner.release()
        } catch (error) {
            console.error(`Error deleting thread_id ${threadId}`, error)
        }
    }

    async getChatMessages(
        overrideSessionId = '',
        returnBaseMessages = false,
        prependMessages?: IMessage[]
    ): Promise<IMessage[] | BaseMessage[]> {
        if (!overrideSessionId) return []

        const chatMessage = await this.config.appDataSource.getRepository(this.config.databaseEntities['ChatMessage']).find({
            where: {
                sessionId: overrideSessionId,
                chatflowid: this.config.chatflowid
            },
            order: {
                createdDate: 'ASC'
            }
        })

        if (prependMessages?.length) {
            chatMessage.unshift(...prependMessages)
        }

        if (returnBaseMessages) {
            return await mapChatMessageToBaseMessage(chatMessage)
        }

        let returnIMessages: IMessage[] = []
        for (const m of chatMessage) {
            returnIMessages.push({
                message: m.content as string,
                type: m.role
            })
        }

        //提取数组最后6条
        returnIMessages = returnIMessages.slice(-6)

        return returnIMessages
    }

    async addChatMessages(): Promise<void> {
        // Empty as it's not being used
    }

    async clearChatMessages(overrideSessionId = ''): Promise<void> {
        if (!overrideSessionId) return
        await this.delete(overrideSessionId)
    }

    collapseMessages(messages: BaseMessage[]): BaseMessage[] {
        const collapsed: BaseMessage[] = []

        for (const msg of messages) {
            // 1) 过滤掉 ToolMessage
            if (msg.getType?.() === 'tool') {
                continue
            }

            // 2) 如果本条和上一条是相同角色，就覆盖上一条
            if (collapsed.length > 0) {
                let lastMsg = collapsed[collapsed.length - 1]
                const lastIsAI = lastMsg.getType?.() === 'ai'
                const currentIsAI = msg.getType?.() === 'ai'

                if (lastIsAI && currentIsAI) {
                    // 用新消息覆盖上一条
                    lastMsg = msg
                    // 如果还有别的字段（如 extraData），也一并覆盖
                    continue
                }
            }

            // 否则直接 push
            collapsed.push(msg)
        }

        return collapsed
    }
}
