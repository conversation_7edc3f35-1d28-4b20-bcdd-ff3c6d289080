<template>
    <div class="flex ml-25" style="margin: 15px 25px 0">
        <a-button @click="add" type="primary">新增</a-button>
    </div>
    <a-table :pagination="false" :columns="columns" :data-source="dataSource" bordered hideOnSinglePage class="p-5">
        <template #headerCell="{ column }">
            <template v-if="column.dataIndex === 'property'">
                property&nbsp;&nbsp;
                <a-tooltip>
                    <template #title>请使用英文</template>
                    <QuestionCircleOutlined />
                </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'description'">
                description&nbsp;&nbsp;
                <a-tooltip>
                    <template #title>AI会在调用工具时根据description,自动填入内容</template>
                    <QuestionCircleOutlined />
                </a-tooltip>
            </template>
        </template>
        <template #bodyCell="{ column, index, record }">
            <template v-if="['property', 'description', 'required'].includes(column.dataIndex)">
                <a-input
                    v-if="column.dataIndex !== 'required'"
                    :value="dataSource[index][column.dataIndex]"
                    @change="(e: ChangeEvent)=> {dataSource[index][column.dataIndex] = e.target.value}"
                    class="m-[-5px] mx-0"
                />

                <a-radio-group
                    v-if="column.dataIndex === 'required'"
                    :value="dataSource[index][column.dataIndex]"
                    @change="(e: RadioChangeEvent)=> {
                         dataSource[index][column.dataIndex] = e.target?.value}"
                    button-style="solid"
                    size="small"
                >
                    <a-radio-button :value="true">true</a-radio-button>
                    <a-radio-button :value="false">false</a-radio-button>
                </a-radio-group>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
                <div class="editable-row-operations">
                    <a-popconfirm title="确定删除？" @confirm="cancel(record.id)">
                        <a>删除</a>
                    </a-popconfirm>
                </div>
            </template>
        </template>
    </a-table>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from 'vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import type { RadioChangeEvent } from 'ant-design-vue'
import type { ChangeEvent } from 'ant-design-vue/es/_util/EventInterface'

const columns = [
    {
        title: 'Property',
        dataIndex: 'property',
        width: '18%'
    },
    {
        title: 'Description',
        dataIndex: 'description',
        width: '35%'
    },
    {
        title: 'Required',
        dataIndex: 'required',
        width: '15%'
    },
    {
        title: '操作',
        width: '15%',
        dataIndex: 'operation'
    }
]

const props = defineProps(['schema'])
const dataSource = ref<any[]>(JSON.parse(props.schema || '[]'))
const emit = defineEmits(['update'])

watch(
    dataSource,
    (arr) => {
        arr = arr.map((v) => ({ ...v, type: 'string' }))
        emit('update', JSON.stringify(arr))
    },
    { deep: true }
)

const add = () => {
    dataSource.value.push({
        id: Math.floor(Math.random() * 100000000).toString(),
        property: '',
        description: '',
        required: true
    })
}
const cancel = (id: string) => {
    dataSource.value = dataSource.value.filter((o) => o.id !== id)
}
</script>
<style scoped>
.editable-row-operations a {
    margin-right: 8px;
}
</style>
