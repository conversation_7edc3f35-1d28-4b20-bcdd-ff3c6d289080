import { Tool, ToolParams } from '@langchain/core/tools'
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager'

export interface InfoSearchArgs extends ToolParams {
    secret?: string
}

interface InfoSearchResponse {
    results: InfoSearchContent[]
}

interface InfoSearchContent {
    content: {
        id: string
        type: string
        status: string
        title: string
        restrictions: Record<string, unknown>
        _links: {
            webui: string
            tinyui: string
            self: string
        }
        _expandable: {
            container: string
            metadata: string
            extensions: string
            operations: string
            children: string
            history: string
            ancestors: string
            body: string
            version: string
            descendants: string
            space: string
        }
    }
    title: string
    excerpt: string
    url: string
    resultGlobalContainer: {
        title: string
        displayUrl: string
    }
    entityType: string
    iconCssClass: string
    lastModified: string
    friendlyLastModified: string
    timestamp: number
}

const decodeHtmlEntities = (str: string) => {
    // 使用内置 DOMParser 转义 HTML 实体
    return str
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'")
        .replace(/&hellip;/g, '…')
}

export class InfoSearch extends Tool {
    static lc_name() {
        return 'InfoSearch'
    }

    name = 'info-search'
    description = '当需要在info上查找某些内容或时很有用。输入应该是要搜索的关键词'

    private secret: string

    constructor({ secret }: InfoSearchArgs) {
        super()
        this.secret = secret || 'OTc4MDk1NTQ1MDc5OqdIRzMcw6HnKLT/lOd8CJ3w+bax'
    }

    async _call(inputs: string, runManager?: CallbackManagerForToolRun) {
        if (!inputs) {
            return '搜索info时出现问题，缺少搜索项'
        }

        const results = await this.search(inputs)

        let txt = ''
        for (const result of results) {
            let excerpt = result.excerpt.replace(/@@@hl@@@|@@@endhl@@@/g, '')
            excerpt = decodeHtmlEntities(excerpt)

            txt += `标题: ${result.content.title} \n`
            txt += `摘要: ${excerpt} \n`
            txt += `链接: https://info.bilibili.co/pages/viewpage.action?pageId=${result.content.id} \n\n`
        }

        return txt
    }

    private async search(query: string) {
        const apiUrl = 'https://info.bilibili.co/rest/api/search'
        const token = this.secret

        try {
            const res = await fetch(`${apiUrl}?cql=siteSearch~'${query}'%20AND%20lastmodified%20%3E%3D%20now(%22-2y%22)&limit=10`, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`
                }
            })

            const data: InfoSearchResponse = await res.json()
            if (!data?.results) throw new Error('搜索info时发生问题，请稍后再试')

            return data.results
        } catch (e) {
            console.error(e)
            throw new Error('搜索info时发生问题，请稍后再试')
        }
    }
}
