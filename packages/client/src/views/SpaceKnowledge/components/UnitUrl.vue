<template>
    <a-form layout="vertical" class="my-[20px] m-auto">
        <a-form-item label="网页链接">
            <div class="flex gap-2">
                <a-input v-model:value="unitInfo.url" placeholder="输入URL" :disabled="isLoading" />
                <a-button @click="handleGetUrlCtx" :disabled="!unitInfo.url" :loading="isLoading">提取内容</a-button>
            </div>
        </a-form-item>
        <a-form-item label="内容根节点">
            <div class="flex gap-2">
                <a-input v-model:value="unitInfo.rootEle" class="w-[300px]" placeholder="输入URL" :disabled="isLoading" />
                <a-typography-text class="leading-[32px]" type="secondary">支持dom选择器写法 div、div#id、div.class ...</a-typography-text>
            </div>
        </a-form-item>
        <a-form-item label="内容预览">
            <a-textarea v-model:value="unitInfo.content" class="!h-[300px]" />
        </a-form-item>
        <slot />
    </a-form>
</template>

<script lang="ts" setup>
import { defineEmits, defineProps } from 'vue'
import { reactive, ref } from 'vue'
import { getWebInfo, webUpload } from '@/api/space'
import { message } from 'ant-design-vue'
defineProps({
    isReady: Boolean
})

const isLoading = ref(false)

const emits = defineEmits(['update:isReady'])

const unitInfo = reactive({ url: '', content: '', rootEle: 'body', title: '' })

const handleGetUrlCtx = async () => {
    try {
        isLoading.value = true

        const res = await getWebInfo(unitInfo.url, unitInfo.rootEle)

        unitInfo.content = res.data.content
        unitInfo.title = res.data.title

        isLoading.value = false
        if (res.data.content == '') {
            message.warning('该链接暂无可以抓取的内容')
            emits('update:isReady', false)
        } else {
            emits('update:isReady', true)
        }
    } catch (e) {
        isLoading.value = false
    }

    isLoading.value = false
}

defineExpose({
    getContent: async () => {
        const res = await webUpload({ title: unitInfo.title, content: unitInfo.content })

        // 转换为提交字段
        return { urlInfo: { ...res.data, remoteUrl: unitInfo.url }, textSplit: JSON.stringify({ web: { root: unitInfo.rootEle } }) }
    }
})
</script>
