<template>
    <div class="flex justify-between items-center mb-[20px]">
        <a-input-search v-model:value="keyword" placeholder="搜索MCP服务" style="width: 250px" @search="onSearch" />
        <a-button type="primary" @click="handleOpen()">创建MCP服务</a-button>
    </div>

    <a-table
        class="copilot-concise-table"
        :dataSource="dataSource"
        :columns="columns"
        :scroll="{ y: tableH, x: 1000 }"
        @change="getPageData"
        :pagination="{ current, pageSize: defaultPageSize, total }"
    >
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'id'">
                <div class="m-[-16px] p-[16px]" @click.stop>{{ record.id }}</div>
            </template>
            <template v-else-if="column.key === 'action'">
                <a-space>
                    <a-button type="link" @click="handleOpen(record)">信息编辑</a-button>
                    <a-button type="link" danger @click="handleDel(record)">删除</a-button>
                </a-space>
            </template>
        </template>
    </a-table>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { computed, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { getMCPList, deleteMCP } from '@/api/mcp'
import CreateMCPComponent from './components/CreateMCP.vue'
import { componentFunctionCall } from '@/utils'
import type { IMCPInfo } from '@/interface/mcp'

const createMCP = componentFunctionCall(CreateMCPComponent)

const route = useRoute()

const dataSource = ref([])

const columns = [
    {
        title: 'id',
        dataIndex: 'id',
        key: 'id'
    },
    {
        title: '名称',
        dataIndex: 'title',
        minWidth: 80
    },
    {
        title: 'name',
        dataIndex: 'name',
        key: 'name'
    },
    {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        minWidth: 300
    },
    {
        title: '创建时间',
        dataIndex: 'ctime',
        key: 'ctime'
    },
    {
        title: '更新时间',
        dataIndex: 'mtime',
        key: 'mtime'
    },
    {
        title: '操作',
        key: 'action',
        width: 240,
        fixed: 'right'
    }
]

const loading = ref(false)
const current = ref(1)
const total = ref(0)
const defaultPageSize = 12
const keyword = ref('')

const spaceId = route.params.spaceId as string

const tableH = computed(() => {
    return document.body.offsetHeight - 240
})

const getPageData = ({ current: page }: { current: string }) => {
    current.value = Number(page)
    getData()
}

const getData = async () => {
    loading.value = true
    try {
        const res = await getMCPList({ spaceId, page: current.value, pageSize: defaultPageSize, keyword: keyword.value })
        dataSource.value = res.data.rows
        total.value = Number(res.data.count)
    } finally {
        loading.value = false
    }
}

const onSearch = () => {
    current.value = 1
    getData()
}

const handleOpen = (data = {} as IMCPInfo) => {
    createMCP({ spaceId, data, onChange: getData })
}

const handleDel = (data: any) => {
    Modal.confirm({
        title: `警告`,
        content: `确认要删除 "${data.title}" MCP服务？`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await deleteMCP(data.id)
            message.success(`${data.title} 删除成功`)
            await getData()
        }
    })
}

getData()
</script>
