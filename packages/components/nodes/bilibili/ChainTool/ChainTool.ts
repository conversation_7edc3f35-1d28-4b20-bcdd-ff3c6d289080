import { BaseChain } from 'langchain/chains'
import { INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'
import { ChainTool } from './core'

class ChainTool_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    tags: string[]
    category: string
    baseClasses: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'Bili Chain Tool'
        this.name = 'biliChainTool'
        this.version = 1.0
        this.type = 'ChainTool'
        this.icon = 'chaintool.svg'
        this.tags = ['bilibili']
        this.category = 'Tools'
        this.description = '通过chain tool可以让Agent挂载多个子进程'
        this.baseClasses = [this.type, 'Tool', ...getBaseClasses(ChainTool)]
        this.inputs = [
            {
                label: 'Base Chain',
                name: 'baseChain',
                type: 'BaseChain'
            },
            {
                label: 'Chain Name',
                name: 'name',
                type: 'string',
                placeholder: '使用大驼峰格式填写工具名称'
            },
            {
                label: 'Chain Description',
                name: 'description',
                type: 'string',
                rows: 3,
                placeholder: '请告诉AI什么时候使用本工具,工具适用于哪些功能'
            },
            {
                label: 'Return Direct',
                name: 'returnDirect',
                type: 'boolean',
                description: '当关闭Direct时,子模型不应该启用stream模式',
                optional: true,
                additionalParams: true
            },
            {
                label: 'Stream',
                name: 'streaming',
                description: '子模型是否启用了stream模式',
                type: 'boolean',
                optional: true,
                additionalParams: true
            },
            {
                label: 'Icon',
                name: 'icon',
                description: '个性化图标,当用户输入@时会展示该图标,填写完整的http路径',
                type: 'string',
                optional: true,
                additionalParams: true
            }
        ]
    }

    async init(nodeData: INodeData): Promise<any> {
        const name = nodeData.inputs?.name as string
        const description = nodeData.inputs?.description as string
        const baseChain = nodeData.inputs?.baseChain as BaseChain
        const returnDirect = nodeData.inputs?.returnDirect as boolean
        const streaming = nodeData.inputs?.streaming as boolean

        const obj = {
            name,
            description,
            chain: baseChain
        } as any

        if (returnDirect) obj.returnDirect = returnDirect
        if (streaming) obj.metadata = { streaming }

        const tool = new ChainTool(obj)

        return tool
    }
}

module.exports = { nodeClass: ChainTool_Tools }
