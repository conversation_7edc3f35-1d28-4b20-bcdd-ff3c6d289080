export const dayOption = [
    { value: '0', label: '不自动更新' },
    { value: '1', label: '每天' },
    { value: '3', label: '每3天' },
    { value: '7', label: '每7天' },
    { value: '30', label: '每30天' }
]
export const dataSource = [
    {
        key: 'localDoc',
        title: '本地文档',
        accept: '.pdf,.txt,.md,.docx',
        description: '上传 PDF, TXT, DOCX, Markdown 格式的本地文件',
        icon: 'DocumentTextIcon',
        limit: 20
    },
    {
        key: 'localExcel',
        title: '本地表格',
        accept: '.csv,.xlsx,.xls',
        description: '上传CSV, Excel 格式的本地文件； 注意：Excel暂时不支持图片内容',
        icon: 'TableCellsIcon',
        limit: 1
    },
    { key: 'infoDoc', title: 'Info文档', type: 'url', description: '获取Info页面导入知识库中', icon: 'BookOpenIcon' },
    {
        key: 'urlDoc',
        title: '在线文档（BETA）',
        type: 'url',
        description: '获取在线网页内容，暂不支持SPA页面的抓取',
        icon: 'GlobeAltIcon'
    },
    {
        key: 'manualImportDoc',
        title: '手动导入文档',
        description: '已支持腾讯文档',
        icon: 'BarsArrowUpIcon'
    },
    { key: 'API', title: 'API', description: '获取API内容，敬请期待', disabled: true, icon: 'LinkIcon' },
    { key: 'AKSO', title: 'AKSO', description: '获取AKSO内容，敬请期待', disabled: true, icon: 'RocketLaunchIcon' }
]
