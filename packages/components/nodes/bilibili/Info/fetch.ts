import fetch from 'node-fetch'
import * as cheerio from 'cheerio'

export async function fetchInfo(pageId: string) {
    const apiUrl = 'https://info.bilibili.co/rest/api'
    const token = 'OTc4MDk1NTQ1MDc5OqdIRzMcw6HnKLT/lOd8CJ3w+bax'

    // const pageId = url.match(/pageId=(\w+)/)?.[1]

    let pageIds = pageId.split(',')
    let txt: string = ''

    for (const pageId of pageIds) {
        if (pageId) {
            const res = await fetch(`${apiUrl}/content/${pageId}?expand=body.storage.value,version`, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`
                }
            })

            if (res.status !== 200) continue
            const info = await res.json()
            txt += `标题: ${info.title} \n\n`

            const $html = cheerio.load(info.body?.storage?.value, {
                xmlMode: true
            })

            $html('p, div, li, h1, h2, h3, h4, h5, tr, br').each(function () {
                $html(this).append('\n') // 在<p>和<div>标签内容后添加换行符
            })

            // 处理markdown
            $html('ac\\:parameter').each(function () {
                // 删除节点
                $html(this).remove()
            })

            $html('ac\\:plain-text-body').each(function () {
                // 替换节点
                $html(this).replaceWith(`\`\`\`\n${$html(this).text()}\n\`\`\`\n`)
            })

            txt += `内容: ${$html.text()}`
            txt += '\n\n'
        }
    }

    return txt
}
