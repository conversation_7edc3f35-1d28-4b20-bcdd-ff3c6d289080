<template>
    <div ref="codeEditBox" class="codeEditBox" :style="computedStyle"></div>
</template>
<script lang="ts" setup>
import { onBeforeUnmount, onMounted, onUnmounted, ref, watch } from 'vue'
import type { CSSProperties } from 'vue'
import { editorProps } from '@/interface/monacoEditorType'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'
import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import * as monaco from 'monaco-editor' // 全量加载
import { apiType } from '@/interface/monacoExtraLib'

const env = import.meta.env || window.location.origin
const props = defineProps(editorProps)
const emit = defineEmits(['updateCode', 'change', 'editor-mounted'])
const base = `${window.location.protocol}${env.VITE_PUBLIC_PATH}`

function createWorkerBlobUrl(workerUrl: string) {
    const blob = new Blob([`importScripts('${workerUrl}');`], { type: 'application/javascript' })
    const blobUrl = URL.createObjectURL(blob)
    return new Worker(blobUrl) // 返回 Worker 实例
}

self.MonacoEnvironment = {
    getWorker(moduleId: string, label: string) {
        if (env.DEV) {
            if (['typescript', 'javascript'].includes(label)) {
                return new tsWorker()
            }
            return new EditorWorker()
        }
        if (label === 'typescript' || label === 'javascript') {
            // 为 TypeScript 和 JavaScript 提供 Blob URL
            return createWorkerBlobUrl(`${base}/ts.worker.bundle.js`) // 替换为你实际的 worker 路径
        }
        // 其他 worker 的 Blob URL
        return createWorkerBlobUrl(`${base}/editor.worker.bundle.js`)
    }
}
monaco.languages.typescript.typescriptDefaults.setEagerModelSync(true)
let editor: monaco.editor.IStandaloneCodeEditor | any
const codeEditBox = ref()

const init = () => {
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
        noSemanticValidation: false,
        noSyntaxValidation: false,
        noSuggestionDiagnostics: false,
        onlyVisible: true,
        diagnosticCodesToIgnore: [1375, 1378, 1108, 2580, 80005] // 指定类型免检查 @TODO:其他自定义库未全支持
    })
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
        target: monaco.languages.typescript.ScriptTarget.ES2020,
        allowNonTsExtensions: true,
        noErrorTruncation: true,
        noImplicitAny: false,
        strictNullChecks: false,
        noUnusedLocals: false,
        strict: true
    })

    monaco.editor.onDidChangeMarkers(([uri]) => {
        const markers = monaco.editor.getModelMarkers({ resource: uri })
        // markers是返回的错误信息数组，可赋值给需要判断语法错误的关键词
        console.info('markers', markers)
    })

    editor = monaco.editor.create(codeEditBox.value, {
        value: props.modelValue,
        language: props.language,
        theme: props.theme,
        ...props.options
    })
    editor.onDidChangeModelContent(() => {
        const value = editor.getValue()
        emit('updateCode', value)
        emit('change', value)
    })
    monaco.languages.typescript.javascriptDefaults.addExtraLib(apiType)
    emit('editor-mounted', editor)
}
const getApiType = (keys: string[]) => {
    // 获取当前mock的变量
    return `
  ${keys
      .map((v) => {
          return ` const $${v} = '输入的参数${v}'
    `
      })
      .toString()}
  `
}
watch(
    () => props.modelValue,
    (newValue) => {
        if (editor) {
            const value = editor.getValue()
            if (newValue !== value) {
                editor.setValue(newValue)
            }
        }
    }
)
watch(
    () => props.schemaKey,
    (newValue) => {
        if (newValue) {
            // 注入右侧调试变量
            monaco.languages.typescript.javascriptDefaults.addExtraLib(getApiType(newValue))
        }
    },
    { immediate: true }
)

onBeforeUnmount(() => {
    editor.dispose()
})

onMounted(() => {
    init()
})

onUnmounted(() => {
    editor.dispose()
})

const computedStyle: CSSProperties = {
    height: props.height,
    width: props.width,
    textAlign: 'left'
}
</script>
<style scoped></style>
