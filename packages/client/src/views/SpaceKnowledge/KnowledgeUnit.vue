<template>
    <a-breadcrumb>
        <a-breadcrumb-item class="cursor-pointer" @click="onBack">知识库</a-breadcrumb-item>
        <a-breadcrumb-item>{{ datasetInfo.title }}</a-breadcrumb-item>
    </a-breadcrumb>

    <div class="flex justify-between items-center mt-[10px]">
        <div>{{ datasetInfo.title }}</div>
        <a-space>
            <a-button :disabled="!rowSelection.selectedRowKeys.length" @click="openMoveModal">文档迁移</a-button>
            <a-button type="primary" @click="showModal">新建文档</a-button>
        </a-space>
    </div>
    <a-divider class="mx-[-24px] my-[10px] w-auto"></a-divider>
    <a-flex class="h-[40px]">
        <a-form-item label="文档名称">
            <a-input v-model:value="searchForm.filename" allowClear @pressEnter="handleSearch" />
        </a-form-item>
        <a-button class="ml-[10px]" type="primary" @click="handleSearch" :disabled="isLoading">搜索</a-button>
        <a-button class="ml-[10px]" @click="handleReset" :disabled="isLoading">重置</a-button>
    </a-flex>
    <a-alert v-if="diagnoseCount" class="px-5 py-2 rounded my-1" banner>
        <template #message> 当前知识库存在可优化{{ diagnoseCount }}项,<a href="" @click.prevent="onDiagnose">请诊断</a> </template>
    </a-alert>
    <a-layout-content class="h-full overflow-y-auto">
        <a-table
            class="copilot-concise-table"
            :dataSource="dataSource"
            :columns="columns"
            :pagination="pagination"
            @change="handleChange"
            :loading="isLoading"
            :scroll="{ y: tableH }"
            :row-selection="{ selectedRowKeys: rowSelection.selectedRowKeys, onChange: onSelectChange }"
            rowKey="id"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.key == 'filename'">
                    <a
                        class="text-left overflow-hidden text-ellipsis"
                        :href="record.location"
                        target="_blank"
                        @click.prevent="onDetail(record)"
                        >{{ record.filename }}</a
                    >
                    <br />
                    <a-typography-text type="secondary">{{ record.id }}</a-typography-text>
                </template>

                <template v-if="column.key === 'action'">
                    <a-space>
                        <a-button type="link" @click="onDetail(record)">详情</a-button>
                        <a-button type="link" @click="handleDel(record)">删除</a-button>
                    </a-space>
                </template>
            </template>
        </a-table>
    </a-layout-content>

    <create-unit ref="createUnitRef" :dataset-info="datasetInfo" @callback="getData" />

    <DiagnoseModal v-model:visible="diagnoseVisible"></DiagnoseModal>
    <MoveModal v-model:visible="moveVisible" v-if="moveVisible" :knowledge="rowSelection.selectedRowKeys" @refresh="getData"></MoveModal>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { computed, onMounted, ref, h, onUnmounted, reactive } from 'vue'
import CreateUnit from '@/views/SpaceKnowledge/components/CreateUnit.vue'
import { delKnowledge, getDataset, getKnowledge } from '@/api/space'
import { sliceDiagnose } from '@/api/knowledge'
import { message, Modal, Tag } from 'ant-design-vue'
import DiagnoseModal from './components/DiagnoseModal.vue'
import MoveModal from './components/MoveModal.vue'
const route = useRoute()

const router = useRouter()

const dataSource = ref([])

const datasetInfo = reactive({ id: '', model: 0, title: '' })

const createUnitRef = ref<any>(null)

const searchForm = reactive({ filename: '' })

const isLoading = ref(false)

const pagination = reactive({ current: 1, pageSize: 10, total: 0, showTotal: (total: number) => `总${total}条 ` })

const diagnoseCount = ref<number>(0)

const StatusMap = new Map([
    ['0', '待同步'],
    ['1', '正常'],
    ['-1', '停用'],
    ['2', '同步错误'],
    ['3', '同步中']
])

const StatusColorMap = new Map([
    ['0', ''],
    ['1', 'success'],
    ['-1', 'error'],
    ['2', 'warning'],
    ['3', 'processing']
])

const columns = [
    {
        title: '文档名称',
        dataIndex: 'filename',
        key: 'filename'
    },
    {
        title: '类型',
        dataIndex: 'extname',
        width: 100
    },
    {
        title: '分段方式',
        dataIndex: 'textSplit',
        customRender: () => {
            return '自动'
        },
        width: 100
    },
    {
        title: '更新频率',
        dataIndex: 'autoUpdate',
        width: 120,
        customRender: ({ text, record }: any) => {
            if (record.remoteUrl == '') {
                return '-'
            }

            return text == 0 ? '不自动更新' : `每${text == 1 ? '' : text}天`
        }
    },
    {
        title: '更新时间',
        dataIndex: 'mtime',
        width: 200
    },
    {
        title: '状态',
        dataIndex: 'status',
        customRender: ({ text }: any) => {
            return h(Tag, { color: StatusColorMap.get(String(text)), bordered: false }, () => StatusMap.get(String(text)) || '-')
        },
        width: 120
    },
    {
        title: '操作',
        key: 'action',
        width: 160
    }
]

const rowSelection = reactive({
    selectedRowKeys: [] as string[]
})

const onSelectChange = (selectedRowKeys: string[]) => {
    rowSelection.selectedRowKeys = selectedRowKeys
}

// let times = null as any

onMounted(async () => {
    const { spaceId, datasetId } = route.params as { spaceId: string; datasetId: string }

    const res = await getDataset({ spaceId, id: datasetId })
    Object.assign(datasetInfo, res.data)
    getData()

    const { data } = await sliceDiagnose({ datasetId, page: 1, pageSize: 20 })
    diagnoseCount.value = Number(data.count)
})

onUnmounted(() => {
    // clearInterval(times)
})

const tableH = computed(() => {
    return document.body.offsetHeight - 270
})

const getData = async () => {
    isLoading.value = true
    rowSelection.selectedRowKeys = []
    const { datasetId } = route.params as { spaceId: string; datasetId: string }
    const data = await getKnowledge(datasetId, pagination.current, pagination.pageSize, searchForm.filename)
    dataSource.value = data.data[0]
    pagination.total = data.data[1] || 0
    isLoading.value = false
}

const handleDel = (data: any) => {
    Modal.confirm({
        title: `警告`,
        content: `确认要删除 ${data.filename}`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await delKnowledge(data.id)
            message.success(`${data.filename} 删除成功`)
            await getData()
        }
    })
}

const handleSearch = async () => {
    pagination.current = 1
    getData()
}
const handleReset = () => {
    pagination.current = 1
    searchForm.filename = ''
    getData()
}

const handleChange = ({ current, pageSize }: any) => {
    pagination.current = current
    pagination.pageSize = pageSize
    getData()
}

const showModal = () => {
    createUnitRef.value?.open()
}

const onDetail = (record: any) => {
    if (datasetInfo.model === 2) {
        const { spaceId, datasetId } = route.params
        router.push(`/space/${spaceId}/knowledge/${datasetId}/detail/${record.id}`)
    } else {
        window.open(record.location)
    }
}

const onBack = () => {
    const { spaceId } = route.params
    router.push(`/space/${spaceId}/knowledge`)
}

const diagnoseVisible = ref(false)

const onDiagnose = () => {
    diagnoseVisible.value = true
}

const moveVisible = ref(false)

const openMoveModal = () => {
    moveVisible.value = true
}
</script>
