<template>
    <a-form layout="vertical" class="w-[450px] my-[50px] m-auto">
        <a-form-item class="mt-[8px]" label="Info链接">
            <div class="flex gap-2">
                <a-input v-model:value="unitInfo.url" placeholder="输入Infol链接">
                    <template #addonBefore>
                        <a-select v-model:value="infoType" class="w-[80px]">
                            <a-select-option :value="1">单页</a-select-option>
                            <a-select-option :value="2">目录</a-select-option>
                        </a-select>
                    </template>
                </a-input>
                <a-button @click="() => (infoType == 1 ? handleUpdateInfo() : handleGetInfoDir())" :disabled="!unitInfo.url">
                    提取内容
                </a-button>
            </div>
        </a-form-item>
        <template v-if="infoType === 1 && unitInfo.file?.fileKey">
            <a-form-item label="Info详情" v-if="unitInfo.file?.fileKey">
                <a-input :value="unitInfo.file.filename" disabled />
            </a-form-item>
        </template>
        <template v-if="infoType === 2 && infoList.length > 0">
            <a-form-item label="目录详情" v-if="infoList.length > 0">
                <div class="max-h-[200px] mr-[-8px] overflow-y-auto">
                    <a-flex class="mb-[10px] pr-[8px]" v-for="item in infoList" :key="item.id">
                        <a-input :value="item.title" disabled />
                        <DeleteOutlined class="cursor-pointer ml-[10px]" @click="onDelInfoItem(item)" />
                    </a-flex>
                </div>
                <div class="mt-[6px]">
                    <a-typography-text type="secondary">已提取 {{ infoList.length }} 条</a-typography-text>
                </div>
            </a-form-item>
        </template>
        <slot />
    </a-form>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps, defineExpose, toRaw, reactive } from 'vue'
import { getInfoChild, updateInfo } from '@/api/space'
import { DeleteOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { IUploadFile } from '@/api/knowledge'

const infoType = ref(1)

defineProps({
    isReady: Boolean
})

const emits = defineEmits(['update:isReady'])

const infoList = ref<any[]>([])

const unitInfo = reactive({ file: { fileKey: null, filename: '' }, url: '' })
const handleUpdateInfo = async () => {
    const res = await updateInfo(unitInfo.url)

    unitInfo.file = res.data

    emits('update:isReady', true)
}

const handleGetInfoDir = async () => {
    const res = await getInfoChild(unitInfo.url)
    if (res.data.length == 0) {
        message.warning(unitInfo.url + ' 没有提取到子页面')
        return
    }
    infoList.value = res.data
    emits('update:isReady', true)
}

const getFormData = async () => {
    switch (infoType.value) {
        case 1:
            return Promise.resolve({ type: 1, data: [toRaw(unitInfo)] })
        case 2: {
            const formData = []
            for (const item of infoList.value) {
                const res = (await updateInfo(item.link)) as { data: IUploadFile }

                formData.push({ ...res.data, remoteUrl: item.link })
            }

            return Promise.resolve({ type: 2, data: formData })
        }
    }
}

const onDelInfoItem = (item: any) => {
    infoList.value.splice(infoList.value.indexOf(item), 1)
}

defineExpose({ getFormData })
</script>
