<template>
    <a-flex justify="space-between" align="flex-start" class="mb-[20px]">
        <div>
            <a-typography-title :level="4">Bot 市场</a-typography-title>
            <a-typography-text type="secondary"> Copilot AI工作流平台已接入AiGateway所有开放模型，欢迎体验! </a-typography-text>
        </div>
        <a-space>
            <a-button type="primary" @click="openModal">Bot 管理</a-button>
        </a-space>
    </a-flex>
    <InfiniteScroll :end="end" @loadMoreFn="getData" ref="infiniteScrollRef">
        <div class="copilot-card-container">
            <a-card hoverable class="w-full" :bodyStyle="{ paddingBottom: '20px' }" v-for="item in marketData" :key="item.id">
                <a class="block w-full h-full text-black" :href="`/chat/${item.id}`" target="_blank">
                    <a-card-meta class="p-0">
                        <template #description>
                            <div class="flex gap-4">
                                <a-avatar class="flex-0" :size="64" shape="square" :src="item.avatar" />
                                <div class="flex-1 flex flex-col overflow-hidden gap-y-1">
                                    <div class="overflow-hidden font-bold text-[#1c1d23] text-lg text-ellipsis text-nowrap">
                                        {{ item.name }}
                                    </div>
                                    <a-typography-text type="secondary" style="color: var(--color-symbol-bold)"
                                        >@{{ item.creator }}</a-typography-text
                                    >
                                    <a-typography-paragraph
                                        style="color: #383743cc; margin-bottom: 8px"
                                        type="secondary"
                                        :ellipsis="{ rows: 2 }"
                                        :content="item.description || '暂无描述'"
                                    >
                                    </a-typography-paragraph>
                                    <div v-if="item.category">
                                        <a-tag v-for="it in item.category.split(';')" :key="it">{{ it }}</a-tag>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </a-card-meta>
                </a>
            </a-card>
        </div>
    </InfiniteScroll>

    <public-bot ref="publicBotRef" class="m-auto" @callback="getData" />
</template>
<script setup lang="ts">
import { nextTick, ref } from 'vue'
import { getPublicList } from '@/api/space'
import PublicBot from '@/views/SpaceMarket/components/PublicBot.vue'
import InfiniteScroll from '@/components/InfiniteScroll.vue'

const marketData = ref<any>([])
const loading = ref(true)
const end = ref(false)

const publicBotRef = ref<any>(null)
const infiniteScrollRef = ref()

const current = ref(0)
const defaultPageSize = 24

const getData = async () => {
    current.value += 1
    const res = await getPublicList({ page: current.value, pageSize: defaultPageSize })
    marketData.value = [...marketData.value, ...res.data.rows]
    loading.value = false
    if (res.data.rows.length < defaultPageSize) {
        end.value = true
    } else {
        // 如果内容高度不足以触发滚动，继续加载更多数据
        await nextTick()
        if (infiniteScrollRef.value?.container?.scrollHeight - infiniteScrollRef.value?.container?.clientHeight < 20) {
            getData()
        }
    }
}

const openModal = () => {
    publicBotRef.value?.open()
}
</script>
<style scoped>
.ant-tag {
    background-color: #fff;
    color: #1c1d2399;
    border-color: #5f67761f;
}
</style>
