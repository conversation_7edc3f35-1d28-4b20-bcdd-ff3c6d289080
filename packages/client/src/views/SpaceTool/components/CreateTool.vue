<template>
  <a-modal v-model:open="state.open" :after-close="handle.afterClose" :title="state.title" destroy-on-close
           @ok="handle.onSubmit">
    <a-form :model="state.form" autocomplete="off" layout="vertical">
      <a-form-item label="标题" name="title" v-bind="validateInfos.title">
        <a-input v-model:value="state.form.title" placeholder="标题请尽量简洁并能体现核心功能，仅展示使用"/>
      </a-form-item>
      <a-form-item label="名称" name="name" v-bind="validateInfos.name">
        <a-input v-model:value="state.form.name" placeholder="名称请遵循大写开头的英文驼峰命名规则"/>
      </a-form-item>
      <a-form-item label="描述" name="description" v-bind="validateInfos.description">
        <a-textarea v-model:value="state.form.description" :rows="4" placeholder="准确描述插件功能，能让AI更有效地利用插件"/>
      </a-form-item>

      <a-form-item label="智能体插件市场" name="isPublic">
        <a-radio-group v-model:value="state.form.isPublic" name="isPublic">
          <a-radio :value="!1">下架</a-radio>
          <a-radio :value="!0">上架</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="头像" name="iconSrc" v-bind="validateInfos.iconSrc">
        <img-upload v-model:avatar="state.form.iconSrc"/>
      </a-form-item>
    </a-form>

  </a-modal>
</template>

<script lang="ts" setup>
import {defineEmits, defineExpose, defineProps, reactive, watch} from 'vue'
import {CheckPascalCaseAsync} from '@/utils/validate'

import {Form, message} from 'ant-design-vue'
import {updateTool, addTool} from "@/api/tool";
import {useSpaceStore} from '@/stores/space'
import {useRoute} from "vue-router";

const props = defineProps(['spaceId'])
const emits = defineEmits(['callback'])
const useForm = Form.useForm
const spaceStore = useSpaceStore()
const route = useRoute()


const state = reactive({
  open: false,
  form: {
    id: '',
    name: '',
    description: '',
    title: '',
    iconSrc: '',
    isPublic: false
  },
  title: '',
  rules: {
    title: [{required: true, message: '请输入智能体插件标题!'}],
    name: [{required: true, message: '请输入智能体插件名称!'}, {validator: CheckPascalCaseAsync, trigger: 'change'}],
    description: [{required: true, message: '输入智能体插件描述!'}],
    iconSrc: [{required: false, message: '请上传头像 用于在插件市场展示!'}],
  }
})

const {validate, validateInfos, resetFields} = useForm(state.form, state.rules)

watch(() => state.form.isPublic, (newValue) => {
  state.rules.iconSrc[0].required = newValue
}, {deep: true});

const defineData = {
  func: "/*\n* You can use any libraries imported in Flowise\n* You can use properties specified in Output Schema as variables. Ex: Property = userid, Variable = $userid\n* You can get default flow config: $flow.sessionId, $flow.chatId, $flow.chatflowId, $flow.input\n* You can get custom variables: $vars.<variable-name>\n* Must return a string value at the end of function\n*/",
  schema: '[]',
}

const handle = {
  open(data: any) {
    state.open = true
    if (data.name) {
      Object.keys(state.form).map((key: string) => {
        // @ts-ignore
        state.form[key] = data[key]
      })

      state.title = '编辑智能体插件'
    } else {
      state.title = '创建智能体插件'
    }
  },
  onSubmit() {
    validate()
        .then(async () => {
          const data = {...state.form, spaceId: props.spaceId} as any
          if (!data.id) {  // 新建
            delete data.id;
            delete data.spaceId
            await addTool({...data, ...defineData, spaceId: route.params.spaceId})
          } else {  // 修改
            await updateTool({...data, ...state.form})
          }
          message.success(`${state.title}成功！`)
          emits('callback')
          state.open = false
        })
        .catch()
  },
  afterClose() {
    Object.assign(state.form, {
      id: '',
      name: '',
      description: '',
      iconSrc: '',
      title: '',
      isPublic: false
    })
    resetFields()
  }
}

defineExpose({open: handle.open})
</script>
