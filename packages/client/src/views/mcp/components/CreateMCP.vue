<template>
    <a-modal v-model:open="open" :title="title" destroy-on-close @ok="onSubmit">
        <a-form :model="form" autocomplete="off" layout="vertical">
            <a-form-item label="标题" name="title" v-bind="validateInfos.title">
                <a-input v-model:value="form.title" placeholder="标题请尽量简洁并能体现核心功能，仅展示使用" />
            </a-form-item>

            <a-form-item label="名称" name="name" v-bind="validateInfos.name">
                <a-input v-model:value="form.name" placeholder="名称请遵循大写开头的英文驼峰命名规则" />
            </a-form-item>

            <a-form-item label="描述" name="description" v-bind="validateInfos.description">
                <a-textarea v-model:value="form.description" :rows="4" placeholder="MCP功能描述" />
            </a-form-item>

            <a-form-item label="创建方式" name="create_type" v-bind="validateInfos.createType">
                <a-radio-group v-model:value="form.createType" name="radioGroup">
                    <a-radio value="copilot">Copilot平台工具集</a-radio>
                    <a-radio value="external">自部署服务</a-radio>
                </a-radio-group>
            </a-form-item>

            <a-form-item label="支持的传输类型" name="transport_type" class="mt-[-10px]" v-bind="validateInfos.transportType">
                <a-checkbox-group
                    v-model:value="transportType"
                    :options="['SSE', 'Streamable HTTP']"
                    :disabled="form.createType === 'copilot'"
                />
            </a-form-item>

            <a-form-item label="工具集" class="mt-[-10px]" v-if="form.createType === 'copilot'" v-bind="validateInfos.tools">
                <a-select
                    v-model:value="tools"
                    mode="multiple"
                    style="width: 100%"
                    placeholder="选择工具集，支持工具中/英文名称和创建人搜索"
                    :options="options"
                >
                    <template #option="{ name, title, iconSrc, creator, description }">
                        <div class="flex items-center" :title="description">
                            <a-avatar class="mr-[10px]" :size="32" shape="square" :src="iconSrc || MCPIcon" />

                            <div>
                                <div class="text-[12px]">
                                    {{ name }} <span class="text-[11px]" style="color: var(--color-symbol-dc)">@{{ creator }}</span>
                                </div>

                                <div class="text-[11px]" style="color: var(--color-symbol-bold)">
                                    {{ title }}
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #tagRender="{ closable, onClose, option }">
                        <a-popover>
                            <template #content>
                                <div class="flex items-center w-[300px]">
                                    <a-avatar class="mr-[10px]" :size="32" shape="square" :src="option.iconSrc || MCPIcon" />

                                    <div>
                                        <div class="text-[12px]">
                                            {{ option.name }}
                                            <span class="text-[11px]" style="color: var(--color-symbol-dc)">@{{ option.creator }}</span>
                                        </div>

                                        <div class="text-[11px]" style="color: var(--color-symbol-bold)">
                                            {{ option.title }}
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-[5px] text-[12px]" style="color: var(--color-symbol-bold); margin-bottom: 8px">
                                    {{ option.description }}
                                </div>
                            </template>
                            <a-tag :closable="closable" @close="onClose" class="flex mt-[3px] mb-[3px] cursor-pointer pt-[5px] pb-[5px]">
                                <a-avatar class="mr-[5px]" :size="20" shape="square" :src="option.iconSrc || MCPIcon" />
                                <span class="text-[12px]">{{ option.name }}（{{ option.title }}）</span>
                            </a-tag>
                        </a-popover>
                    </template>
                </a-select>
            </a-form-item>

            <template v-else>
                <a-form-item v-if="transportType.includes('SSE')" label="SSE URL" class="mt-[-10px]" v-bind="validateInfos.externalURL">
                    <a-input v-model:value="externalSSEURL" placeholder="请输入SSE URL"> </a-input>
                </a-form-item>

                <a-form-item
                    v-if="transportType.includes('Streamable HTTP')"
                    label="Streamable HTTP URL"
                    class="mt-[-10px]"
                    v-bind="validateInfos.externalURL"
                >
                    <a-input v-model:value="externalStreamableHTTPURL" placeholder="请输入StreamableHTTP URL"> </a-input>
                </a-form-item>
            </template>

            <a-form-item label="应用场景" v-bind="validateInfos.scene">
                <a-select v-model:value="scene" mode="tags" style="width: 100%" placeholder="请输入应用场景"></a-select>
            </a-form-item>

            <a-form-item label="头像" name="iconSrc" v-bind="validateInfos.iconSrc">
                <img-upload v-model:avatar="form.iconSrc" />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { CheckPascalCaseAsync } from '@/utils/validate'
import { Form, message } from 'ant-design-vue'
import { updateMCP, addMCP } from '@/api/mcp'
import { getToolList } from '@/api/tool'
import MCPIcon from '@/assets/images/mcp.png'
import { type IMCPInfo } from '@/interface/mcp'

interface IToolSelectorOptions {
    value: string
    name: string
    title: string
    creator: string
    description: string
    iconSrc: string
}

interface IProps {
    spaceId: string
    data: IMCPInfo
}

const props = defineProps<IProps>()

const emit = defineEmits(['change'])
const useForm = Form.useForm
const tools = ref<string[]>([])
const externalSSEURL = ref('')
const externalStreamableHTTPURL = ref('')
const toolList = ref<Record<string, any>[]>([])
const options = ref<IToolSelectorOptions[]>([])
const transportType = ref(['SSE', 'Streamable HTTP'])
const scene = ref<string[]>([])
const open = ref(true)
const title = ref('')

const form = ref<Omit<IMCPInfo, 'creator' | 'ctime' | 'mtime'>>({
    id: '',
    name: '',
    description: '',
    title: '',
    iconSrc: '',
    createType: 'copilot',
    transportType: transportType.value.join(','),
    scene: '',
    source: '',
    spaceId: props.spaceId
})

watch(
    () => transportType.value,
    (v) => (form.value.transportType = v.join(','))
)

watch(
    () => form.value.createType,
    (type) => {
        if (type === 'copilot') {
            const toolset = tools.value
                .map((tool) => {
                    const { id } = JSON.parse(tool)
                    return id
                })
                .join(',')
            form.value.source = toolset
            transportType.value = ['SSE', 'Streamable HTTP']
            return
        }
        form.value.source = JSON.stringify({
            sse: transportType.value.includes('SSE') ? externalSSEURL.value : void 0,
            streamable: transportType.value.includes('Streamable HTTP') ? externalStreamableHTTPURL.value : void 0
        })
    }
)

watch(
    () => scene.value,
    (v) => {
        form.value.scene = v.join(',')
    }
)

const RULES = {
    title: [{ required: true, message: '请输入MCP服务标题!' }],
    name: [
        { required: true, message: '请输入MCP服务名称!' },
        { validator: CheckPascalCaseAsync, trigger: 'change' }
    ],
    description: [{ required: true, message: '请输入MCP服务描述!' }],
    createType: [{ required: true }],
    transportType: [{ required: true, message: '请选择传输类型!' }],
    scene: [{ required: true, message: '请输入应用场景!' }],
    iconSrc: [{ required: false, message: '请上传头像 用于在MCP服务市场展示!' }],
    tools: [{ required: true, message: '请选择工具集!' }],
    externalURL: [{ required: true, message: '请输入URL!' }]
}

const { validate, validateInfos } = useForm(form.value, RULES)

const onSubmit = async () => {
    await validate()

    const data = JSON.parse(JSON.stringify(form.value)) as IMCPInfo

    if (data.createType === 'copilot') {
        const toolset = tools.value
            .map((tool) => {
                const { id } = JSON.parse(tool)
                return id
            })
            .join(',')
        data.source = toolset
    } else {
        data.source = JSON.stringify({
            sse: transportType.value.includes('SSE') ? externalSSEURL.value : void 0,
            streamable: transportType.value.includes('Streamable HTTP') ? externalStreamableHTTPURL.value : void 0
        })
    }

    if (!data.id) {
        // 新建
        Reflect.deleteProperty(data, 'id')
        await addMCP(data)
    } else {
        // 修改
        await updateMCP(data)
    }

    open.value = false
    message.success(`${title.value}成功！`)
    emit('change')
}

const initData = async () => {
    const res = await getToolList({ pageSize: 100000 })
    toolList.value = res.data.rows
    options.value = res.data.rows.map((item: any) => {
        return {
            value: JSON.stringify({
                id: item.id,
                name: item.name,
                title: item.title,
                creator: item.creator
            }),
            name: item.name,
            title: item.title,
            creator: item.creator,
            description: item.description,
            iconSrc: item.iconSrc
        }
    })

    if (!Object.keys(props.data).length) {
        title.value = '创建MCP服务'
        return
    }

    Object.keys(form.value).map((key: string) => {
        // @ts-ignore
        Reflect.set(form.value, key, props.data[key])
    })

    title.value = '编辑MCP服务'

    transportType.value = props.data.transportType.split(',')

    if (props.data.createType === 'copilot') {
        // @ts-ignore
        tools.value = props.data.source.split(',').map((id) => {
            const tool = toolList.value.find((item: any) => item.id === id)
            if (!tool) return ''
            return JSON.stringify({
                id: tool.id,
                name: tool.name,
                title: tool.title,
                creator: tool.creator
            })
        })
    } else {
        const parseSource = JSON.parse(props.data.source)
        if (transportType.value.includes('SSE')) {
            externalSSEURL.value = parseSource.sse
        }
        if (transportType.value.includes('Streamable HTTP')) {
            externalStreamableHTTPURL.value = parseSource.streamable
        }
    }

    scene.value = props.data.scene.split(',')
}

void initData()
</script>
