.copilot-concise-table {
    .ant-table {
        background-color: var(--color-bg-1);
        .ant-table-thead > tr > th {
            background-color: var(--color-bg-1);
            color: var(--color-symbol-bold);
            border-color: var(--color-symbol-light);
            &::before {
                width: 0 !important;
            }
        }
        .ant-table-tbody tr td {
            background-color: var(--color-bg-1);
            color: var(--color-symbol-bold);
            border-color: var(--color-symbol-light) !important;
            &.ant-table-cell-row-hover {
                background-color: rgb(227, 227, 231);
            }
        }
        .ant-table-tbody > tr.ant-table-row:hover > td {
            background-color: rgb(227, 227, 231);
        }
        .ant-table-tbody > tr.ant-table-placeholder:hover > td {
            background-color: var(--color-bg-1);
        }
    }
}
.copilot-knowledge-table {
    .ant-table {
        border-radius: 8px;
        background-color: var(--color-bg-1);
        border: 1px solid var(--color-symbol-light);
        overflow: hidden;
        .ant-table-thead > tr > th {
            font-size: 12px;
            background-color: var(--color-bg-1);
            color: var(--color-symbol-medium);
            border-color: var(--color-symbol-light);
            &::before {
                width: 0 !important;
            }
        }
        .ant-table-tbody > tr > td {
            border-top: none !important;
        }
        .ant-table-tbody > tr.ant-table-row:hover > td {
            background-color: #ececf0;
        }
        .ant-table-tbody > tr.ant-table-placeholder:hover > td {
            background-color: var(--color-bg-1);
        }
    }
}
@media (min-width: 1920px) {
    .copilot-card-container {
        width: 1658px;
        margin: 0 auto;
    }
}
.copilot-card-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px; /* Adjust the gap between cards */
}
