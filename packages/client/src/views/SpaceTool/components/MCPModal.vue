<template>
    <div v-if="modelValue">
        <context-holder />
        <a-modal
            :visible="modelValue"
            :footer="null"
            :closable="true"
            :mask-closable="true"
            :width="500"
            class="mcp-modal"
            @cancel="handleCancel"
        >
            <div class="flex flex-col p-1">
                <div class="flex">
                    <div class="flex items-center">
                        <a-avatar class="mr-[10px]" :size="68" shape="square" :src="props.iconSrc" />
                    </div>
                    <div>
                        <div class="text-[16px] font-bold">{{ props.title }}</div>
                        <a-typography-text type="secondary" class="mr-1" style="color: var(--color-symbol-bold)">{{
                            props.name
                        }}</a-typography-text>
                        <div>
                            <a-typography-text type="secondary" style="color: var(--color-symbol-dc)"
                                >@{{ props.creator }}</a-typography-text
                            >
                        </div>
                    </div>
                </div>

                <div class="mt-[10px]" style="color: var(--color-symbol-bold)">
                    {{ props.description }}
                </div>

                <a-tabs>
                    <a-tab-pane key="1">
                        <template #tab>
                            <span>SSE URL</span>
                        </template>
                        <a-card size="small" class="w-[100%]">
                            <template #title>
                                <div class="flex justify-between items-center">
                                    <div class="text-[13px]">HTTP URL</div>
                                    <a-button size="small" type="link" :icon="h(CopyOutlined)" @click="copyMCPUrl('sse')">复制</a-button>
                                </div>
                            </template>
                            <a :href="sseUrl" target="_blank">{{ sseUrl }}</a>
                        </a-card>
                    </a-tab-pane>

                    <a-tab-pane key="2">
                        <template #tab>
                            <span>StreamableHTTP URL</span>
                        </template>
                        <a-card size="small" class="w-[100%]">
                            <template #title>
                                <div class="flex justify-between items-center">
                                    <div class="text-[13px]">HTTP URL</div>
                                    <a-button size="small" type="link" :icon="h(CopyOutlined)" @click="copyMCPUrl('streamable-http')"
                                        >复制</a-button
                                    >
                                </div>
                            </template>
                            <a :href="streamableHTTPUrl" target="_blank">{{ streamableHTTPUrl }}</a>
                        </a-card>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, h } from 'vue'
import { message } from 'ant-design-vue'
import { mcpSign } from '@/api/mcp'
import { CopyOutlined } from '@ant-design/icons-vue'

interface IProp {
    modelValue: boolean
    name: string
    title: string
    creator: string
    description: string
    iconSrc: string
    tools: ITool[]
    id: string
}

interface ITool {
    name: string
    title: string
    description: string
}

const props = defineProps<IProp>()

const emit = defineEmits(['update:modelValue', 'cancel'])

const [messageApi, contextHolder] = message.useMessage()

const mcpSignData = ref({
    sign: '',
    username: '',
    host: ''
})

const handleCancel = () => {
    emit('update:modelValue', false)
    emit('cancel')
}

function copyToClipboard(text: string, cb: (successful: boolean) => void) {
    // 检查浏览器是否支持 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        // 使用 navigator.clipboard.writeText 将文本复制到剪贴板
        navigator.clipboard.writeText(text).then(
            function () {
                cb(true)
            },
            function () {
                cb(false)
            }
        )
    } else {
        // 如果 Clipboard API 不可用，则使用回退方法（例如，创建一个临时的文本区域元素）
        let textArea = document.createElement('textarea')
        textArea.value = text
        // 使文本区域不可见
        textArea.style.position = 'fixed' // 防止滚动
        textArea.style.top = '0'
        textArea.style.left = '0'
        textArea.style.width = '2em'
        textArea.style.height = '2em'
        textArea.style.padding = '0'
        textArea.style.border = 'none'
        textArea.style.outline = 'none'
        textArea.style.boxShadow = 'none'
        textArea.style.background = 'transparent'

        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        try {
            let successful = document.execCommand('copy')
            cb(successful)
        } catch (err) {
            cb(false)
        }

        document.body.removeChild(textArea)
    }
}

const sseUrl = computed(
    () =>
        `${'http:'}//${mcpSignData.value.host}/api/mcp/sse/function-tool/${props.id}?sign=${mcpSignData.value.sign}&username=${
            mcpSignData.value.username
        }`
)

const streamableHTTPUrl = computed(
    () =>
        `${location.protocol}//${mcpSignData.value.host}/api/mcp/function-tool/${props.id}?sign=${mcpSignData.value.sign}&username=${mcpSignData.value.username}`
)

function copyMCPUrl(type: 'sse' | 'streamable-http') {
    copyToClipboard(type === 'sse' ? sseUrl.value : streamableHTTPUrl.value, (successful) => {
        if (successful) {
            messageApi.success('已复制到粘贴板，sign具有个人身份权限，请不要随意分享给他人，本次sign在dashboard重登后失效')
        } else {
            messageApi.error('获取失败，请重试')
        }
    })
}

mcpSign().then((res) => {
    mcpSignData.value = res.data
})
</script>

<style scoped>
.mcp-modal :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
}

.mcp-modal :deep(.ant-modal-close) {
    color: #666;
}

.tool-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #1890ff;
    transition: all 0.3s;
}

.tool-icon:hover {
    background-color: #e6f7ff;
    cursor: pointer;
}
</style>
