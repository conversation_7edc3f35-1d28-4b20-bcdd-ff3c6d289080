<template>
    <a-modal v-model:open="open" title="编辑" :after-close="handleAfterClose" :mask-closablel="false" destroy-on-close>
        <a-form
            :model="formData"
            :rules="rules"
            :label-col="{ style: { width: '80px' } }"
            :wrapper-col="{ span: 18 }"
            class="pt-[20px]"
            @finish="handleFinish"
            autocomplete="off"
        >
            <a-form-item label="名称" name="name">
                <a-input v-model:value="formData.name" />
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-textarea v-model:value="formData.description" />
            </a-form-item>
            <a-form-item label="头像" name="avatar">
                <img-upload v-model:avatar="formData.avatar" />
            </a-form-item>
            <a-form-item label="分类" name="category">
                <a-input v-model:value="formData.category" />
            </a-form-item>
            <a-form-item label="Bot市场" name="isPublic">
                <a-radio-group v-model:value="formData.isPublic" name="public">
                    <a-radio :value="!0">上架</a-radio>
                    <a-radio :value="!1">下架</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item class="justify-center flex">
                <a-space>
                    <a-button @click="open = false">取消</a-button>
                    <a-button type="primary" html-type="submit">提交</a-button>
                </a-space>
            </a-form-item>
        </a-form>

        <template #footer></template>
    </a-modal>
</template>
<script setup lang="ts">
import { ref, defineEmits, defineExpose } from 'vue'
import { updateChatFlowInfo } from '@/api/space'
import { message } from 'ant-design-vue'
import ImgUpload from '@/components/upload/ImgUpload.vue'

const emits = defineEmits(['callback'])
const open = ref(false)

const formData = ref({ name: '', description: '', avatar: '', category: '', isPublic: false })

const dataTemp = ref<any>({})

const rules: any = {
    name: [{ required: true, message: '请填写名称' }],
    avatar: [{ required: true, message: '请上传头像' }]
}

const handleFinish = async (val: any) => {
    await updateChatFlowInfo({ id: dataTemp.value.id, ...val })

    message.success('编辑成功！')

    emits('callback')
    open.value = false
}
const handleOpen = (data: any) => {
    dataTemp.value = data
    Object.keys(formData.value).map((key) => {
        // @ts-ignore
        formData.value[key] = data[key]
    })
    open.value = true
}

const handleAfterClose = () => {
    formData.value = { name: '', description: '', avatar: '', category: '', isPublic: false }
}

defineExpose({ open: handleOpen })
</script>
