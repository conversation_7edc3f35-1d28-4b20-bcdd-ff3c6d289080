import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js'
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js'

export async function connectMcp(url: string, transportType: 'sse' | 'streamable-http', customHeaders: Record<string, any> = {}) {
    if (transportType === 'sse') {
        url = url.replace(/^http:/, location.protocol)
    }

    const client = new Client(
        {
            name: 'copilot-client',
            version: '1.0.0'
        },
        {
            capabilities: {
                sampling: {},
                roots: {
                    listChanged: true
                }
            }
        }
    )

    const transportOptions = {
        eventSourceInit: {
            fetch: (url: string | URL | globalThis.Request, init: RequestInit | undefined) =>
                fetch(url, { ...init, headers: customHeaders })
        },
        requestInit: {
            headers: customHeaders
        },
        reconnectionOptions: {
            maxReconnectionDelay: 30000,
            initialReconnectionDelay: 1000,
            reconnectionDelayGrowFactor: 1.5,
            maxRetries: 2
        }
    }

    const transport =
        transportType === 'sse'
            ? new SSEClientTransport(new URL(url), {
                  ...transportOptions
              })
            : new StreamableHTTPClientTransport(new URL(url), {
                  sessionId: undefined,
                  ...transportOptions
              })

    await client.connect(transport)

    return client
}
