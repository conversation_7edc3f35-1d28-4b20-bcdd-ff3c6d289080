import { ChatCoze } from './core'
import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'

class Coze_ChatModels implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    description: string
    baseClasses: string[]
    credential: INodeParams
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'Coze'
        this.name = 'coze'
        this.version = 1.0
        this.type = 'Coze'
        this.icon = 'coze.svg'
        this.category = 'Chat Models'
        this.description = 'Coze Api'
        this.tags = ['bilibili']
        this.baseClasses = [this.type, 'BaseChatModel', ...getBaseClasses(ChatCoze)]
        this.inputs = [
            {
                label: 'Bot Id',
                name: 'botId',
                type: 'string'
            },
            {
                label: 'API Key',
                name: 'api<PERSON>ey',
                type: 'string'
            },
            {
                label: 'Stream',
                name: 'streaming',
                description: '开启ws流模式',
                type: 'boolean'
            },
            {
                label: 'user',
                name: 'user',
                type: 'string',
                description: '标识当前与 Bot 交互的用户',
                default: 'api',
                optional: true
            }
        ]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const apiKey = nodeData.inputs?.apiKey as string
        const botId = nodeData.inputs?.botId as string
        const streaming = nodeData.inputs?.streaming as boolean
        const user = (nodeData.inputs?.user as string) || 'api'

        const model = new ChatCoze({
            apiKey,
            botId,
            streaming,
            user
        })

        return model
    }
}

module.exports = { nodeClass: Coze_ChatModels }
