<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { watch } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'

const route = useRoute()

watch(
    () => route.path,
    () => {
        window.dispatchEvent(new CustomEvent('consoleUrlChange'))
    }
)

const theme = {
    token: {
        fontFamily:
            'SF Pro Display,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif',
        colorBgLayout: '#f4f4f6'
    }
}
</script>

<template>
    <a-config-provider :theme="theme" :locale="zhCN">
        <RouterView />
    </a-config-provider>
</template>
