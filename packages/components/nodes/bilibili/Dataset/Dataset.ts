import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses, getDataset } from '../../../src/utils'
import { ChatDataset } from './core'

const datasetPrompt = `You are the world's trusted expert Q&A system.Always answer queries using the provided contextual information rather than prior knowledge.Some rules to follow:
1. All output answers are in Chinese. do not use English or other languages.
2. If there are no suitable results, return empty.

User: Contextual information is as follows.
---------------------
{context_str}
---------------------
Given the context information and not prior knowledge, answer the query.
Query: {query_str}
Answer: `

class Dataset_ChatModels implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    description: string
    baseClasses: string[]
    tags: string[]
    credential: INodeParams
    inputs: INodeParams[]

    constructor() {
        this.label = '知识库DatasetModel'
        this.name = 'dataset'
        this.version = 2.0
        this.type = 'Dataset'
        this.icon = 'dataset.svg'
        this.category = 'Chat Models'
        this.description = '内部知识库组件'
        this.baseClasses = [this.type, ...getBaseClasses(ChatDataset)]
        this.tags = ['bilibili']
        this.inputs = [
            {
                label: '知识库ID',
                name: 'datasetId',
                type: 'string',
                description: '前往知识库列表页粘贴id至此处'
            },
            {
                label: '模型',
                name: 'model',
                type: 'options',
                description: '内部敏感信息请使用B站自研Index长模型',
                options: [
                    {
                        label: 'GPT-3.5-Turbo',
                        name: 'gpt-3.5-turbo-0125'
                    },
                    {
                        label: 'Bili-Index-Long',
                        name: 'index-2.0-long-text'
                    },
                    {
                        label: 'Claude-3',
                        name: 'claude-3-haiku-20240307'
                    },
                    {
                        label: 'GPT-4-Turbo',
                        name: 'gpt-4-turbo-preview'
                    },
                    {
                        label: 'GPT-4-128K',
                        name: 'gpt-4-128k'
                    },
                    {
                        label: 'GPT-3',
                        name: 'gpt-3'
                    },
                    {
                        label: 'GPT-4-PROXY',
                        name: 'gpt-4-proxy'
                    },
                    {
                        label: 'GPT-3-PROXY',
                        name: 'gpt-3-proxy'
                    },
                    {
                        label: 'ollama:llama3:8b-instruct-fp16',
                        name: 'ollama:llama3:8b-instruct-fp16'
                    }
                ],
                default: 'gpt-3.5-turbo-0125'
            },
            {
                label: '自定义Prompt',
                name: 'prompt',
                type: 'string',
                rows: 4,
                description: '自定义Prompt上下文',
                additionalParams: true,
                optional: true,
                default: datasetPrompt,
                placeholder: datasetPrompt
            },
            {
                label: 'Bot id',
                name: 'botId',
                type: 'string',
                description: '如不了解,默认请填1',
                additionalParams: true,
                optional: true,
                default: '1'
            }
        ]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const model = (nodeData.inputs?.model as string) || 'gpt-3.5-turbo-0125'
        const botId = (nodeData.inputs?.botId as string) || '1'
        const datasetId = nodeData.inputs?.datasetId as string
        const prompt = (nodeData.inputs?.prompt as string) || datasetPrompt

        // 方便测试,仅prod验证dataset权限
        if (process.env.DEPLOY_ENV === 'prod') {
            await getDataset(datasetId, options)
        }

        const chatModel = new ChatDataset({
            datasetId,
            maxRetries: 0,
            model,
            botId,
            prompt
        })
        return chatModel
    }
}

module.exports = { nodeClass: Dataset_ChatModels }
