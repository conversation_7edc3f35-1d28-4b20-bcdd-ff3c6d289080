import { getBaseClasses } from '../../../src'
import { SaverOptions } from './interface'
import { ICommonObject, IDatabaseEntity, INode, INodeData, INodeParams } from '../../../src'
import { DataSource } from 'typeorm'
import { RDSSaver } from './rdsSaver'

class RDSAgentMemory_Memory implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    badge: string
    baseClasses: string[]
    inputs: INodeParams[]
    credential: INodeParams

    constructor() {
        this.label = 'RDS Agent Memory'
        this.name = 'rdsAgentMemory'
        this.version = 1.0
        this.type = 'AgentMemory'
        this.icon = 'cloud.svg'
        this.category = 'Memory'
        this.description = '使用Cloud平台上的RDS数据库来记录Agent flow里的历史对话、系统状态、元数据'
        this.baseClasses = [this.type, ...getBaseClasses(RDSSaver)]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const databaseEntities = options.databaseEntities as IDatabaseEntity
        const chatflowid = options.chatflowid as string
        const appDataSource = options.appDataSource as DataSource
        const threadId = options.sessionId || options.chatId

        const args: SaverOptions = {
            threadId,
            appDataSource,
            databaseEntities,
            chatflowid
        }
        const recordManager = new RDSSaver(args)
        return recordManager
    }
}

module.exports = { nodeClass: RDSAgentMemory_Memory }
