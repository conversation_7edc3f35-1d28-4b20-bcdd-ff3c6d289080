import { BaseChatModel, type BaseChatModelParams } from '@langchain/core/language_models/chat_models'
import { AIMessage, AIMessageChunk, BaseMessage, ChatMessage, ChatMessageChunk, HumanMessageChunk } from '@langchain/core/messages'
import { ChatGenerationChunk, ChatResult } from '@langchain/core/outputs'
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager'
import { Logger } from 'winston'
import { ConsoleCallbackHandler } from '../../../src/handler'
import { SOURCE_DOCUMENTS_PREFIX } from '../../../src/agents'
import { NewTokenIndices } from '@langchain/core/callbacks/base'

/**
 * 消息体里的角色信息
 */
export type ChatCozeMessageRole = 'assistant' | 'user'

/**
 * 提交时chat_history历史消息字段
 */
interface ChatCozeMessage {
    role: ChatCozeMessageRole
    content: string
    content_type: string
}

/**
 * 请求时的接口参数
 */
interface ChatCompletionRequest {
    bot_id: string
    conversation_id?: string // 标识对话发生在哪一次会话中，使用方自行维护此字段。对于不需要区分 conversation 的场合（仅是一问一答），可不传该参数，系统自动生成。
    user?: string // 标识当前与 Bot 交互的用户
    query: string
    chat_history?: ChatCozeMessage[]
    stream?: boolean
    custom_variables?: Object //Bot 中定义的变量信息。key 是变量名，value 是变量值。
}

/**
 *  Chat 接口返回时的基本消息结构
 */
interface ChatCompletionMessage {
    role: ChatCozeMessageRole
    type: 'answer' | 'function_call' | 'tool_response' | 'follow_up'
    content: string
    content_type: string
}

/**
 * 返回参数
 */
interface ChatCompletionResponse {
    code: number
    messages: ChatCompletionMessage[]
    msg: string
}

/**
 * Coze class的输入.
 */
declare interface ChatCozeInput {
    streaming?: boolean // 暂不支持
    prefixMessages?: ChatCozeMessage[]
    botId: string
    apiKey: string
    user?: string
    conversationId?: string
    customVariables?: Object
}

/**
 * Function that extracts the custom role of a generic chat message.
 * @param message Chat message from which to extract the custom role.
 * @returns The custom role of the chat message.
 */
function extractGenericMessageCustomRole(message: ChatMessage) {
    if (['assistant', 'user'].includes(message.role) === false) {
        console.warn(`Unknown message role: ${message.role}`)
    }

    return message.role as ChatCozeMessageRole
}

/**
 * Function that converts a base message to a Bili100K message role.
 * @param message Base message to convert.
 * @returns The Bili100K message role.
 */
function messageToChatInfoRole(message: BaseMessage): ChatCozeMessageRole {
    const type = message._getType()
    switch (type) {
        case 'ai':
            return 'assistant'
        case 'human':
            return 'user'
        case 'system':
            return 'assistant'
        case 'function':
            throw new Error('Function messages not supported')
        case 'generic': {
            if (!ChatMessage.isInstance(message)) throw new Error('Invalid generic chat message')
            return extractGenericMessageCustomRole(message)
        }
        default:
            throw new Error(`Unknown message type: ${type}`)
    }
}

function _convertDeltaToMessageChunk(delta: Record<string, any>, defaultRole?: ChatCozeMessageRole) {
    const role = delta.role ?? defaultRole
    const content = delta.content ?? ''
    if (role === 'assistant') {
        return new AIMessageChunk({ content })
    } else {
        return new ChatMessageChunk({ content, role })
    }
}

function convertMessagesToCozeParams(messages: BaseMessage[]) {
    return messages.map((message) => ({
        role: messageToChatInfoRole(message),
        content: message.content as string,
        content_type: 'text'
    }))
}

export class ChatCoze extends BaseChatModel implements ChatCozeInput {
    static lc_name() {
        return 'Coze'
    }

    get callKeys(): string[] {
        return ['stop', 'signal', 'options']
    }

    get lc_secrets(): { [key: string]: string } | undefined {
        return {
            apikey: 'API_KEY'
        }
    }

    get lc_aliases(): { [key: string]: string } | undefined {
        return undefined
    }

    lc_serializable = true

    apiUrl = 'https://api.coze.com/open_api/v2/chat'

    streaming = false

    prefixMessages?: ChatCozeMessage[]

    botId: string

    apiKey: string

    user?: string

    conversationId?: string

    customVariables?: Object

    constructor(fields?: Partial<ChatCozeInput> & BaseChatModelParams) {
        super(fields ?? {})

        this.botId = fields?.botId ?? this.botId
        if (!this.botId) {
            throw new Error('botId not found')
        }

        this.apiKey = fields?.apiKey ?? this.apiKey
        if (!this.apiKey) {
            throw new Error('apiKey not found')
        }

        this.streaming = fields?.streaming ?? this.streaming
        this.prefixMessages = fields?.prefixMessages ?? this.prefixMessages

        this.user = fields?.user ?? this.user
        this.conversationId = fields?.conversationId ?? this.conversationId
        this.customVariables = fields?.customVariables ?? this.customVariables
    }

    /**
     * Get the parameters used to invoke the model
     */
    invocationParams(): Omit<ChatCompletionRequest, 'query'> {
        return {
            stream: this.streaming,
            bot_id: this.botId,
            user: this.user,
            conversation_id: this.conversationId,
            custom_variables: this.customVariables
        }
    }

    /**
     * Get the identifying parameters for the model
     */
    identifyingParams() {
        return {
            ...this.invocationParams()
        }
    }

    /**
     * 使LLm能够支持stream输出
     * @param messages
     * @param options
     * @param runManager
     */
    async *_streamResponseChunks(
        messages: BaseMessage[],
        options: this['ParsedCallOptions'],
        runManager?: CallbackManagerForLLMRun
    ): AsyncGenerator<ChatGenerationChunk> {
        const messagesMapped: ChatCozeMessage[] = convertMessagesToCozeParams(messages)
        const query = messagesMapped.pop()?.content
        if (!query) throw new Error('ChatCoze query不能为空')

        const params = {
            ...this.invocationParams(),
            query,
            chat_history: messagesMapped
        }

        let defaultRole: ChatCozeMessageRole | undefined
        const streamIterable = await this.completionWithRetry(params, true, options?.signal)
        let toolResponse: any = []
        for await (const data of streamIterable) {
            const message = data?.message
            if (!message) {
                continue
            }

            const newTokenIndices = {
                prompt: 0,
                completion: data.index ?? 0,
                usage: (data as any).usage ?? (data as any).usage,
                messageType: message.type
            }

            const chunk = _convertDeltaToMessageChunk(message, defaultRole)

            defaultRole = message.role ?? defaultRole

            if (typeof chunk.content !== 'string') {
                console.log('[WARNING]: Received non-string content from Coze. This is currently not supported.')
                continue
            }

            // 在这里处理function_call和tool_response
            if (message.type === 'function_call') {
                const content = JSON.parse(message.content)
                void runManager?.handleLLMNewToken(`<mark class="orange">${content.name}</mark> `)
                continue
            }

            if (message.type === 'tool_response') {
                // 把tool_response内容保存到sourceDocuments
                const content = JSON.parse(message.content)
                const sourceDocuments = content?.data?.organic_results?.map((item: Record<string, any>) => ({
                    pageContent: item.snippet + '\n\n' + item.link
                }))

                if (sourceDocuments) toolResponse.push(...sourceDocuments)

                continue
            }

            if (message.type === 'answer') {
                // 正常的AI回复
                const generationChunk = new ChatGenerationChunk({
                    message: chunk,
                    text: chunk.content,
                    generationInfo: newTokenIndices
                })

                yield generationChunk

                void runManager?.handleLLMNewToken(generationChunk.text ?? '', newTokenIndices, undefined, undefined, undefined, {
                    chunk: generationChunk
                })
            }

            if (message.type === 'verbose' && toolResponse.length > 0) {
                // 消息结束,处理sourceDocuments
                const text = SOURCE_DOCUMENTS_PREFIX + JSON.stringify(toolResponse)
                const generationChunk = new ChatGenerationChunk({
                    message: chunk,
                    text,
                    generationInfo: newTokenIndices
                })

                yield generationChunk
            }
        }
        if (options.signal?.aborted) {
            throw new Error('AbortError')
        }
    }

    /** @ignore */
    async _generate(
        messages: BaseMessage[],
        options: this['ParsedCallOptions'],
        runManager?: CallbackManagerForLLMRun
    ): Promise<ChatResult> {
        const params = this.invocationParams()

        const messagesMapped: ChatCozeMessage[] = messages.map((message) => ({
            role: messageToChatInfoRole(message),
            content: message.content as string,
            content_type: 'text'
        }))

        const query = messagesMapped.pop()?.content
        if (!query) throw new Error('ChatCoze query不能为空')

        /**
         * 等待streaming支持后可开启
         */
        if (params.stream) {
            const stream = this._streamResponseChunks(messages, options, runManager)
            const finalChunks: Record<number, ChatGenerationChunk> = {}
            for await (const chunk of stream) {
                const index = (chunk.generationInfo as NewTokenIndices)?.completion ?? 0
                if (finalChunks[index] === undefined) {
                    finalChunks[index] = chunk
                } else {
                    finalChunks[index] = finalChunks[index].concat(chunk)
                }
            }

            const generations = Object.entries(finalChunks)
                .sort(([aKey], [bKey]) => parseInt(aKey, 10) - parseInt(bKey, 10))
                .map(([_, value]) => value)

            // 重新创建一个新的generations, 把tool_response里的text合并到answer里
            let answerIndex = -1
            let toolResponseIndex = -1
            generations.forEach((generation, index) => {
                if (generation.generationInfo?.messageType === 'answer') {
                    answerIndex = index
                } else if (generation.generationInfo?.messageType === 'verbose') {
                    // 消息结束时的messageType,此步骤会处理sourceDocuments
                    toolResponseIndex = index
                }
            })

            if (answerIndex !== -1 && toolResponseIndex !== -1) {
                const text = generations[answerIndex].text + generations[toolResponseIndex].text
                return {
                    generations: [
                        {
                            text,
                            message: new AIMessage(text)
                        }
                    ]
                }
            }

            return { generations }
        } else {
            const data = await this.completionWithRetry(
                {
                    ...params,
                    query,
                    chat_history: messagesMapped
                },
                false,
                options?.signal
            )

            if (data?.code !== 0) {
                throw new Error(data?.msg)
            }

            let text = (data?.messages as ChatCompletionMessage[]).find((message) => message.type === 'answer')?.content || ''
            let sourceDocuments = (data?.messages as ChatCompletionMessage[]).find((message) => message.type === 'tool_response')?.content
            if (sourceDocuments) {
                const docs = JSON.parse(sourceDocuments).data?.organic_results.map((doc: any) => ({
                    pageContent: doc.snippet + '\n' + doc.link
                }))
                text = text + SOURCE_DOCUMENTS_PREFIX + JSON.stringify(docs)
            }

            return {
                generations: [
                    {
                        text,
                        message: new AIMessage(text)
                    }
                ]
            }
        }
    }

    /** @ignore */
    async completionWithRetry(
        request: ChatCompletionRequest,
        stream: boolean,
        signal?: AbortSignal,
        onmessage?: (event: MessageEvent) => void
    ) {
        const makeCompletionRequest = async () => {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    // ...(stream ? { Accept: 'text/event-stream' } : {}),
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${this.apiKey}`,
                    Connection: 'Keep-alive',
                    Accept: '*/*'
                },
                body: JSON.stringify(request),
                signal
            })

            if (response.status !== 200) {
                throw new Error(`Coze error：${response.status} ${response.statusText}`)
            }

            if (!stream) {
                return response.json()
            }

            if (response.body) {
                const reader = response.body.getReader()
                const decoder = new TextDecoder('utf-8')

                return {
                    async *[Symbol.asyncIterator]() {
                        let data = ''
                        while (true) {
                            const { done, value } = await reader.read()
                            if (done) break
                            data += decoder.decode(value, { stream: true })

                            let newlineIndex
                            while ((newlineIndex = data.indexOf('\n')) !== -1) {
                                const line = data.slice(0, newlineIndex).trim()
                                data = data.slice(newlineIndex + 1)
                                if (line) {
                                    yield JSON.parse(line.slice(5)) // Remove 'data:' prefix
                                }
                            }
                        }
                    }
                }
            }
        }

        return this.caller.call(makeCompletionRequest)
    }

    _llmType() {
        return 'chat_coze'
    }

    /** @ignore */
    _combineLLMOutput() {
        return []
    }
}
