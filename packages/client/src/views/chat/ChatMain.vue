<template>
    <div class="chat">
        <ChatWindow
            :avatar="state.chatflowSpecific.avatar"
            :chatflowid="state.chatflowid"
            :name="state.chatflowSpecific.name"
            :nodes="state.nodesImg"
            :mentionList="state.mentionList"
            :description="state.chatflowSpecific.description"
            :creator="state.chatflowSpecific.creator"
            :createdDate="state.chatflowSpecific.createdDate"
            :apiHost="state.apiHost"
            :loading="loading"
            :type="state.chatflowSpecific.type"
            :initialMessage="initialMessage"
        ></ChatWindow>
    </div>
</template>
<script lang="ts" setup>
import { watch, reactive, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import ChatWindow from '@/components/chat/ChatWindow.vue'
import type { ChatNode } from '@/components/chat/ChatWindow.vue'
import { getSpecificChatflow, getToolsInfo, type ChatflowSpecific } from '@/api/chat'
import { RUN_ENV } from '@/constant/env'

const route = useRoute()
const state = reactive({
    chatflowid: route.params.id as string,
    chatflowSpecific: {} as ChatflowSpecific,
    nodesImg: [] as ChatNode[],
    mentionList: [] as ChatNode[],
    apiHost: `//${window.location.host}`
})

const search = new URLSearchParams(location.search)
const initialMessage = ref(search.get('query') || '')

const loading = ref(false)

watch(
    () => {
        return route.params.id as string
    },
    (newValue) => {
        state.chatflowid = newValue
        getChatflowsInfo()
    }
)

onMounted(() => {
    getChatflowsInfo()
})
const getChatflowsInfo = async () => {
    loading.value = false
    try {
        const data = await getSpecificChatflow(state.chatflowid)
        data.createdDate = dayjs(data.createdDate).format('YYYY-MM-DD')
        state.chatflowSpecific = data
        const flowData = JSON.parse(data.flowData)

        const images: Record<string, string> = {}
        const edges_ids = flowData.edges.map((item: any) => item.source)

        const baseURL = RUN_ENV === 'uat' ? `//uat-copilot.bilibili.co` : `//copilot.bilibili.co`

        const customToolIds = [] as { id: string; avatar: string }[]

        for (let j = 0; j < flowData.nodes.length; j += 1) {
            const name = flowData.nodes[j].data.name
            const imageSrc = `${baseURL}/api/v1/node-icon/${name}`
            if (!images[name]) {
                images[name] = imageSrc
                state.nodesImg.push({
                    name: name,
                    src: imageSrc
                })
            }
            const item = flowData.nodes[j].data
            // 获取plugins

            if (edges_ids.includes(item.id) && item.category === 'Tools' && data.type !== 'MULTIAGENT') {
                // BiliTool 单独从接口获取name和description
                if (item.type === 'BiliTool') {
                    customToolIds.push({
                        id: item.inputs.selectedTool,
                        avatar: item.inputs?.icon || imageSrc
                    })
                } else {
                    state.mentionList.push({
                        id: item.id,
                        name: item.inputs?.name || item.name,
                        avatar: item.inputs?.icon || imageSrc,
                        description: item.inputs?.description
                    })
                }
            }
        }
        if (customToolIds.length) {
            const ids = customToolIds.map((item) => item.id)
            const { data } = await getToolsInfo(ids)
            data.forEach((item: any) => {
                state.mentionList.push({
                    id: item.id,
                    name: item.name,
                    avatar: item.iconSrc || customToolIds.find((tool) => tool.id === item.id)?.avatar,
                    description: item.description
                })
            })
        }

        loading.value = true
    } catch (error) {
        console.error(error)
    }
}
</script>
<style>
.chat {
    height: 100%;
}
</style>
