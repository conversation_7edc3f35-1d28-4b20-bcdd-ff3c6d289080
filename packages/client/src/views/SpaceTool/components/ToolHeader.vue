<template>
  <div style="border-bottom: 1px solid #e5e6eb" class="text-center text-black h-20 px-6 py-4 bg-[#f4f4f6] z-1 flex w-full items-center justify-between box-shadow-[0_2px_2px_rgba(29,28,35,0.04),0_0_2px_rgba(29,28,35,0.18)]">
    <div class="m-0 flex items-center">
      <a-button type="link" :href="`/space/${route.params.spaceId}/tool`">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" focusable="false" aria-hidden="true"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.2782 4.23933C16.864 4.82511 16.864 5.77486 16.2782 6.36065L10.6213 12.0175L16.2782 17.6744C16.864 18.2601 16.864 19.2099 16.2782 19.7957C15.6924 20.3815 14.7426 20.3815 14.1569 19.7957L7.43934 13.0782C6.85355 12.4924 6.85355 11.5426 7.43934 10.9568L14.1569 4.23933C14.7426 3.65354 15.6924 3.65354 16.2782 4.23933Z" fill="#000"></path></svg>
      </a-button>
      <img v-if="detail && detail.iconSrc" class="rounded-md" width="48" height="48" :src="detail.iconSrc" alt="icon"/>
      <img v-else class="rounded-md" width="48" height="48" :src="toolLogo" alt="icon"/>

      <div class="flex flex-col text-left pl-4" >
        <h1 class="m-0 text-[18px]">{{ detail?.title || '--'}}
        </h1>
        <span v-if="detail?.isPublic" class="text-[#616161]">
          <CheckCircleOutlined  class="text-[12px] text-[#87d068] pr-2"/>
          <span class="text-[12px]">已上架 &nbsp;|&nbsp; {{detail?.name}}</span>
        </span>
        <span v-else class="text-[#616161]">
          <MehOutlined   class="pr-2 text-[12px] text-[#f50]"/>
          <span class="text-[12px]">未上架 &nbsp;|&nbsp; {{detail?.name}}</span>
        </span>
      </div>
    </div>
    <div>
      <a-button  @click="handleEdit" class="mr-5">
        <template #icon>
          <FormOutlined/>
        </template>
      </a-button>
      <a-button type="primary" @click="handleSave">
        <template #icon>
          <SaveOutlined />
        </template>
        保存
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import toolLogo from '@/assets/images/tool.png'

import {defineProps, toRefs} from 'vue';
import {CheckCircleOutlined,MehOutlined, SaveOutlined, FormOutlined} from '@ant-design/icons-vue';
import {useRoute} from "vue-router";

const props = defineProps<{ detail: any }>();
const {detail} = toRefs(props);
const route = useRoute()

const emit = defineEmits(['save','edit']);

const handleEdit = () => {
  emit('edit');
}

const handleSave = () => {
  emit('save');
}
</script>

<style>

</style>