import { OpenAIChatInput } from '@langchain/openai'
import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'
import { ChatAiGateway } from './core'

class AiGateway_ChatModels implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    description: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'AI Gateway'
        this.name = 'aiGateway'
        this.version = 2.0
        this.type = 'AiGateway'
        this.icon = 'ai.png'
        this.category = 'Chat Models'
        this.tags = ['bilibili']
        this.description = 'AI开放平台上的第三方大语言模型，如gpt-4o等'
        this.baseClasses = [this.type, 'BaseChatModel', ...getBaseClasses(ChatAiGateway)]
        this.inputs = [
            {
                label: 'Source',
                name: 'source',
                type: 'string',
                description: 'query参数中的source字段，如openai',
                placeholder: 'openai',
                default: 'openai'
            },
            {
                label: 'Model Name',
                name: 'modelName',
                type: 'string',
                description: 'query参数中的model字段，如gpt-4o',
                placeholder: 'gpt-4o',
                default: 'gpt-4o'
            },
            {
                label: 'Base Path',
                name: 'basePath',
                type: 'string',
                placeholder: 'http://ai-gateway.bilibili.co/v1',
                default: 'http://ai-gateway.bilibili.co/v1',
                additionalParams: true
            },
            {
                label: 'Secret Key',
                name: 'secretKey',
                type: 'string',
                additionalParams: true
            },
            {
                label: 'Access Key',
                name: 'accessKey',
                type: 'string',
                additionalParams: true
            },
            {
                label: 'Stream',
                name: 'streaming',
                description: '开启Event Stream',
                type: 'boolean',
                additionalParams: true
            },
            {
                label: 'Temperature',
                name: 'temperature',
                type: 'number',
                step: 0.1,
                default: 0.7,
                optional: true,
                additionalParams: true
            },
            {
                label: 'Max Tokens',
                name: 'maxTokens',
                type: 'number',
                step: 1,
                default: 4096,
                optional: true,
                additionalParams: true
            },
            {
                label: 'Allow Image Uploads',
                name: 'allowImageUploads',
                type: 'boolean',
                description: '允许上传图片',
                default: false,
                optional: true
            }
        ]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const secretKey = (nodeData.inputs?.secretKey as string) || 'sk_UhwpWdOU61lkcN9Nm26gvG3L2MiYf5mi'
        const accessKey = (nodeData.inputs?.accessKey as string) || 'ak_b937d175558944e4b13be4c196477481'
        const temperature = nodeData.inputs?.temperature as string
        const maxTokens = nodeData.inputs?.maxTokens as string
        const streaming = nodeData.inputs?.streaming as boolean
        const source = nodeData.inputs?.source as string
        const modelName = nodeData.inputs?.modelName as string
        const basePath = (nodeData.inputs?.basePath as string) || 'http://ai-gateway.bilibili.co/v1'

        const obj: Partial<OpenAIChatInput> = {
            streaming: streaming ?? false,
            modelName: modelName
        }

        if (maxTokens) obj.maxTokens = parseInt(maxTokens, 10)
        if (temperature) obj.temperature = parseFloat(temperature)

        const model = new ChatAiGateway({
            ...obj,
            secretKey,
            accessKey,
            source,
            basePath
        })

        return model
    }
}

module.exports = { nodeClass: AiGateway_ChatModels }
