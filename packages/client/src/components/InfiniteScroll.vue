<template>
    <div class="infinite-scroll-container" ref="container">
        <!-- 渲染列表 -->
        <slot></slot>
        <!-- 占位符元素，用于触发 IntersectionObserver -->
        <div ref="loadMoreTrigger" class="load-more-trigger">
            <loading-outlined class="text-lg" v-if="!end" />
        </div>
    </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, toRefs } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'

export default {
    components: { LoadingOutlined },
    props: {
        end: Boolean
    },
    setup(props, { emit }) {
        const loadMoreTrigger = ref(null)

        const container = ref(null)

        const { end } = toRefs(props)

        const handleIntersect = (entries) => {
            if (entries[0].isIntersecting && !end.value) {
                emit('loadMoreFn')
            }
        }

        let observer
        onMounted(() => {
            observer = new IntersectionObserver(handleIntersect)
            if (loadMoreTrigger.value) {
                observer.observe(loadMoreTrigger.value)
            }
        })

        onBeforeUnmount(() => {
            if (observer && loadMoreTrigger.value) {
                observer.unobserve(loadMoreTrigger.value)
            }
        })

        return {
            loadMoreTrigger,
            container
        }
    }
}
</script>

<style>
.infinite-scroll-container {
    overflow: auto;
}

.load-more-trigger {
    margin-top: 20px;
    height: 20px;
    text-align: center;
}
</style>
