<template>
    <a-flex justify="space-between" align="center">
        <a-typography-title class="flex-1 !mb-0" :level="4">MCP 市场</a-typography-title>

        <a-input class="flex-1 border-none shadow-md leading-8" placeholder="搜索" v-model:value="keyword" @pressEnter="onSearch">
            <template #prefix>
                <MagnifyingGlassIcon class="w-4 h-4 text-[#383743]" />
            </template>
        </a-input>
        <div class="flex-1 text-right"></div>
    </a-flex>

    <a-typography-text type="secondary" class="mt-2 mb-5 text-center"> 所有公开的MCP都可在AI编排里使用 </a-typography-text>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface IProps {
    titile: string
    description: string
    searchKeyword: string
}

const props = defineProps<IProps>()

const keyword = ref(props.searchKeyword)

watch(
    () => props.searchKeyword,
    (v) => (keyword.value = v)
)

watch(keyword, (v) => {
    emit('update:searchKeyword', v)
})

const emit = defineEmits(['searchInputPressEnter', 'update:searchKeyword'])

const onSearch = () => {
    emit('searchInputPressEnter')
}
</script>
