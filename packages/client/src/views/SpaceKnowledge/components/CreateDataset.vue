<template>
    <a-modal v-model:open="state.open" :title="state.title" @ok="handle.onSubmit" destroy-on-close :after-close="handle.afterClose">
        <a-form :model="state.form" layout="vertical" autocomplete="off">
            <a-form-item label="名称" name="title" v-bind="validateInfos.title">
                <a-input v-model:value="state.form.title" placeholder="输入数据集名称" />
            </a-form-item>
            <a-form-item label="知识库服务" name="model">
                <a-select
                    v-model:value="state.form.model"
                    :disabled="!!state.form.id"
                    :options="[{ value: 2, label: 'AI开放平台' }]"
                ></a-select>
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-input v-model:value="state.form.description" placeholder="输入数据集描述" />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup lang="ts">
import { reactive, defineProps, defineEmits, defineExpose } from 'vue'

import { Form, message } from 'ant-design-vue'
import { updateDataset } from '@/api/space'

const props = defineProps(['spaceId'])
const emits = defineEmits(['callback'])
const useForm = Form.useForm

const state = reactive({
    open: false,
    form: {
        id: '',
        title: '',
        description: '',
        model: 2
    },
    title: '',
    rules: {
        title: [{ required: true, message: '请输入数据集名称!' }]
    }
})

const { validate, validateInfos, resetFields } = useForm(state.form, state.rules)

const handle = {
    open(data: any) {
        state.open = true

        if (data.id) {
            Object.keys(state.form).map((key: string) => {
                // @ts-ignore
                state.form[key] = data[key]
            })

            state.title = '编辑数据集'
        } else {
            state.title = '创建数据集'
        }
    },
    onSubmit() {
        validate()
            .then(async () => {
                const data = { ...state.form, spaceId: props.spaceId } as any

                if (data.id == '') {
                    delete data.id
                }

                await updateDataset(data)
                message.success(`${state.title}成功！`)

                emits('callback')
                state.open = false
            })
            .catch()
    },
    afterClose() {
        Object.assign(state.form, {
            id: '',
            title: '',
            description: '',
            model: 2
        })
        resetFields()
    }
}

defineExpose({ open: handle.open })
</script>
