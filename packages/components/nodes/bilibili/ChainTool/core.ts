import { DynamicStructuredTool, DynamicToolInput } from '@langchain/core/tools'
import { BaseChain } from 'langchain/chains'
import { handleEscapeCharacters } from '../../../src/utils'
import { z } from 'zod'

export interface ChainToolInput extends Omit<DynamicToolInput, 'func'> {
    chain: BaseChain
}

export class ChainTool extends DynamicStructuredTool {
    chain: BaseChain

    constructor({ chain, ...rest }: ChainToolInput) {
        super({
            ...rest,
            // required to be input schema
            schema: z.object({
                input: z.string()
            }),
            func: async ({ input }, runManager, config) => {
                // To enable LLM Chain which has promptValues
                if ((chain as any).prompt && (chain as any).prompt.promptValues) {
                    const promptValues = handleEscapeCharacters((chain as any).prompt.promptValues, true)
                    const values = await chain.invoke(promptValues, { ...runManager?.getChild(), callbacks: config?.callbacks })
                    return values?.text
                }

                return chain.invoke({ input }, { ...runManager?.getChild(), callbacks: config?.callbacks })
            }
        })
        this.chain = chain
    }
}
