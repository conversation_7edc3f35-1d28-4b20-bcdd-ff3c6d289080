<template>
    <a-breadcrumb class="mb-[25px]">
        <a-breadcrumb-item class="cursor-pointer" @click="goKnowledge">知识库</a-breadcrumb-item>
        <a-breadcrumb-item>{{ fileInfo.filename }}</a-breadcrumb-item>
    </a-breadcrumb>
    <a-skeleton :loading="loading">
        <a-divider />
        <div v-if="isTable">
            <div class="text-right"><a-button @click="onOpenAdd">新增切片</a-button></div>
            <a-table
                :dataSource="dataSource"
                :columns="columns"
                class="copilot-concise-table"
                :scroll="{
                    x: tableX,
                    y: 'calc(100vh - 290px)'
                }"
                :pagination="pagination"
                @change="changeTable"
                :loading="tableLoading"
            >
                <template #headerCell="{ column }">
                    <template v-if="column.isIndex">
                        <span>
                            {{ column.title }}
                            <a-tag :bordered="false" color="green">向量存储</a-tag>
                        </span>
                    </template>
                </template>
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'copilot-slice-table-operate'">
                        <a-button type="link" v-if="record.knowledge_sentence_id" @click="onTableEdit(record)">编辑</a-button>
                        <a-tooltip v-else placement="top" title="同步失败，sentenceId不能为空">
                            <a-button type="link" disabled>编辑</a-button>
                        </a-tooltip>
                    </template>
                </template>
            </a-table>
        </div>
        <div class="document-page" v-else>
            <div class="header">
                <div class="title">
                    <span>{{ fileInfo.filename }}</span>
                </div>
                <a-button @click="onOpenAdd">新增切片</a-button>
            </div>
            <a-divider />
            <div class="paragraph-container">
                <div v-for="(item, index) in sliceList" :key="index" class="p-[10px] paragraph-info" :id="item.id">
                    <div :class="[index % 2 ? 'hover:bg-purple-100' : 'hover:bg-rose-100']" class="p-3 rounded relative transition">
                        <div class="mb-2">
                            <a-tag :bordered="false">hit:{{ item.hit }}</a-tag>
                            <a-tag :bordered="false">id:{{ item.id }}</a-tag>
                            <a-tag :bordered="false">sentenceId:{{ item.sentenceId }}</a-tag>
                        </div>
                        <CherryMarkdown
                            :id="'markdown-container-' + item.id"
                            :content="item.content"
                            :cherryConfig="{
                                editor: {
                                    height: 'auto',
                                    defaultModel: 'previewOnly'
                                },
                                engine: {
                                    global: {
                                        // 开启流式模式 （默认 true）
                                        flowSessionContext: true
                                    },
                                    syntax: {
                                        codeBlock: {
                                            selfClosing: false
                                        },
                                        header: {
                                            anchorStyle: 'none'
                                        },
                                        table: {
                                            enableChart: false,
                                            selfClosing: false
                                        },
                                        fontEmphasis: {
                                            selfClosing: false
                                        }
                                    }
                                },
                                previewer: {
                                    enablePreviewerBubble: false
                                },
                                isPreviewOnly: true
                            }"
                        ></CherryMarkdown>
                        <a-space class="absolute top-1 right-1 operate-buttons opacity-0">
                            <div class="p-1 rounded-md bg-white shadow-lg cursor-pointer hover:bg-zinc-50" @click="onOpenEdit(item, index)">
                                <FormOutlined class="text-base text-[#1d1c2399]" />
                            </div>
                            <div class="p-1 rounded-md bg-white shadow-lg cursor-pointer hover:bg-zinc-50">
                                <DeleteOutlined class="text-base text-[#1d1c2399]" @click="onDelete(item.knowledgeId, item.id)" />
                            </div>
                        </a-space>
                    </div>
                </div>
            </div>
            <a-pagination
                class="text-right"
                v-model:current="pagination.current"
                v-model:page-size="pagination.pageSize"
                show-size-changer
                :total="pagination.total"
                @change="onGetSliceList"
            />
        </div>
        <TextEditModal
            v-model:visible="visible"
            v-if="visible"
            :contentInfo="contentInfo"
            :tableRecord="tableRecord"
            :tableMeta="fileInfo.tableMeta"
            :isTable="isTable"
            @updateSliceList="onGetSliceList"
        ></TextEditModal>
    </a-skeleton>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getKnowledgeSliceList, getKnowledgeDetail, sliceDelete, type IKnowledgeSliceList, type TableMeta } from '@/api/knowledge'
import { FormOutlined, DeleteOutlined } from '@ant-design/icons-vue'
// import { TrashIcon, PencilSquareIcon } from '@heroicons/vue/24/outline'
import TextEditModal from './components/TextEditModal.vue'
import { Modal, notification } from 'ant-design-vue'
import CherryMarkdown from './components/CherryMarkdown.vue'

const router = useRouter()
const route = useRoute()

const fileInfo = reactive({
    filename: '',
    extname: '',
    tableMeta: [] as TableMeta[]
})
const sliceList = ref<IKnowledgeSliceList[]>([])
const loading = ref(true)
const tableLoading = ref(false)

const tableExtname = ['.csv', '.xlsx', '.xls']

const columns = ref<Record<string, any>[]>([])
const dataSource = ref<Record<string, string | number>[]>([])

const tableX = computed(() => {
    return (columns.value.length + 3) * 160 + 'px'
})
const state = reactive({
    spaceId: '',
    datasetId: '',
    knowledgeId: ''
})

const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total: number) => `总${total}条 `
})

const visible = ref(false)
const isTable = ref(false)

const contentInfo = reactive({
    title: '',
    content: '',
    knowledgeSliceId: '',
    knowledgeId: ''
})
const tableRecord = reactive<Record<string, any>>({})

const defalutColumns = [
    {
        title: 'KSId',
        key: 'knowledge_ks_id',
        dataIndex: 'knowledge_ks_id',
        isIndex: false,
        width: 200,
        ellipsis: true
    },
    {
        title: 'sentenceId',
        key: 'knowledge_sentence_id',
        dataIndex: 'knowledge_sentence_id',
        isIndex: false,
        width: 160,
        ellipsis: true
    },
    {
        title: 'hit',
        key: 'knowledge_hit',
        dataIndex: 'knowledge_hit',
        isIndex: false,
        width: 80,
        ellipsis: true
    }
]

const goKnowledge = () => {
    router.push(`/space/${state.spaceId}/knowledge/${state.datasetId}`)
}

const onOpenAdd = () => {
    visible.value = true
    contentInfo.title = '新增切片'
    if (isTable.value) {
        Object.assign(tableRecord, dataSource.value[0])
        for (const key in tableRecord) {
            if (key !== 'knowledgeId') tableRecord[key] = ''
        }
    } else {
        contentInfo.content = ''
        contentInfo.knowledgeId = state.knowledgeId
        contentInfo.knowledgeSliceId = ''
    }
}

const onOpenEdit = (item: IKnowledgeSliceList, index: number) => {
    visible.value = true
    contentInfo.title = '#' + (index + 1)
    contentInfo.content = item.content
    contentInfo.knowledgeId = item.knowledgeId
    contentInfo.knowledgeSliceId = item.id
}

const onGetKnowledgeDetail = async () => {
    const { data } = await getKnowledgeDetail(String(state.datasetId), String(state.knowledgeId))
    Object.assign(fileInfo, data)
    isTable.value = tableExtname.includes(fileInfo.extname)
    if (isTable.value) {
        columns.value = fileInfo.tableMeta.map((item: TableMeta) => {
            return {
                title: item.columnName,
                key: item.columnName,
                dataIndex: item.columnName,
                isIndex: item.isIndex,
                width: 160,
                ellipsis: true
            }
        })
        columns.value = [
            ...defalutColumns,
            ...columns.value,
            {
                title: '操作',
                key: 'copilot-slice-table-operate',
                width: 160,
                fixed: 'right'
            }
        ]
    }
}
const onTableEdit = (record: Record<string, any>) => {
    visible.value = true
    contentInfo.title = '编辑切片'
    Object.assign(tableRecord, record)
}
const onGetSliceList = async () => {
    tableLoading.value = true
    const { data } = await getKnowledgeSliceList(
        String(state.datasetId),
        String(state.knowledgeId),
        pagination.current,
        pagination.pageSize
    )
    pagination.total = data.count
    if (isTable.value) {
        dataSource.value = data.rows.map((item) => {
            return {
                knowledge_ks_id: item.id,
                knowledge_sentence_id: item.sentenceId,
                knowledge_hit: item.hit,
                knowledgeId: item.knowledgeId,
                ...JSON.parse(item.content)
            }
        })
    } else {
        sliceList.value = [...data.rows]
    }
    tableLoading.value = false
}

const changeTable = ({ current, pageSize }: any) => {
    pagination.current = current
    pagination.pageSize = pageSize
    onGetSliceList()
}

const onDelete = (knowledgeId: string, knowledgeSliceId: string) => {
    Modal.confirm({
        title: `警告`,
        content: '确认要删除当前切片吗？',
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await sliceDelete({ knowledgeId, knowledgeSliceId: [knowledgeSliceId] })
            notification.success({
                message: '成功',
                description: '切片删除成功'
            })
            onGetSliceList()
        }
    })
}

onMounted(async () => {
    const { spaceId, datasetId, knowledgeId } = route.params
    Object.assign(state, {
        spaceId,
        datasetId,
        knowledgeId
    })

    loading.value = true
    if (!isTable.value) pagination.pageSize = 100
    await onGetKnowledgeDetail()
    await onGetSliceList()
    loading.value = false

    if (!isTable.value) {
        nextTick(() => {
            const id = route.query.id || sliceList.value[0].id
            id && document.getElementById(String(id))?.scrollIntoView({ behavior: 'instant' })
        })
    }
})
</script>

<style scoped lang="less">
.document-page {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 600;
}

.stats {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #999;
}

.file-selector {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.paragraph-container {
    padding: 0 100px;
    height: calc(100vh - 280px);
    overflow: auto;
    .paragraph-info:hover {
        .operate-buttons {
            opacity: 1;
        }
    }
}
</style>
