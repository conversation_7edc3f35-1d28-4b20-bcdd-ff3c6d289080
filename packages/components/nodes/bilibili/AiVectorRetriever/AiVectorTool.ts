import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { z } from 'zod'
import { BaseDynamicToolInput, DynamicTool, StructuredTool, ToolInputParsingException } from '@langchain/core/tools'
import { RunnableConfig } from '@langchain/core/runnables'
import { CallbackManager, CallbackManagerForToolRun, Callbacks, parseCallbackConfigArg } from '@langchain/core/callbacks/manager'
import { getBaseClasses } from '../../../src'
import { SOURCE_DOCUMENTS_PREFIX } from '../../../src/agents'
import { vectorQuery } from './api'

type ZodObjectAny = z.ZodObject<any, any, any, any>
type IFlowConfig = { sessionId?: string; chatId?: string; input?: string; state?: ICommonObject }
interface DynamicStructuredToolInput<T extends z.ZodObject<any, any, any, any> = z.ZodObject<any, any, any, any>>
    extends BaseDynamicToolInput {
    func?: (input: z.infer<T>, runManager?: CallbackManagerForToolRun, flowConfig?: IFlowConfig) => Promise<string>
    schema: T
}

class AiVectorRetriever_Tools implements INode {
    label: string
    name: string
    version: number
    description: string
    type: string
    icon: string
    category: string
    baseClasses: string[]
    tags: string[]
    inputs: INodeParams[]

    constructor() {
        this.label = 'AiVectorRetriever'
        this.name = 'aiVectorRetriever'
        this.version = 1.0
        this.type = 'CustomTool'
        this.icon = 'ai.png'
        this.category = 'Tools'
        this.description = '知识库检索工具，返回文档切片内容'
        this.baseClasses = [this.type, 'DynamicTool', ...getBaseClasses(DynamicTool)]
        this.tags = ['bilibili']
        this.inputs = [
            {
                label: 'Name',
                name: 'name',
                type: 'string',
                default: 'AiVectorRetriever'
            },
            {
                label: 'DatasetId',
                name: 'datasetId',
                type: 'string',
                description: '请填入知识库列表页中的数据集id'
            },
            {
                label: 'Top N',
                name: 'topN',
                type: 'number',
                default: 5,
                description: '查询的相似性匹配数量'
            },
            {
                label: 'Description',
                name: 'description',
                type: 'string',
                description: '填写知识库包含哪些相关文档，以便更好的匹配',
                rows: 4
            },
            {
                label: 'Return Source Documents',
                name: 'returnSourceDocuments',
                type: 'boolean',
                optional: true,
                description: '是否展示召回的文档内容',
                additionalParams: true
            },
            {
                label: '最小相似度',
                name: 'distance',
                type: 'number',
                description: '最小值0，大于设定值的内容将不会被调用',
                step: 0.1,
                default: 0.6,
                optional: true,
                additionalParams: true
            }
        ]
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const name = nodeData.inputs?.name as string
        const description = nodeData.inputs?.description as string
        const datasetId = nodeData.inputs?.datasetId as string
        const topN = nodeData.inputs?.topN as number
        const distance = (nodeData.inputs?.distance as number) || 0.6
        const returnSourceDocuments = nodeData.inputs?.returnSourceDocuments as boolean

        const input = {
            name,
            description
        }

        const flow = { chatflowId: options.chatflowid }

        const func = async ({ input }: { input: string }, _?: CallbackManagerForToolRun, flowConfig?: IFlowConfig) => {
            if (!input) {
                return ''
            }

            const body = {
                query: input,
                topN,
                datasetId,
                distance
            }

            const res = await vectorQuery(body)
            const docs = res.map((item) => {
                return {
                    pageContent: item.content,
                    metadata: {
                        slice: {
                            filename: item.filename,
                            distance: item.distance,
                            sliceId: item.sliceId,
                            knowledgeId: item.knowledgeId,
                            location: item.location
                        }
                    }
                }
            })

            const content = docs.map((doc) => doc.pageContent).join('\n\n')
            const sourceDocuments = JSON.stringify(docs)
            return returnSourceDocuments ? content + SOURCE_DOCUMENTS_PREFIX + sourceDocuments : content
        }

        const schema = z.object({
            input: z.string().describe('input to look up in retriever')
        }) as any

        const tool = new DynamicStructuredTool({ ...input, func, schema })
        tool.setFlowObject(flow)
        return tool
    }
}

class DynamicStructuredTool<T extends z.ZodObject<any, any, any, any> = z.ZodObject<any, any, any, any>> extends StructuredTool<
    T extends ZodObjectAny ? T : ZodObjectAny
> {
    static lc_name() {
        return 'DynamicStructuredTool'
    }

    name: string

    description: string

    func: DynamicStructuredToolInput['func']

    // @ts-ignore
    schema: T

    private flowObj: any

    constructor(fields: DynamicStructuredToolInput<T>) {
        super(fields)
        this.name = fields.name
        this.description = fields.description
        this.func = fields.func
        this.returnDirect = fields.returnDirect ?? this.returnDirect
        this.schema = fields.schema
    }

    async call(arg: any, configArg?: RunnableConfig | Callbacks, tags?: string[], flowConfig?: IFlowConfig): Promise<string> {
        const config = parseCallbackConfigArg(configArg)
        if (config.runName === undefined) {
            config.runName = this.name
        }
        let parsed
        try {
            parsed = await this.schema.parseAsync(arg)
        } catch (e) {
            throw new ToolInputParsingException(`Received tool input did not match expected schema`, JSON.stringify(arg))
        }
        const callbackManager_ = await CallbackManager.configure(
            config.callbacks,
            this.callbacks,
            config.tags || tags,
            this.tags,
            config.metadata,
            this.metadata,
            { verbose: this.verbose }
        )
        const runManager = await callbackManager_?.handleToolStart(
            this.toJSON(),
            typeof parsed === 'string' ? parsed : JSON.stringify(parsed),
            undefined,
            undefined,
            undefined,
            undefined,
            config.runName
        )
        let result
        try {
            result = await this._call(parsed, runManager, flowConfig)
        } catch (e) {
            await runManager?.handleToolError(e)
            throw e
        }
        if (result && typeof result !== 'string') {
            result = JSON.stringify(result)
        }
        await runManager?.handleToolEnd(result)
        return result
    }

    // @ts-ignore
    protected _call(arg: any, runManager?: CallbackManagerForToolRun, flowConfig?: IFlowConfig): Promise<string> {
        let flowConfiguration: ICommonObject = {}
        if (typeof arg === 'object' && Object.keys(arg).length) {
            for (const item in arg) {
                flowConfiguration[`$${item}`] = arg[item]
            }
        }

        // inject flow properties
        if (this.flowObj) {
            flowConfiguration['$flow'] = { ...this.flowObj, ...flowConfig }
        }

        return this.func!(arg as any, runManager, flowConfiguration)
    }

    setFlowObject(flow: any) {
        this.flowObj = flow
    }
}

module.exports = { nodeClass: AiVectorRetriever_Tools }
