<template>
    <a-modal title="Function Demo" width="700px" v-model:open="state.open" @ok="onSubmit">
        <div>
            <a-button class="mb-2" size="small" @click="handleCopy"> 复制代码 </a-button>
            <pre class="code-block flex flex-col text-[12px]"><code>{{code}}</code></pre>
        </div>
        <template #footer>
            <a-button class="mr-1" @click="handleCopy">复制</a-button>
            <a-button type="primary" @click="onSubmit">关闭</a-button>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import { defineExpose, reactive } from 'vue'
import { message } from 'ant-design-vue'
import useClipboard from 'vue-clipboard3'

const code =
    '// 将以下json复制到右侧mock参数内\n' +
    '`\n' +
    '{\n' +
    '  "input": "用户输入的问题",\n' +
    '  "schema": {\n' +
    '    "username": "guwenjia"\n' +
    '  },\n' +
    '  "env": {\n' +
    '    "hello": "world"\n' +
    '  }\n' +
    '}\n' +
    '`\n' +
    '\n' +
    '// 使用input可获取当前用户输入的内容，但MCP协议下不提供，请根据当前云函数的用途判断是否需要\n' +
    'console.log($flow.input)\n' +
    '\n' +
    '// env参数是方便在MCP协议下，可传入更多自定义参数，在普通工作流中没有此参数\n' +
    '// 在MCP URL中传入，如 http://...?sign=...&username=...&hello=world\n' +
    'console.log($flow.env.hello)\n' +
    '\n' +
    '// 以下演示如何基于服务端接口进行数据查询\n' +
    '// 使用yuumi,基于discoveryID进行接口通讯,支持grpc/rpc/http协议\n' +
    'const res = await $yuumi.http({\n' +
    "    appId: 'live.fe.mlive-node-base',\n" +
    "    method: 'GET',\n" +
    "    path: '/oa/users/search',\n" +
    '    params: {\n' +
    '        username: $username // ToolCall协议中的自定义参数，在Tool arguments配置 \n' +
    '    },\n' +
    '    cookie: $cookie\n' +
    '})\n' +
    '\n' +
    'if(res.code !== 0) {\n' +
    '    // 使用自然语言风格返回错误内容\n' +
    "    return '用户查找失败'\n" +
    '}\n' +
    '\n' +
    'const user = res.data.map(u => ({\n' +
    "    '部门': u.department_name,\n" +
    "    '昵称': u.nick_name,\n" +
    "    '职务': u.job_title,\n" +
    "    '用户名': u.login_id\n" +
    '}))\n' +
    '\n' +
    'console.log($json2MarkdownTable(user))\n' +
    '\n' +
    '// 注意:结果必须以字符串格式return\n' +
    'return $json2MarkdownTable(user)'

const { toClipboard } = useClipboard()

const state = reactive({
    open: false
})

const open = () => {
    state.open = true
}

const onSubmit = () => {
    state.open = false
}

const handleCopy = async () => {
    await toClipboard(code)
    message.success('已复制')
}

defineExpose({ open })
</script>
<style scoped>
.code-block {
    background-color: #f5f5f5;
    color: #333333;
    padding: 16px;
    line-height: 1.5;
    white-space: pre-wrap;
    overflow-x: auto;
    border-radius: 4px;
}
</style>
