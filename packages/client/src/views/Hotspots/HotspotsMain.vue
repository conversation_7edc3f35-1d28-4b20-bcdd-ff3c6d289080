<template>
    <div class="container mx-auto px-4 py-20 overflow-auto">
        <div class="flex flex-col items-center mb-8">
            <a-typography-title>热点洞察</a-typography-title>
            <a-typography-text class="mb-8 text-gray-500"
                >「热点洞察」由AI驱动，实时挖掘全网热点，智能生成选题方案与风险预警，构建“挖掘-分析-推荐”全流程闭环，全面提升热点采纳率与内容增长效率。</a-typography-text
            >
            <div>
                <a-button type="primary" @click="goToSubscribe" disabled>订阅热点</a-button>
                <a-button class="ml-4" disabled>我的热点</a-button>
            </div>
        </div>
        <div>
            <div class="flex justify-between mb-4">
                <div>
                    <span class="font-bold text-xl mr-4">热点榜</span>
                    <span class="text-gray-500">更新于{{ updateTime }}</span>
                </div>
                <!-- 自定义分页 -->
                <div class="flex justify-center">
                    <div
                        v-for="(p, idx) in platforms"
                        :key="p.value"
                        class="mx-2 cursor-pointer"
                        :class="activeIndex === idx ? 'text-blue-600 font-bold border-b-2 border-blue-600' : 'text-gray-500'"
                        @click="goTo(idx)"
                    >
                        {{ p.label }}
                    </div>
                </div>
            </div>
            <a-carousel arrows :dots="false" :slides-to-show="2" ref="carouselRef" :after-change="onAfterChange">
                <template #prevArrow>
                    <div class="custom-slick-arrow custom-slick-arrow-left">
                        <LeftOutlined />
                    </div>
                </template>
                <template #nextArrow>
                    <div class="custom-slick-arrow custom-slick-arrow-right">
                        <RightOutlined />
                    </div>
                </template>
                <div v-for="p in platforms" :key="p.value">
                    <a-card class="mr-2 h-[520px]">
                        <p class="mb-4">
                            <span>{{ p.label }}</span>
                            <span v-if="p.label === 'B站内网'" class="text-gray-500">（根据不同实验组，取最高排名，数据T+2h）</span>
                        </p>
                        <a-table
                            :data-source="hotspotsRank[platforms.indexOf(p)]"
                            :columns="columns"
                            size="small"
                            :pagination="false"
                            :scroll="{ y: 390 }"
                        >
                        </a-table>
                        <span class="text-sm text-gray-500" v-if="hotspotsRank[platforms.indexOf(p)].length > 10">向下滚动查看更多</span>
                    </a-card>
                </div>
            </a-carousel>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import type { HotspotsRank } from '@/interface/hotspots'
import { getHotspotsList } from '@/api/hot'
import router from '@/router'

const updateTime = ref(new Date().toLocaleString())
const platforms = [
    { value: 'bilibili', label: 'B站' },
    { value: 'bilibili-bi', label: 'B站内网' },
    { value: 'rednote', label: '小红书' },
    { value: 'weibo', label: '微博' },
    { value: 'douyin', label: '抖音' },
    { value: 'netease-news', label: '网易新闻' },
    { value: 'tencent-news', label: '腾讯新闻' },
    { value: 'toutiao', label: '今日头条' },
    { value: 'zhihu', label: '知乎' },
    { value: 'douban', label: '豆瓣' }
]
const carouselRef = ref()
const activeIndex = ref(0)
const hotspotsRank = ref<HotspotsRank[][]>(Array(platforms.length).fill([]))

const columns = [
    { title: '排名', dataIndex: 'rank', key: 'rank', width: 60 },
    {
        title: '热点',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        ellipsis: true,
        customRender: ({ record }: { record: HotspotsRank }) =>
            h(
                'a',
                {
                    href: record.url,
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    style: 'color: #1677ff; textDecoration: "underline"'
                },
                record.title
            )
    },
    {
        title: '热度',
        dataIndex: 'hot',
        key: 'hot',
        width: 100,
        customRender: ({ record }: { record: HotspotsRank }) => (record.hot === 0 ? '-' : record.hot)
    }
]

function goTo(idx: number) {
    activeIndex.value = idx
    // 调用 carousel 的 goTo 方法
    carouselRef.value?.goTo(idx)
}

function onAfterChange(current: number) {
    activeIndex.value = current
}

// 获取热点榜数据
platforms.forEach(async (platform, index) => {
    try {
        const res = await getHotspotsList({ platform: platform.value })
        hotspotsRank.value[index] = res.data
        updateTime.value = res.data[0]?.ctime || new Date().toLocaleString() // 更新最新时间
    } catch (error) {
        console.error(`获取 ${platform} 热点榜失败:`, error)
        hotspotsRank.value[index] = [] // 如果获取失败，设置为空数组
    }
})

function goToSubscribe() {
    // 跳转到订阅页面
    router.push({ name: 'hotspots-subscribe' })
}
</script>

<style scoped>
:deep(.slick-arrow.custom-slick-arrow) {
    width: 20px;
    height: 20px;
    font-size: 20px;
    color: #000;
    transition: ease all 0.3s;
    opacity: 0.3;
    z-index: 1;
    top: 50%;
}
:deep(.custom-slick-arrow-left) {
    left: 10px;
}
:deep(.custom-slick-arrow-right) {
    right: 10px;
}
:deep(.slick-arrow.custom-slick-arrow:before) {
    display: none;
}
:deep(.slick-arrow.custom-slick-arrow:hover) {
    color: #000;
    opacity: 0.5;
}
</style>
