<template>
    <a-modal
        v-model:open="open"
        title="Bot管理"
        :width="750"
        class="min-h-[300px]"
        :destroy-on-close="true"
        :mask-closable="false"
        :after-close="afterClose"
    >
        <a-form-item class="w-[300px] mt-[20px] mb-[10px]" label="工作空间">
            <a-select v-model:value="curSpace" :options="spaceOptions" @change="handleChange"> </a-select>
        </a-form-item>
        <div class="overflow-x-hidden h-[400px] overflow-y-auto mb-[20px] w-[600px] m-auto">
            <a-card v-for="item in flowData" :key="item.id" class="mb-[10px]">
                <a-flex justify="space-between" align="center">
                    <div class="w-[70%]">
                        {{ item.name }} <a-typography-text type="secondary">@{{ item.creator }}</a-typography-text>
                        <br />
                        <a-typography-paragraph
                            :ellipsis="{ row: 1 }"
                            :content="item.description || '暂无描述'"
                            class="!mb-0"
                            type="secondary"
                        >
                        </a-typography-paragraph>
                    </div>
                    <a-button
                        type="primary"
                        :danger="item.isPublic"
                        size="small"
                        :disabled="!isSelf(item.creator)"
                        @click="handlePublic(item)"
                    >
                        {{ item.isPublic ? '下架' : '上架' }}
                    </a-button>
                </a-flex>
            </a-card>
        </div>

        <template #footer>
            <a-button @click="open = false">关闭</a-button>
        </template>
    </a-modal>
</template>
<script lang="ts" setup>
import { computed, ref, defineEmits, defineExpose } from 'vue'
import { useSpaceStore } from '@/stores/space'
import { getChatFlowList, updateChatFlowInfo } from '@/api/space'
import { useUserStore } from '@/stores/user'
import { message, Modal } from 'ant-design-vue'
const { isSelf } = useUserStore()

const spaceStore = useSpaceStore()

const curSpace = ref('')

const flowData = ref<any[]>([])

const emits = defineEmits(['callback'])

const open = ref<boolean>(false)

const spaceOptions = computed(() => {
    return spaceStore.allSpaces.map((item) => ({ label: item.title, value: item.id }))
})

const showModal = () => {
    curSpace.value = spaceStore.personalSpace.id
    open.value = true

    handleChange(curSpace.value)
}

const handlePublic = async (data: any) => {
    Modal.confirm({
        title: `警告`,
        content: `确认要上架 ${data.name} `,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
            await updateChatFlowInfo({ id: data.id, isPublic: !data.isPublic })
            message.success(`${data.name} 上架成功`)
            await handleChange(curSpace.value)
        }
    })
}

const handleChange = async (id: string) => {
    flowData.value = (await getChatFlowList(id)).data
}

const afterClose = () => {
    emits('callback')
}

defineExpose({ open: showModal })
</script>
