import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses } from '../../../src/utils'
import { ChatBiliIndex } from './core'

class Bilibili_ChatModels implements INode {
    label: string
    name: string
    version: number
    type: string
    icon: string
    category: string
    description: string
    baseClasses: string[]
    tags: string[]
    credential: INodeParams
    inputs: INodeParams[]

    constructor() {
        this.label = 'Bili-index-2.0'
        this.name = 'bilibili'
        this.version = 3.0
        this.type = 'Bilibili'
        this.icon = 'bilibili.png'
        this.category = 'Chat Models'
        this.description = 'bilibili自研大模型index2.0'
        this.baseClasses = [this.type, ...getBaseClasses(ChatBiliIndex)]
        this.tags = ['bilibili']
        this.inputs = []
    }

    async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
        const model = 'index-2.0'
        const apikey = '2f6b4cc3ddfe31774a2e8a79661f425a'
        const source = 'bili'

        const biliIndex = new ChatBiliIndex({
            model,
            apikey,
            source,
            maxRetries: 1,
            maxConcurrency: 2
        })
        return biliIndex
    }
}

module.exports = { nodeClass: Bilibili_ChatModels }
